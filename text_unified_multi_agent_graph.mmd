---
config:
  flowchart:
    curve: linear
---
graph TD;
	__start__([<p>__start__</p>]):::first
	message_cleaner(message_cleaner)
	global_context_summarization(global_context_summarization)
	mode_switcher(mode_switcher)
	intent_analyzer(intent_analyzer)
	text_simple_chat(text_simple_chat)
	text_knowledge_agent(text_knowledge_agent)
	text_mcp_agent(text_mcp_agent)
	text_multimodal_agent(text_multimodal_agent)
	text_custom_rag_agent(text_custom_rag_agent)
	text_supervisor(text_supervisor)
	text_parallel_coordinator(text_parallel_coordinator)
	__end__([<p>__end__</p>]):::last
	__start__ --> message_cleaner;
	global_context_summarization --> mode_switcher;
	intent_analyzer -.-> text_custom_rag_agent;
	intent_analyzer -.-> text_knowledge_agent;
	intent_analyzer -.-> text_mcp_agent;
	intent_analyzer -.-> text_multimodal_agent;
	intent_analyzer -.-> text_parallel_coordinator;
	intent_analyzer -.-> text_simple_chat;
	intent_analyzer -.-> text_supervisor;
	message_cleaner --> global_context_summarization;
	mode_switcher -.-> intent_analyzer;
	mode_switcher -. &nbsp;fallback&nbsp; .-> text_simple_chat;
	text_custom_rag_agent --> __end__;
	text_knowledge_agent --> __end__;
	text_mcp_agent --> __end__;
	text_multimodal_agent --> __end__;
	text_parallel_coordinator --> __end__;
	text_simple_chat --> __end__;
	text_supervisor --> __end__;
	classDef default fill:#f2f0ff,line-height:1.2
	classDef first fill-opacity:0
	classDef last fill:#bfb6fc
