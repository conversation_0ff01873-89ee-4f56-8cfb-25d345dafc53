---
config:
  flowchart:
    curve: linear
---
graph TD;
	__start__([<p>__start__</p>]):::first
	initialize_workflow(initialize_workflow)
	extract_pdf_pages(extract_pdf_pages)
	dispatch_page_analysis(dispatch_page_analysis)
	analyze_single_page(analyze_single_page)
	dispatch_translation(dispatch_translation)
	translate_page_content(translate_page_content)
	finalize_results(finalize_results)
	generate_translated_pdf(generate_translated_pdf)
	__end__([<p>__end__</p>]):::last
	__start__ --> initialize_workflow;
	analyze_single_page -.-> dispatch_translation;
	dispatch_page_analysis -.-> analyze_single_page;
	dispatch_translation -.-> translate_page_content;
	extract_pdf_pages -.-> dispatch_page_analysis;
	finalize_results -.-> __end__;
	finalize_results -.-> generate_translated_pdf;
	generate_translated_pdf -.-> __end__;
	initialize_workflow -.-> extract_pdf_pages;
	translate_page_content -.-> finalize_results;
	classDef default fill:#f2f0ff,line-height:1.2
	classDef first fill-opacity:0
	classDef last fill:#bfb6fc
