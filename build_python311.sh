#!/bin/bash

echo "🐱 拯救小猫：构建Python 3.11版本的Docker镜像..."
echo "包含179个Python包，支持LangGraph原生astream功能"
echo "=========================================="

# 检查requirements文件是否存在
if [ ! -f "current_requirements.txt" ]; then
    echo "❌ 错误：current_requirements.txt 文件不存在"
    echo "请先运行：docker exec sx-jiliang-chat-multi-agent pip freeze > current_requirements.txt"
    exit 1
fi

echo "✅ 发现依赖文件：$(wc -l < current_requirements.txt) 个Python包"

# 构建新镜像
echo "🔨 开始构建 rag_chat:v1.1.11-python311..."
docker build -f Dockerfile.python311 -t rag_chat:v1.1.11-python311 .

if [ $? -eq 0 ]; then
    echo "🎉 构建成功！"
    echo "新镜像：rag_chat:v1.1.11-python311"
    echo ""
    echo "验证Python版本："
    docker run --rm rag_chat:v1.1.11-python311 python --version
    echo ""
    echo "下一步：更新docker-compose.yml中的镜像名称"
    echo "将 'image: rag_chat:v1.1.10' 改为 'image: rag_chat:v1.1.11-python311'"
else
    echo "❌ 构建失败，请检查错误信息"
    exit 1
fi