#!/usr/bin/env python3
"""
吉量聊天重构版API启动脚本
启动jiliang_chat_refactored.py中的API服务
"""

import os
import sys
import uvicorn
from pathlib import Path
from fastapi import FastAPI
from fastapi.middleware.cors import CORSMiddleware

# 设置项目路径
project_root = Path(__file__).parent
rag_chat_path = project_root / "rag_chat"
sys.path.insert(0, str(rag_chat_path))

# 导入聊天API路由
from jiliang_chat_refactored import router as chat_router
from logger import logger

def create_app():
    """创建FastAPI应用"""
    
    app = FastAPI(
        title="吉量聊天重构版API",
        description="基于LangGraph的聊天服务",
        version="3.0.0",
        docs_url="/docs",
        redoc_url="/redoc"
    )
    
    # 添加CORS支持
    app.add_middleware(
        CORSMiddleware,
        allow_origins=["*"],
        allow_credentials=True,
        allow_methods=["*"],
        allow_headers=["*"],
    )
    
    # 注册聊天路由
    app.include_router(chat_router, prefix="", tags=["聊天"])
    
    @app.get("/health")
    async def health_check():
        """健康检查"""
        return {"status": "healthy", "service": "jiliang_chat_refactored"}
    
    return app

# 创建全局app实例用于gunicorn
app = create_app()

def main():
    """主函数"""
    
    # 检查环境变量
    required_vars = ["OPENAI_API_KEY", "OPENAI_BASE_URL", "OPENAI_MODEL"]
    missing_vars = [var for var in required_vars if not os.getenv(var)]
    
    if missing_vars:
        print(f"警告: 缺少环境变量 {missing_vars}")
        print("请确保在.env文件中配置了相关的AI服务配置")
    
    # 检查必要的目录
    temp_dir = os.getenv('TEMP_DIR', os.path.join(os.path.expanduser("~"), "temp"))
    if not os.path.exists(temp_dir):
        os.makedirs(temp_dir, exist_ok=True)
        print(f"创建临时目录: {temp_dir}")
    
    # 使用gunicorn启动生产环境，性能更好
    import subprocess
    import sys
    import multiprocessing
    
    # 计算最优worker数量（平衡内存需求和CPU资源利用）
    # 集群环境下CPU充足(8-16%使用率)，可适当增加worker数量
    worker_count = min(multiprocessing.cpu_count() + 1, 5)
    
    # gunicorn生产环境配置
    gunicorn_cmd = [
        sys.executable, "-m", "gunicorn",
        "start_jiliang_chat_refactored:app",
        "--workers", str(worker_count),
        "--worker-class", "uvicorn.workers.UvicornWorker",
        "--bind", "0.0.0.0:18810",
        "--timeout", "300",                  # 多智能体处理时间长
        "--keep-alive", "5",                 # 保持连接降低开销
        "--max-requests", "500",             # 防止内存泄漏
        "--max-requests-jitter", "50",       # 避免同时重启
        "--worker-tmp-dir", "/dev/shm",      # 使用共享内存
        "--worker-connections", "1000",      # 每个worker连接数
        "--log-level", "info",
        "--access-logfile", "-",             # 访问日志到stdout
        "--error-logfile", "-"               # 错误日志到stderr
    ]
    
    try:
        subprocess.run(gunicorn_cmd)
    except KeyboardInterrupt:
        logger.info("服务被用户中断")
    except FileNotFoundError:
        # 如果没有gunicorn，回退到uvicorn
        logger.warning("未找到gunicorn，使用uvicorn启动")
        uvicorn.run(
            "start_jiliang_chat_refactored:app",
            host='0.0.0.0',
            port=18810,
            log_level="info"
        )
    
if __name__ == "__main__":
    main()