aio-pika==9.5.5
aiofiles==23.2.1
aiohappyeyeballs==2.5.0
aiohttp==3.12.14
aiormq==6.8.1
aiosignal==1.4.0
aiosqlite==0.20.0
annotated-types==0.7.0
anthropic==0.57.1
anyio==4.8.0
async-timeout==4.0.3
attrs==25.1.0
azure-ai-documentintelligence==1.0.2
azure-core==1.35.0
azure-identity==1.23.1
backoff==2.2.1
beautifulsoup4==4.13.4
certifi==2025.1.31
cffi==1.17.1
charset-normalizer==3.4.1
click==8.1.8
cobble==0.1.4
coloredlogs==15.0.1
cryptography==44.0.2
dataclasses-json==0.6.7
defusedxml==0.7.1
distro==1.9.0
dmPython==2.5.8
dnspython==2.7.0
docx2pdf==0.1.8
docx2txt==0.8
dotenv==0.9.9
dydantic==0.0.8
et_xmlfile==2.0.0
exceptiongroup==1.2.2
fastapi==0.115.11
filetype==1.2.0
flatbuffers==25.2.10
frozenlist==1.5.0
gevent==25.5.1
googleapis-common-protos==1.70.0
greenlet==3.2.2
grpcio==1.67.1
grpcio-tools==1.67.1
h11==0.14.0
h2==4.2.0
hpack==4.1.0
httpcore==1.0.7
httpx==0.28.1
httpx-sse==0.4.1
humanfriendly==10.0
hyperframe==6.1.0
idna==3.10
importlib_metadata==8.7.0
isodate==0.7.2
jiter==0.8.2
jsonpatch==1.33
jsonpointer==3.0.0
jsonschema==4.25.0
jsonschema-specifications==2025.4.1
langchain==0.3.27
langchain-anthropic==0.3.17
langchain-community==0.3.27
langchain-core==0.3.72
langchain-deepseek==0.1.4
langchain-mcp-adapters==0.1.9
langchain-milvus==0.2.1
langchain-openai==0.3.28
langchain-text-splitters==0.3.9
langfuse==3.2.1
langgraph==0.6.0
langgraph-checkpoint==2.1.1
langgraph-checkpoint-sqlite==2.0.5
langgraph-prebuilt==0.6.0
langgraph-sdk==0.2.0
langgraph-supervisor==0.0.27
langgraph-swarm==0.0.13
langmem==0.0.27
langsmith==0.3.45
loguru==0.7.3
lxml==5.3.1
magika==0.6.2
mammoth==1.9.1
markdownify==1.1.0
markitdown==0.1.2
marshmallow==3.26.1
mcp==1.12.2
mem0ai @ file:///app/mem0ai-0.1.113-py3-none-any.whl
milvus-lite==2.5.0
mpmath==1.3.0
msal==1.33.0
msal-extensions==1.3.1
multidict==6.1.0
mypy_extensions==1.1.0
nest-asyncio==1.6.0
numpy>=1.23.5,<2.0.0
olefile==0.47
onnxruntime==1.22.1
openai==1.97.1
opencv-python==4.11.0.86
openparse==0.7.0
openpyxl==3.1.5
opentelemetry-api==1.35.0
opentelemetry-exporter-otlp==1.35.0
opentelemetry-exporter-otlp-proto-common==1.35.0
opentelemetry-exporter-otlp-proto-grpc==1.35.0
opentelemetry-exporter-otlp-proto-http==1.35.0
opentelemetry-proto==1.35.0
opentelemetry-sdk==1.35.0
opentelemetry-semantic-conventions==0.56b0
orjson==3.10.18
ormsgpack==1.10.0
packaging==24.2
pamqp==3.3.0
pandas==2.3.0
pdfminer.six==20240706
pillow==11.1.0
portalocker==2.10.1
posthog==5.4.0
propcache==0.3.0
protobuf==5.29.5
pycparser==2.22
pydantic==2.10.6
pydantic-settings==2.10.1
pydantic_core==2.27.2
pydub==0.25.1
PyJWT==2.10.1
pymilvus==2.5.11
pymongo==4.11.2
PyMuPDF==1.25.3
pypdf==5.3.1
python-dateutil==2.9.0.post0
python-docx==1.1.2
python-dotenv==1.1.1
python-multipart==0.0.20
python-pptx==1.0.2
pytz==2025.2
PyYAML==6.0.2
qdrant-client==1.14.3
redis>=5.0,<7.0
# redis-py-cluster==2.1.3  # Replaced by native cluster support in redis>=4.1.0
referencing==0.36.2
regex==2024.11.6
requests==2.32.3
requests-toolbelt==1.0.0
rpds-py==0.26.0
shapely==2.1.0
silk-python==0.2.6
six==1.17.0
sniffio==1.3.1
soupsieve==2.7
SpeechRecognition==3.14.3
SQLAlchemy==2.0.41
sse-starlette==3.0.2
starlette==0.46.0
sympy==1.14.0
tenacity==9.1.2
tiktoken==0.9.0
tqdm==4.67.1
trustcall==0.0.39
typing-inspect==0.9.0
typing-inspection==0.4.1
typing_extensions==4.14.1
tzdata==2025.2
ujson==5.10.0
urllib3==2.3.0
uvicorn==0.34.0
websocket==0.2.1
websockets==15.0.1
wrapt==1.17.2
xlrd==2.0.2
xlsxwriter==3.2.5
xxhash==3.5.0
yarl==1.18.3
youtube-transcript-api==1.0.3
zipp==3.23.0
zope.event==5.0
zope.interface==7.2
zstandard==0.23.0
langgraph-checkpoint-redis==0.0.8