"""
调试文本解析功能
"""

import sys
import os
from pathlib import Path

# 添加项目根目录到路径
project_root = Path(__file__).parent
sys.path.insert(0, str(project_root))

from rag_chat.multi_agent.text_parser import text_parser

def test_tool_call_parsing():
    """测试工具调用解析"""
    
    # 测试新格式
    test_text1 = '''
[TOOL_CALL]
{
    "name": "search_knowledge_base",
    "args": {
        "query": "三峡集团 发展历史",
        "max_results": "5"
    },
    "reasoning": "需要检索三峡集团官方知识库中关于企业成立时间、组织架构演变、重大工程项目里程碑等历史信息，为用户提供系统的发展脉络"
}
[/TOOL_CALL]
'''
    
    # 测试旧格式兼容性
    test_text1_old = '''
<tool_call>
{
    "name": "search_knowledge_base",
    "args": {
        "query": "三峡集团 发展历史",
        "max_results": "5"
    },
    "reasoning": "需要检索三峡集团官方知识库中关于企业成立时间、组织架构演变、重大工程项目里程碑等历史信息，为用户提供系统的发展脉络"
}
</tool_call>
'''
    
    print("🔧 测试工具调用解析")
    print("=" * 50)
    
    # 测试新格式
    print("测试新格式:")
    print(test_text1)
    print("\n解析结果:")
    parsed_actions = text_parser.parse_text(test_text1)
    print(f"解析到 {len(parsed_actions)} 个动作")
    
    for i, action in enumerate(parsed_actions):
        print(f"动作 {i+1}:")
        print(f"  类型: {action.action_type}")
        print(f"  内容: {action.content[:100]}...")
        print(f"  元数据: {action.metadata}")
        print()
    
    # 测试旧格式兼容性
    print("\n🔄 测试旧格式兼容性:")
    print(test_text1_old)
    print("\n解析结果:")
    parsed_actions_old = text_parser.parse_text(test_text1_old)
    print(f"解析到 {len(parsed_actions_old)} 个动作")
    
    for i, action in enumerate(parsed_actions_old):
        print(f"动作 {i+1}:")
        print(f"  类型: {action.action_type}")
        print(f"  内容: {action.content[:100]}...")
        print(f"  元数据: {action.metadata}")
        print()
    
    # 测试工具调用检测函数
    print(f"新格式 is_tool_call 结果: {text_parser.is_tool_call(test_text1)}")
    print(f"旧格式 is_tool_call 结果: {text_parser.is_tool_call(test_text1_old)}")
    
    # 测试提取工具调用函数
    tool_calls = text_parser.extract_tool_calls(test_text1)
    print(f"新格式 extract_tool_calls 结果: {len(tool_calls)} 个工具调用")
    for i, tool_call in enumerate(tool_calls):
        print(f"  工具 {i+1}: {tool_call.name}, 参数: {tool_call.args}")
    
    tool_calls_old = text_parser.extract_tool_calls(test_text1_old)
    print(f"旧格式 extract_tool_calls 结果: {len(tool_calls_old)} 个工具调用")
    for i, tool_call in enumerate(tool_calls_old):
        print(f"  工具 {i+1}: {tool_call.name}, 参数: {tool_call.args}")

def test_patterns():
    """测试正则表达式模式"""
    import re
    
    print("\n🔍 测试正则表达式模式")
    print("=" * 50)
    
    test_text = '''<tool_call>
{
    "name": "search_knowledge_base",
    "args": {
        "query": "三峡集团",
        "max_results": 5
    },
    "reasoning": "需要搜索"
}
</tool_call>'''
    
    patterns = text_parser.tool_patterns
    
    for i, pattern in enumerate(patterns):
        print(f"\n模式 {i+1}: {pattern}")
        matches = re.finditer(pattern, test_text, re.DOTALL | re.IGNORECASE)
        match_count = 0
        for match in matches:
            match_count += 1
            print(f"  匹配 {match_count}: 组数={len(match.groups())}")
            for j, group in enumerate(match.groups()):
                print(f"    组 {j+1}: {group[:100]}...")
        
        if match_count == 0:
            print(f"  无匹配")

if __name__ == "__main__":
    test_tool_call_parsing()
    test_patterns()