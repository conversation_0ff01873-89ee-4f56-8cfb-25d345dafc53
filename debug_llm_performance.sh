#!/bin/bash

# LLM API性能诊断脚本
# 用于测试服务器容器内的LLM API调用速度

CONTAINER_NAME="sx-jiliang-chat-v2"
echo "🔍 开始LLM API性能诊断..."
echo "容器名称: $CONTAINER_NAME"
echo "=========================================="

# 检查容器是否运行
if ! docker ps | grep -q "$CONTAINER_NAME"; then
    echo "❌ 容器 $CONTAINER_NAME 未运行"
    exit 1
fi

echo "✅ 容器运行状态正常"
echo ""

# 3. 测试LLM API调用性能
echo "🤖 3. 测试LLM API调用性能..."

# 先检查Python和依赖
echo "🔍 检查Python环境..."
docker exec $CONTAINER_NAME python3 -c "
import sys
print('Python版本:', sys.version)
try:
    import requests
    print('✅ requests库可用')
except ImportError:
    print('❌ requests库未安装')
    exit(1)
"

# 测试网络连通性
echo "🌐 测试网络连通性..."
docker exec $CONTAINER_NAME python3 -c "
import socket
try:
    sock = socket.create_connection(('*************', 19111), timeout=5)
    sock.close()
    print('✅ 网络连接正常')
except Exception as e:
    print('❌ 网络连接失败:', str(e))
"

# 执行API测试（添加更多调试信息）
docker exec $CONTAINER_NAME python3 << 'PYTHON_SCRIPT'
import requests
import time
import json

print("🚀 开始执行API测试...")

def test_llm_api():
    base_url = 'http://*************:19111/ai/v1'
    model = 'qwen2---5-72b-goxmbugy'
    
    print(f'📋 测试配置:')
    print(f'   API地址: {base_url}')
    print(f'   模型名称: {model}')
    print()
    
    print('🔬 执行简单API调用测试...')
    try:
        start = time.time()
        response = requests.post(
            f'{base_url}/chat/completions',
            json={
                'model': model,
                'messages': [{'role': 'user', 'content': 'hi'}],
                'max_tokens': 5
            },
            timeout=30
        )
        end = time.time()
        
        print(f'⏱️  API调用耗时: {end-start:.2f}秒')
        print(f'📊 响应状态码: {response.status_code}')
        
        if response.status_code == 200:
            print('✅ API调用成功')
            data = response.json()
            if 'choices' in data and len(data['choices']) > 0:
                content = data['choices'][0]['message']['content']
                print(f'📝 响应内容: {content[:50]}...')
            else:
                print('⚠️  响应格式异常')
        else:
            print(f'❌ API调用失败: {response.text[:200]}')
            
    except requests.exceptions.Timeout:
        print('❌ API调用超时 (>30秒)')
    except requests.exceptions.ConnectionError as e:
        print(f'❌ 网络连接错误: {str(e)[:100]}')
    except Exception as e:
        print(f'❌ 调用异常: {str(e)[:100]}')
        import traceback
        traceback.print_exc()

try:
    test_llm_api()
    print("✅ 测试函数执行完成")
except Exception as e:
    print(f"❌ 脚本执行异常: {e}")
    import traceback
    traceback.print_exc()

PYTHON_SCRIPT
echo ""

# 5. 测试容器资源使用
echo "📊 5. 检查容器资源使用..."
docker stats $CONTAINER_NAME --no-stream --format "table {{.Container}}\t{{.CPUPerc}}\t{{.MemUsage}}\t{{.NetIO}}"
echo ""
