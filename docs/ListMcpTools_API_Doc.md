# ListMcpTools API 文档

## 接口概述

该接口用于加载指定的MCP (Model Context Protocol) 工具，并将工具的schema信息上传到指定的服务器。该接口主要供系统内部使用，不需要客户端提供认证信息。

## 基本信息

- **接口路径**: `/jiliang/chat/ListMcpTools`
- **请求方法**: `POST`
- **Content-Type**: `application/json`

## 请求参数

### 请求体参数

| 参数名 | 类型 | 必填 | 描述 |
|--------|------|------|------|
| mcp_ids | List[int] | 是 | MCP服务器ID列表，用于指定要加载的MCP工具 |

> **注意**: 该接口不需要客户端提供认证头，所有认证信息通过环境变量配置。

## 请求示例

```bash
curl -X POST "http://localhost:8000/jiliang/chat/ListMcpTools" \
  -H "Content-Type: application/json" \
  -d '{
    "mcp_ids": [1, 2, 3]
  }'
```

```json
{
  "mcp_ids": [1, 2, 3]
}
```

## 响应格式

### 成功响应

```json
{
  "success": true,
  "message": "MCP工具加载成功",
  "tools_count": 5
}
```

### 失败响应

```json
{
  "success": false,
  "message": "MCP工具加载失败，未能成功连接"
}
```

或

```json
{
  "success": false,
  "message": "未提供mcp_ids"
}
```

### 参数验证错误响应 (HTTP 422)

```json
{
  "detail": [
    {
      "type": "missing",
      "loc": ["body", "mcp_ids"],
      "msg": "Field required",
      "input": {}
    }
  ]
}
```

## 响应字段说明

| 字段名 | 类型 | 描述 |
|--------|------|------|
| success | boolean | 操作是否成功 |
| message | string | 响应消息，描述操作结果 |
| tools_count | integer | 成功加载的工具数量（仅在成功时返回） |

## 接口行为说明

1. **工具加载**: 根据提供的 `mcp_ids` 初始化MCP Simple路由器并加载对应的工具
2. **Schema上传**: 将加载的工具信息（包括工具名称、描述、参数schema）上传到配置的MCP服务器，使用环境变量中配置的认证信息
3. **状态更新**: 为每个MCP ID更新加载状态（成功=1，失败=0），同样使用环境变量认证

## 环境变量依赖

接口运行需要以下环境变量：

- `MCP_API_BASE_URL`: MCP API服务器的基础URL
- `MCP-X-API-Key`: 用于向MCP服务器上传工具schema的API密钥
- `MCP-X-Username`: 用于向MCP服务器上传工具schema的用户名

> **说明**: 这些环境变量用于接口内部调用MCP服务器API，不需要客户端提供。

## 错误处理

- 如果未提供 `mcp_ids` 参数或参数为空，将返回错误消息
- 如果MCP路由器初始化失败，将返回失败状态并尝试更新各个MCP ID的状态为失败
- 网络请求失败（工具上传或状态更新）会记录错误日志但不影响主要响应
- 如果请求体格式不正确或缺少必需参数，FastAPI会自动返回HTTP 422验证错误

## 注意事项

1. 该接口会自动处理多个MCP ID的批量操作
2. 即使部分操作失败，接口仍会尝试完成所有相关操作
3. 所有的网络请求都设置了30秒的超时时间
4. 工具schema以JSON格式存储在 `toolArgs` 字段中
5. `mcp_ids` 必须是整数列表，如果传入其他类型会返回HTTP 422验证错误
6. 请求体必须符合JSON格式，缺少必需字段会触发参数验证错误