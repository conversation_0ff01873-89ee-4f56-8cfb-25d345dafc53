# 线程历史API接口文档

## 概述

从多智能体系统中获取完整的对话历史记录。

## API端点

### POST `/jiliang/chat/get_history`

获取指定用户会话线程的完整对话历史记录。

## 请求格式

### 请求模型：`ThreadHistoryRequest`

| 字段 | 类型 | 必填 | 默认值 | 说明 |
|------|------|------|--------|------|
| `user_id` | string | ✅ | - | 用户标识符 |
| `qa_id` | string | ✅ | - | 会话/对话标识符 |
| `limit` | integer | ❌ | 100 | 返回消息数量限制 |
| `include_metadata` | boolean | ❌ | false | 是否包含线程元数据 |

### 请求示例

```json
{
    "user_id": "user123",
    "qa_id": "session456", 
    "limit": 50,
    "include_metadata": true
}
```

## 响应格式

### 响应模型：`ThreadHistoryResponse`

| 字段 | 类型 | 说明 |
|------|------|------|
| `thread_id` | string | 组合线程标识符 (`{user_id}--{qa_id}`) |
| `messages` | array | 对话消息列表 |
| `total_count` | integer | 响应中的消息总数 |
| `metadata` | object | 线程元数据（如果请求） |

### 消息对象结构

`messages`数组中的每条消息包含：

| 字段 | 类型 | 说明 |
|------|------|------|
| `type` | string | 消息类型：`human`、`ai`、`tool` |
| `content` | string | 原始消息内容 |
| `role` | string | 消息角色：`human`、`assistant`、`tool` |
| `display_type` | string | 显示格式类型 |
| `formatted_content` | string | 清理后的显示内容 |
| `id` | string | 消息唯一标识符 |
| `checkpoint_id` | string | 关联的检查点标识符 |
| `step` | integer | 处理步骤编号 |
| `timestamp` | string | 消息时间戳 |
| `created_at` | string | 创建时间戳 |

#### AI消息额外字段

| 字段 | 类型 | 说明 |
|------|------|------|
| `agent` | string | 智能体标识符 |
| `agent_display_name` | string | 友好的智能体名称 |

### 元数据对象结构

当`include_metadata`为true时：

| 字段 | 类型 | 说明 |
|------|------|------|
| `thread_id` | string | 线程标识符 |
| `total_snapshots` | integer | 状态快照数量 |
| `status` | string | 请求状态 |

## 响应示例

```json
{
    "thread_id": "user123--session456",
    "messages": [
        {
            "type": "human",
            "content": "今天天气怎么样？",
            "role": "human", 
            "display_type": "human_message",
            "formatted_content": "今天天气怎么样？",
            "id": "msg_001",
            "checkpoint_id": "ckpt_123",
            "step": 1,
            "timestamp": "2024-01-15T10:30:00Z"
        },
        {
            "type": "ai",
            "content": "我来帮您查询天气信息。",
            "role": "assistant",
            "display_type": "ai_message", 
            "formatted_content": "我来帮您查询天气信息。",
            "agent": "knowledge_agent",
            "agent_display_name": "知识专家",
            "id": "msg_002",
            "checkpoint_id": "ckpt_124",
            "step": 2,
            "timestamp": "2024-01-15T10:30:05Z"
        }
    ],
    "total_count": 2,
    "metadata": {
        "thread_id": "user123--session456",
        "total_snapshots": 5,
        "status": "success"
    }
}
```

## 错误响应

### 404 - 未找到历史记录

```json
{
    "thread_id": "user123--session456",
    "messages": [],
    "total_count": 0,
    "metadata": {
        "status": "no_history",
        "thread_id": "user123--session456"
    }
}
```

### 500 - 系统错误

```json
{
    "error": "获取线程历史失败: 连接超时"
}
```

### 500 - 检查点系统不可用

```json
{
    "error": "检查点系统不可用"
}
```

### 限制数量并包含元数据

```bash
curl -X POST "http://localhost:18800/jiliang/chat/get_history" \
     -H "Content-Type: application/json" \
     -d '{
       "user_id": "testuser",
       "qa_id": "session001", 
       "limit": 20,
       "include_metadata": true
     }'
```