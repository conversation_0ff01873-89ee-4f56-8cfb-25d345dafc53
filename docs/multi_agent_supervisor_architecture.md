# 多智能体监督者架构设计方案

## 项目概述

基于当前 `jiliang_chat_refactored.py` 的单Agent架构，设计实现一个支持MCP、知识库、多模态处理的多智能体监督者系统。

## 1. 当前系统分析

### 1.1 现有优势
- ✅ 完整的LangGraph ReAct agent架构
- ✅ 动态MCP工具加载（RAG路由器）
- ✅ 达梦数据库checkpointer支持
- ✅ 上下文总结功能
- ✅ 流式响应和会话管理
- ✅ 多种checkpointer支持（DM、Redis、Memory）

### 1.2 核心局限性
- ❌ 单一agent，无法处理复杂多模态任务
- ❌ 缺乏任务分解和专门化处理
- ❌ 工具数量增多时性能下降
- ❌ 无法并行处理多个子任务

## 2. 多智能体监督者架构设计

### 2.1 核心架构图

```
用户请求 → 意图识别器 → 路由决策
                  ↓
    ┌─────────────────────┬─────────────────────┐
    │                     │                     │
简单任务               复杂任务               多模态任务
    │                     │                     │
直接路由               监督者Agent           多Agent协作
    ↓                     ↓                     ↓
单一Agent      MCP Agent + 知识库Agent    图片Agent + 文件Agent
```

### 2.2 系统层次结构

```
├── 意图识别层 (Intent Classification Layer)
│   ├── 任务复杂度分析
│   ├── 任务类型识别
│   └── 路由决策
├── 路由调度层 (Routing Layer)
│   ├── 直接路由 (Simple Tasks)
│   ├── 监督者模式 (Complex Tasks)
│   └── 多Agent协作 (Multi-modal Tasks)
└── 执行层 (Execution Layer)
    ├── MCP专家Agent
    ├── 知识库专家Agent
    ├── 多模态专家Agent
    └── 通用对话Agent
```

## 3. 意图识别系统设计

### 3.1 Pydantic模型定义

```python
from typing import List, Literal, Optional
from pydantic import BaseModel, Field

class TaskComplexity(BaseModel):
    level: Literal["simple", "complex", "multi_modal"] = Field(
        description="任务复杂度：simple-单一操作，complex-需要多步骤，multi_modal-涉及多种模态"
    )
    reasoning: str = Field(description="复杂度判断的原因")

class TaskType(BaseModel):
    primary_type: Literal["mcp", "knowledge_base", "file_processing", "image_analysis", "general_chat"] = Field(
        description="主要任务类型"
    )
    secondary_types: List[str] = Field(description="次要任务类型")
    requires_collaboration: bool = Field(description="是否需要多Agent协作")

class IntentClassification(BaseModel):
    complexity: TaskComplexity
    task_type: TaskType
    route_decision: Literal["direct", "supervisor", "multi_agent"] = Field(
        description="路由决策：direct-直接路由，supervisor-监督者模式，multi_agent-多Agent协作"
    )
    recommended_agents: List[str] = Field(description="推荐的Agent列表")
```

### 3.2 意图识别实现

```python
async def classify_intent(question: str, context: dict) -> IntentClassification:
    """智能意图识别"""
    
    # 创建结构化输出的LLM
    classifier_llm = llm.with_structured_output(IntentClassification)
    
    # 构建分类提示词
    classification_prompt = f"""
    分析以下用户请求，判断任务复杂度和类型：
    
    用户问题：{question}
    上下文信息：{context}
    
    请根据以下标准进行分类：
    1. 任务复杂度：
       - simple: 单一操作，如简单问答、基础检索
       - complex: 需要多步骤，如复杂推理、多工具协作
       - multi_modal: 涉及多种模态，如图片+文本、文件+问答
    
    2. 任务类型：
       - mcp: 需要调用外部MCP工具
       - knowledge_base: 需要知识库检索
       - file_processing: 需要处理文件
       - image_analysis: 需要图片分析
       - general_chat: 普通对话
    
    3. 路由决策：
       - direct: 可直接处理的简单任务
       - supervisor: 需要监督者协调的复杂任务
       - multi_agent: 需要多Agent协作的复合任务
    """
    
    result = await classifier_llm.ainvoke(classification_prompt)
    return result
```

## 4. 专门化Agent设计

### 4.1 MCP专家Agent

```python
async def create_mcp_agent(mcp_ids: List[str], question: str) -> Any:
    """专门处理MCP工具调用的Agent"""
    
    # 动态加载MCP工具
    mcp_tools = await mcp_router.get_dynamic_mcp_tools(question, mcp_ids)
    
    # 创建专门化的MCP Agent
    mcp_agent = create_react_agent(
        model=agent_llm,
        tools=mcp_tools,
        name="mcp_expert",
        prompt="""
        你是MCP工具专家，专门处理外部工具调用和API集成任务。
        
        核心能力：
        1. 理解用户需求，选择合适的MCP工具
        2. 正确调用外部API和服务
        3. 处理工具调用的结果和错误
        4. 将结果转换为用户友好的格式
        
        工作原则：
        - 优先使用最相关的工具
        - 处理工具调用异常
        - 提供清晰的执行反馈
        """,
        checkpointer=graph_checkpointer
    )
    
    return mcp_agent
```

### 4.2 知识库专家Agent

```python
async def create_knowledge_agent(rag_ids: List[str]) -> Any:
    """专门处理知识库检索的Agent"""
    
    # 构建知识库工具
    knowledge_tools = [search_knowledge_base]
    
    # 如果有额外的知识库ID，添加对应工具
    if rag_ids:
        for rag_id in rag_ids:
            knowledge_tools.append(
                create_knowledge_tool(rag_id)
            )
    
    knowledge_agent = create_react_agent(
        model=agent_llm,
        tools=knowledge_tools,
        name="knowledge_expert",
        prompt="""
        你是知识库专家，专门处理信息检索和知识问答任务。
        
        核心能力：
        1. 理解用户查询意图
        2. 从多个知识库中检索相关信息
        3. 综合分析检索结果
        4. 提供准确、完整的答案
        
        工作原则：
        - 优先使用最相关的知识库
        - 交叉验证多个信息源
        - 明确标注信息来源
        - 承认知识边界
        """,
        checkpointer=graph_checkpointer
    )
    
    return knowledge_agent
```

### 4.3 多模态专家Agent

```python
async def create_multimodal_agent() -> Any:
    """专门处理文件和图片的Agent"""
    
    # 多模态处理工具
    multimodal_tools = [
        process_image_tool,
        process_document_tool,
        extract_text_tool,
        ocr_tool,
        handwriting_recognition_tool
    ]
    
    multimodal_agent = create_react_agent(
        model=agent_llm,
        tools=multimodal_tools,
        name="multimodal_expert",
        prompt="""
        你是多模态处理专家，专门处理图片、文档和文件分析任务。
        
        核心能力：
        1. 图片内容识别和描述
        2. 文档内容提取和分析
        3. OCR文字识别
        4. 手写识别
        5. 表格结构化提取
        
        工作原则：
        - 根据文件类型选择合适的处理工具
        - 保持内容的完整性和准确性
        - 提供结构化的分析结果
        - 处理多种文件格式
        """,
        checkpointer=graph_checkpointer
    )
    
    return multimodal_agent
```

## 5. 监督者系统实现

### 5.1 监督者Agent创建

```python
async def create_supervisor_workflow(
    agents: List[Any], 
    intent: IntentClassification
) -> Any:
    """创建监督者工作流"""
    
    # 根据意图生成监督者提示词
    supervisor_prompt = generate_supervisor_prompt(intent)
    
    # 创建监督者工作流
    supervisor_workflow = create_supervisor(
        agents=agents,
        model=agent_llm,
        prompt=supervisor_prompt,
        output_mode="full_history",  # 保持完整历史
        add_handoff_messages=True,   # 添加交接消息
        handoff_tool_prefix="delegate_to"  # 自定义工具前缀
    )
    
    return supervisor_workflow

def generate_supervisor_prompt(intent: IntentClassification) -> str:
    """根据意图生成监督者提示词"""
    
    base_prompt = """
    你是一个智能任务监督者，负责协调多个专门化Agent来完成复杂任务。
    
    可用的Agent：
    - mcp_expert: 处理外部工具调用和API集成
    - knowledge_expert: 处理知识库检索和问答
    - multimodal_expert: 处理图片、文档和文件分析
    
    工作原则：
    1. 分析任务需求，制定执行计划
    2. 按需调用合适的专门化Agent
    3. 协调Agent之间的信息传递
    4. 整合各Agent的结果
    5. 确保任务完整完成
    """
    
    # 根据意图添加特定指导
    if intent.complexity.level == "complex":
        base_prompt += """
        
        当前任务复杂度：复杂任务
        - 需要多步骤处理
        - 可能需要多个Agent协作
        - 注意保持任务的逻辑连贯性
        """
    
    if intent.task_type.requires_collaboration:
        base_prompt += """
        
        协作模式：多Agent协作
        - 合理安排Agent执行顺序
        - 确保信息在Agent间正确传递
        - 避免重复工作
        """
    
    return base_prompt
```

### 5.2 智能路由系统

```python
async def route_request(
    intent: IntentClassification,
    request: RagQARequest
) -> Any:
    """智能路由请求到合适的处理模式"""
    
    # 根据路由决策选择处理模式
    if intent.route_decision == "direct":
        # 直接路由到单一Agent
        return await create_direct_agent(intent, request)
    
    elif intent.route_decision == "supervisor":
        # 使用监督者模式
        agents = await create_required_agents(intent, request)
        return await create_supervisor_workflow(agents, intent)
    
    elif intent.route_decision == "multi_agent":
        # 多Agent协作模式
        return await create_multi_agent_network(intent, request)
    
    else:
        # 默认回退到现有的单Agent模式
        return await create_dynamic_agent(
            request.model,
            request.question,
            mcp_ids=request.mcp_ids
        )

async def create_required_agents(
    intent: IntentClassification,
    request: RagQARequest
) -> List[Any]:
    """根据意图创建所需的Agent"""
    
    agents = []
    
    # 根据推荐的Agent列表创建相应的专门化Agent
    for agent_type in intent.recommended_agents:
        if agent_type == "mcp_expert" and request.mcp_ids:
            agent = await create_mcp_agent(request.mcp_ids, request.question)
            agents.append(agent)
        
        elif agent_type == "knowledge_expert":
            agent = await create_knowledge_agent(request.extral_rag_ids or [])
            agents.append(agent)
        
        elif agent_type == "multimodal_expert":
            agent = await create_multimodal_agent()
            agents.append(agent)
    
    return agents
```

## 6. 详细实现步骤

### 6.1 阶段1：基础架构搭建（1-2周）

#### 步骤1：环境准备
```bash
# 安装依赖
pip install langgraph-supervisor

# 创建新的模块文件
mkdir -p rag_chat/multi_agent
touch rag_chat/multi_agent/__init__.py
touch rag_chat/multi_agent/intent_classifier.py
touch rag_chat/multi_agent/specialized_agents.py
touch rag_chat/multi_agent/supervisor_agent.py
touch rag_chat/multi_agent/router.py
```

#### 步骤2：意图识别模块
```python
# rag_chat/multi_agent/intent_classifier.py
from typing import List, Literal, Optional
from pydantic import BaseModel, Field
from langchain_openai import ChatOpenAI

class IntentClassifier:
    def __init__(self, llm: ChatOpenAI):
        self.llm = llm.with_structured_output(IntentClassification)
    
    async def classify(self, question: str, context: dict) -> IntentClassification:
        """分类用户意图"""
        # 实现意图分类逻辑
        pass
```

#### 步骤3：专门化Agent模块
```python
# rag_chat/multi_agent/specialized_agents.py
from langgraph.prebuilt import create_react_agent

class SpecializedAgents:
    def __init__(self, llm, mcp_router, checkpointer):
        self.llm = llm
        self.mcp_router = mcp_router
        self.checkpointer = checkpointer
    
    async def create_mcp_agent(self, mcp_ids: List[str], question: str):
        """创建MCP专家Agent"""
        # 实现MCP Agent创建逻辑
        pass
    
    async def create_knowledge_agent(self, rag_ids: List[str]):
        """创建知识库专家Agent"""
        # 实现知识库Agent创建逻辑
        pass
    
    async def create_multimodal_agent(self):
        """创建多模态专家Agent"""
        # 实现多模态Agent创建逻辑
        pass
```

### 6.2 阶段2：监督者模式实现（2-3周）

#### 步骤4：监督者Agent
```python
# rag_chat/multi_agent/supervisor_agent.py
from langgraph_supervisor import create_supervisor

class SupervisorAgent:
    def __init__(self, llm, checkpointer):
        self.llm = llm
        self.checkpointer = checkpointer
    
    async def create_supervisor_workflow(self, agents: List[Any], intent: IntentClassification):
        """创建监督者工作流"""
        # 实现监督者工作流创建逻辑
        pass
```

#### 步骤5：智能路由系统
```python
# rag_chat/multi_agent/router.py
class IntelligentRouter:
    def __init__(self, intent_classifier, specialized_agents, supervisor_agent):
        self.intent_classifier = intent_classifier
        self.specialized_agents = specialized_agents
        self.supervisor_agent = supervisor_agent
    
    async def route_request(self, request: RagQARequest) -> Any:
        """智能路由请求"""
        # 1. 分类意图
        intent = await self.intent_classifier.classify(request.question, {})
        
        # 2. 根据意图路由
        if intent.route_decision == "direct":
            return await self._direct_route(intent, request)
        elif intent.route_decision == "supervisor":
            return await self._supervisor_route(intent, request)
        elif intent.route_decision == "multi_agent":
            return await self._multi_agent_route(intent, request)
```

### 6.3 阶段3：多模态能力扩展（3-4周）

#### 步骤6：多模态工具实现
```python
# rag_chat/multi_agent/multimodal_tools.py
from langchain_core.tools import tool

@tool
async def process_image(image_path: str) -> str:
    """处理图片并返回文本描述"""
    # 调用现有的OCR和图片处理API
    pass

@tool
async def process_document(file_path: str) -> str:
    """处理文档并返回文本化内容"""
    # 调用现有的文档处理API
    pass

@tool
async def extract_text(file_path: str) -> str:
    """提取文件中的文本"""
    # 调用现有的文本提取API
    pass
```

#### 步骤7：文件上传处理
```python
# 扩展现有的文件处理能力
async def handle_file_upload(file_data: bytes, file_type: str) -> str:
    """处理文件上传"""
    # 1. 保存文件到临时目录
    # 2. 根据文件类型调用相应的处理工具
    # 3. 返回文本化结果
    pass
```

### 6.4 阶段4：集成与优化（1-2周）

#### 步骤8：主服务集成
```python
# 修改 jiliang_chat_refactored.py
from multi_agent.router import IntelligentRouter

# 初始化多Agent路由器
multi_agent_router = IntelligentRouter(
    intent_classifier, 
    specialized_agents, 
    supervisor_agent
)

@router.post("/jiliang/chat/v3")
async def chat_stream_v3(request: Request, body: RagQARequest):
    """新的多Agent聊天接口"""
    
    # 使用多Agent路由器处理请求
    agent = await multi_agent_router.route_request(body)
    
    # 配置和流式响应保持不变
    config = {
        "configurable": {"thread_id": f"{userid}--{qa_id}", "checkpoint_ns": "chat_stream_v3"}
    }
    
    return StreamingResponse(
        stream_agent_response(agent, body.question, config, userid, apikey, qa_id),
        media_type="text/event-stream; charset=utf-8",
        headers=headers
    )
```

#### 步骤9：向后兼容
```python
# 保持现有v2接口不变，作为备用
@router.post("/jiliang/chat/v2")
async def chat_stream_v2(request: Request, body: RagQARequest):
    """现有的单Agent接口（保持兼容）"""
    # 保持原有逻辑不变
    pass
```

## 7. 性能优化策略

### 7.1 并发处理
```python
# 使用asyncio并发执行多个Agent
async def parallel_agent_execution(agents: List[Any], tasks: List[str]):
    """并行执行多个Agent任务"""
    
    async def execute_agent_task(agent, task):
        return await agent.ainvoke({"messages": [{"role": "user", "content": task}]})
    
    # 并发执行
    results = await asyncio.gather(*[
        execute_agent_task(agent, task) 
        for agent, task in zip(agents, tasks)
    ])
    
    return results
```

### 7.2 缓存机制
```python
# 意图分类结果缓存
from functools import lru_cache
from hashlib import md5

@lru_cache(maxsize=1000)
async def cached_intent_classification(question_hash: str) -> IntentClassification:
    """缓存意图分类结果"""
    # 对于相同的问题，复用分类结果
    pass
```

### 7.3 资源管理
```python
# Agent池管理
class AgentPool:
    def __init__(self, max_agents: int = 10):
        self.max_agents = max_agents
        self.active_agents = {}
        self.agent_queue = asyncio.Queue()
    
    async def get_agent(self, agent_type: str) -> Any:
        """获取或创建Agent"""
        if agent_type in self.active_agents:
            return self.active_agents[agent_type]
        
        # 创建新Agent
        agent = await self.create_agent(agent_type)
        self.active_agents[agent_type] = agent
        return agent
    
    async def release_agent(self, agent_type: str):
        """释放Agent资源"""
        if agent_type in self.active_agents:
            del self.active_agents[agent_type]
```

## 8. 测试策略

### 8.1 单元测试
```python
# tests/test_intent_classifier.py
import pytest
from multi_agent.intent_classifier import IntentClassifier

@pytest.mark.asyncio
async def test_simple_intent_classification():
    """测试简单任务的意图分类"""
    classifier = IntentClassifier(llm)
    result = await classifier.classify("今天天气怎么样？", {})
    assert result.complexity.level == "simple"
    assert result.route_decision == "direct"

@pytest.mark.asyncio
async def test_complex_intent_classification():
    """测试复杂任务的意图分类"""
    classifier = IntentClassifier(llm)
    result = await classifier.classify(
        "帮我分析这张图片的内容，并结合知识库查找相关信息", {}
    )
    assert result.complexity.level == "multi_modal"
    assert result.route_decision == "multi_agent"
```

### 8.2 集成测试
```python
# tests/test_multi_agent_integration.py
@pytest.mark.asyncio
async def test_supervisor_workflow():
    """测试监督者工作流"""
    # 创建测试请求
    request = RagQARequest(
        question="分析这个文档并查找相关MCP工具",
        mcp_ids=["test_mcp"],
        extral_rag_ids=["test_rag"]
    )
    
    # 执行多Agent处理
    result = await multi_agent_router.route_request(request)
    
    # 验证结果
    assert result is not None
    assert "messages" in result
```

### 8.3 性能测试
```python
# tests/test_performance.py
import time
import asyncio

@pytest.mark.asyncio
async def test_concurrent_requests():
    """测试并发请求处理能力"""
    
    # 创建多个并发请求
    requests = [
        RagQARequest(question=f"测试问题{i}") 
        for i in range(10)
    ]
    
    start_time = time.time()
    
    # 并发处理
    results = await asyncio.gather(*[
        multi_agent_router.route_request(req) 
        for req in requests
    ])
    
    end_time = time.time()
    
    # 验证性能
    assert len(results) == 10
    assert end_time - start_time < 30  # 30秒内完成
```

## 9. 部署和运维

### 9.1 Docker配置
```dockerfile
# 更新Dockerfile
FROM python:3.11-slim

# 安装新依赖
RUN pip install langgraph-supervisor

# 复制多Agent模块
COPY rag_chat/multi_agent /app/rag_chat/multi_agent

# 其他配置保持不变
```

### 9.2 环境变量配置
```bash
# 新增环境变量
MULTI_AGENT_ENABLED=true
INTENT_CLASSIFIER_MODEL=gpt-4o
SUPERVISOR_MAX_AGENTS=10
AGENT_POOL_SIZE=20
```

### 9.3 监控和日志
```python
# 添加监控指标
from prometheus_client import Counter, Histogram, Gauge

# 定义监控指标
intent_classification_counter = Counter('intent_classification_total', 'Intent classification count', ['intent_type'])
agent_execution_duration = Histogram('agent_execution_duration_seconds', 'Agent execution duration')
active_agents_gauge = Gauge('active_agents_count', 'Number of active agents')

# 在代码中添加监控
async def classify_intent_with_metrics(question: str, context: dict):
    """带监控的意图分类"""
    start_time = time.time()
    
    try:
        result = await classify_intent(question, context)
        intent_classification_counter.labels(intent_type=result.task_type.primary_type).inc()
        return result
    finally:
        duration = time.time() - start_time
        agent_execution_duration.observe(duration)
```

## 10. 风险评估和缓解策略

### 10.1 技术风险
- **Agent协调复杂度**：通过完善的测试和监控缓解
- **性能下降**：通过缓存和连接池优化
- **错误传播**：通过独立的错误处理和降级机制

### 10.2 业务风险
- **用户体验变化**：通过A/B测试和渐进式部署
- **成本增加**：通过智能路由减少不必要的Agent调用
- **维护复杂度**：通过良好的文档和监控工具

### 10.3 缓解策略
1. **降级机制**：当多Agent系统出现问题时，自动降级到单Agent模式
2. **监控告警**：实时监控系统健康状况
3. **版本管理**：保持旧版本接口的可用性
4. **负载均衡**：合理分配Agent资源

## 11. 总结

这个多智能体监督者架构设计充分利用了LangGraph的先进特性，实现了从单一Agent向多智能体系统的优雅升级。主要优势包括：

1. **任务专门化**：每个Agent专注于特定领域，提高处理效率
2. **智能路由**：基于LLM的意图识别，自动选择最合适的处理方式
3. **可扩展性**：易于添加新的专门化Agent和功能
4. **向后兼容**：保持现有接口的稳定性
5. **性能优化**：通过并发处理和缓存机制提升响应速度

通过分阶段实施这个方案，可以在保持系统稳定性的同时，大幅提升处理复杂任务的能力，为用户提供更好的智能对话体验。