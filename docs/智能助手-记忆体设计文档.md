# 智能助手-会话记忆设计文档

## 表设计
该表为Mem0自动生成的Milvus Collection。

**表名**：T_USER_CHAT_MESSAGE_MEMORIES

**Schema**：

| 字段       | 类型               | Nullable | 默认值 | 索引名称      | 索引类型         | 索引参数 | 描述 |
|------------|--------------------|----------|--------|---------------|------------------|----------|------|
| id         | VarChar(512)       | ×        | --     | 创建标量索引  | --               | --       | --   |
| vectors    | FloatVector(1024)   | ×        | --     | vector_index  | AUTOINDEX(L2)    | --       | --   |
| metadata   | JSON               | ×        | --     | --            | --               | --       | --   |
| $meta      | JSON (Dynamic field) | ×        | --     | --            | --               | --       | --   |

其中，向量维度根据嵌入模型决定。

## 接口设计

### 公共请求头

| 参数名    | 类型    | 必填 | 描述                       |
| --------- | ------- | ---- | -------------------------- |
| X-API-KEY | String  | 是   | apikey |
| X-Username | String  | 是   | 当前操作用户的用户名（行云用户名）|

```
X-API-Key:<your-api-key>
X-Username:<用户名>
```

### 对话记忆存储接口

**功能说明**: 存储用户对话记忆

**请求URL**: `/v2/jiliang/qa_message_memory_create`

**请求方法**: POST

**请求参数**:

| 参数名    | 类型    | 必填 | 描述                       |
| --------- | ------- | ---- | -------------------------- |
| userid    | String  | 是   | 行云用户名 |
| qa_id    | String  | 是   | 会话ID |
| agent_id    | String  | 否   | 智能体ID |
| messages    | List  | 是   | 消息 |
| files    | List  | 否   | 文件 |
| metadata    | Object  | 是   | 元数据，例如位置、时间戳、用户状态等 |

messages 对象描述：

| 参数名     | 类型   | 描述                            |
| ---------- | ------ | ------------------------------ |
| role      | String | 角色：user/assitant              |
| content   | String | 消息内容 |

files 对象描述：

| 参数名     | 类型   | 描述                            |
| ---------- | ------ | ------------------------------ |
| file_type   | Integer | 0图像，1语音 |
| file_id   | String | 文件ID |
| file_url   | String | 文件URL（与file_base64，二选一） |
| file_base64   | String | 文件BASE64（与file_url，二选一） |

注：
```
file_base64写法

PNG图像：  f"data:image/png;base64,{base64_image}"
JPEG图像： f"data:image/jpeg;base64,{base64_image}"
WEBP图像： f"data:image/webp;base64,{base64_image}"
```

metadata 对象描述：

| 参数名     | 类型   | 描述                            |
| ---------- | ------ | ------------------------------ |
| chat_type   | Integer | 对话类型:0普通问答，1⽂件问答，2知识库问答 |

**请求示例**:

json

```json
{
  "userid": "xkm",
  "qa_id": "qa-40b2516c-45a9-4e9f-affc-87960299e568",
  "agent_id": "",
  "messages": [
      {
        "role": "user", 
        "content": "Hi, I'm Alex. I'm a vegetarian and I'm allergic to nuts."
      },
      {
        "role": "assistant", 
        "content": "Hello Alex! I've noted that you're a vegetarian and have a nut allergy. I'll keep this in mind for any food-related recommendations or discussions."
      },
      {
        "role": "user", 
        "content": "thank you!"
      },
      {
        "role": "assistant", 
        "content": "You're welcome! "
      }
  ],
  "files": [
    {
        "file_type": 0,
        "file_id": "123456798",
        "file_url": "https://help-static-aliyun-doc.aliyuncs.com/file-manage-files/zh-CN/20241022/emyrja/dog_and_girl.jpeg",
    }
  ],
  "metadata": {
        "chat_type": 1,
  }
}
```

**响应格式**: JSON

响应参数描述：

| 参数名    | 类型   | 描述                         |
| --------- | ------ | ---------------------------- |
| code      | String | 响应编码，200:成功，500:失败 |
| msg       | String | 响应主信息                   |
| errorCode | String | 业务错误编码                 |
| errorMsg  | String | 业务错误描述                 |
| data      | Any    | 业务场景对象                 |
| exception | String | 异常信息                     |

data对象描述：

| 参数名    | 类型    | 描述         |
| --------- | ------- | ------------ |
| memory_id  | String    | 记忆id |
| memory | String | 记忆 |
| event  | String  | 表事件：ADD/UPDATE |
| previous_memory  | Object | 更新前记忆 |

**响应示例**:

json

```json
{
    "code": "200",
    "msg": "success",
    "errorCode": "",
    "errorMsg": "",
    "data":[
      {
          "memory_id": "89054c1e-0566-43c6-b9e6-978598cd0c5a",
          "memory": "Would like to go to Japan for a week",
          "event": "UPDATE",
          "previous_memory": "Planning a trip to Japan next month"
      },
      {
          "memory_id": "7aa70167-9ef0-40b6-840c-a50ebbacec8e",
          "memory": "Looking for a place to stay in Japan",
          "event": "ADD"
      }
    ],
    "exception": ""
}
```

### 对话记忆检索接口

**功能说明**: 检索对话记忆

**请求URL**: `/v2/jiliang/qa_message_memory_search`

**请求方法**: POST

**请求参数**:

| 参数名    | 类型    | 必填 | 描述                       |
| --------- | ------- | ---- | -------------------------- |
| query    | String  | 是   | 消息内容 |
| userid    | String  | 是   | 行云用户名 |
| qa_id    | String  | 否   | 会话ID |
| agent_id    | String  | 否   | 智能体ID |
| limit    | Integer | 否   | 返回结果数量，默认100条 |
| filters    | Object  | 否   | 检索条件 |
| threshold    | Float  | 否   | 设置相关性阈值以确保质量结果，值越小越相关,返回大于等于该阈值的结果 |

filters对象描述：
| 参数名     | 类型   | 描述                            |
| ---------- | ------ | ------------------------------ |
| field_name        | String | 字段名             |
| field_value        | String | 字段值             |

**请求示例**:

json

```json
{
  "query": "I'm a vegetarian and I'm allergic to nuts.",
  "userid": "xkm",
  "qa_id": "qa-40b2516c-45a9-4e9f-affc-87960299e568",
  "agent_id": "",
  "limit": 2,
  "filters": {
    "chat_type": 1
  },
  "threshold": 1.2
}
```

**响应格式**: JSON

响应参数描述：

| 参数名    | 类型   | 描述                         |
| --------- | ------ | ---------------------------- |
| code      | String | 响应编码，200:成功，500:失败 |
| msg       | String | 响应主信息                   |
| errorCode | String | 业务错误编码                 |
| errorMsg  | String | 业务错误描述                 |
| data      | Any    | 业务场景对象                 |
| exception | String | 异常信息                     |

data对象描述：

| 参数名    | 类型    | 描述         |
| --------- | ------- | ------------ |
| memory_id  | String    | 记忆id |
| memory | String | 记忆 |
| hash  | String    | 哈希值 |
| metadata  | Object | 元数据 |
| score  | Float    | 相关性 |
| created_at      | String | 记忆创建时间 |
| updated_at      | String | 记忆更新时间 |
| userid      | String | 行云用户名 |
| qa_id      | String | 会话ID |

metadata 对象描述：

| 参数名     | 类型   | 描述                            |
| ---------- | ------ | ------------------------------ |
| chat_type   | Integer | 对话类型:0普通问答，1⽂件问答，2知识库问答 |
| file_type   | Integer | 0图像，1语音 |
| file_id   | String | 文件ID |

**响应示例**:

json

```json
{
    "code": "200",
    "msg": "success",
    "errorCode": "",
    "errorMsg": "",
    "data": [
        {
          "memory_id": "31d48491-8582-42b8-9b70-d0cd09234808",
          "memory": "Looking for a place to stay and a place to eat", 
          "hash": "7120ad158dea2aeff717191e38fae572",
          "metadata": {
              "chat_type": 1,
              "file_type": 0,
              "file_id": "123456798"
          },
          "score": 0.1083630323410034, 
          "created_at": "2025-06-17T18:26:45.918052-07:00",
          "updated_at": null,
          "user_id": "alex", 
          "qa_id": "trip-planning-2024"
        },
        {
          "memory_id": "25f70064-9e56-48f3-9645-56f069f48782",
          "memory": "Suggested to stay in Tokyo Metro hotel", 
          "hash": "7f2e80bf9aea8601a7d2ea3240619836",
          "metadata": {
              "chat_type": 1,
          },
          "score": 1.1083630323410034, 
          "created_at": "2025-06-17T18:26:45.928306-07:00",
          "updated_at": null,
          "user_id": "alex", 
          "qa_id": "trip-planning-2024"
        }
    ],
    "exception": ""
}
```

### 会话记忆获取接口

**功能说明**: 获取会话全部记忆

**请求URL**: `/v2/jiliang/qa_session_all_memories`

**请求方法**: POST

**请求参数**:

| 参数名    | 类型    | 必填 | 描述                       |
| --------- | ------- | ---- | -------------------------- |
| userid    | String  | 是   | 行云用户名 |
| qa_id    | String  | 否   | 会话ID |
| agent_id    | String  | 否   | 智能体ID |
| chat_type   | Integer | 否   | 对话类型:0普通问答，1⽂件问答，2知识库问答 |

**请求示例**:

json

```json
{
  "userid": "xkm",
  "qa_id": "qa-40b2516c-45a9-4e9f-affc-87960299e568",
  "agent_id": "",
  "chat_type": 1
}
```

**响应格式**: JSON

响应参数描述：

| 参数名    | 类型   | 描述                         |
| --------- | ------ | ---------------------------- |
| code      | String | 响应编码，200:成功，500:失败 |
| msg       | String | 响应主信息                   |
| errorCode | String | 业务错误编码                 |
| errorMsg  | String | 业务错误描述                 |
| data      | Any    | 业务场景对象                 |
| exception | String | 异常信息                     |

data对象描述：

| 参数名    | 类型    | 描述         |
| --------- | ------- | ------------ |
| memory_id  | String    | 记忆id |
| memory | String | 记忆 |
| hash  | String    | 哈希值 |
| metadata  | Object | 元数据 |
| created_at      | String | 记忆创建时间 |
| updated_at      | String | 记忆更新时间 |
| userid      | String | 行云用户名 |
| qa_id      | String | 会话ID |

metadata 对象描述：

| 参数名     | 类型   | 描述                            |
| ---------- | ------ | ------------------------------ |
| chat_type   | Integer | 对话类型:0普通问答，1⽂件问答，2知识库问答 |
| file_type   | Integer | 0图像，1语音 |
| file_id   | String | 文件ID |

**响应示例**:

json

```json
{
    "code": "200",
    "msg": "success",
    "errorCode": "",
    "errorMsg": "",
    "data": [
        {
          "memory_id": "31d48491-8582-42b8-9b70-d0cd09234808",
          "memory": "Looking for a place to stay and a place to eat", 
          "hash": "7120ad158dea2aeff717191e38fae572",
          "metadata": {
              "chat_type": 1,
              "file_type": 0,
              "file_id": "123456798"
          },
          "created_at": "2025-06-17T18:26:45.918052-07:00",
          "updated_at": null,
          "user_id": "alex", 
          "qa_id": "trip-planning-2024"
        },
        {
          "memory_id": "25f70064-9e56-48f3-9645-56f069f48782",
          "memory": "Suggested to stay in Tokyo Metro hotel", 
          "hash": "7f2e80bf9aea8601a7d2ea3240619836",
          "metadata": {
              "chat_type": 1
          },
          "created_at": "2025-06-17T18:26:45.928306-07:00",
          "updated_at": null,
          "user_id": "alex", 
          "qa_id": "trip-planning-2024"
        }
    ],
    "exception": ""
}
```

### 会话记忆删除接口

**功能说明**: 删除用户会话记忆

**请求URL**: `/v2/jiliang/qa_session_memories_delete`

**请求方法**: POST

**请求参数**:

| 参数名    | 类型    | 必填 | 描述                       |
| --------- | ------- | ---- | -------------------------- |
| userid    | String  | 是   | 行云用户名，查询该用户记录 |
| qa_id     | String  | 是   | 会话ID   |
| agent_id    | String  | 否   | 智能体ID |
| chat_type   | Integer | 是  | 对话类型:0普通问答，1⽂件问答，2知识库问答 |
| memory_ids | List | 否   | 单条/多条记忆 |
| delete_all | Bool  | 否   | 是否清空该会话的所有记忆, 默认否：False |

**请求示例**:

json

```json
不清空所有：
{
  "userid": "xkm",
  "qa_id": "qa-40b2516c-45a9-4e9f-affc-87960299e568",
  "agent_id": "",
  "chat_type": 1, 
  "memory_ids": ["45e9952d-aef2-401e-b373-ebda9e74a7d5"]
}
清空所有：
{
  "userid": "xkm",
  "qa_id": "qa-40b2516c-45a9-4e9f-affc-87960299e568",
  "agent_id": "",
  "chat_type": 1,
  "delete_all": true
}
```

**响应格式**: JSON

响应参数描述：

| 参数名    | 类型   | 描述                         |
| --------- | ------ | ---------------------------- |
| code      | String | 响应编码，200:成功，500:失败 |
| msg       | String | 响应主信息                   |
| errorCode | String | 业务错误编码                 |
| errorMsg  | String | 业务错误描述                 |
| data      | Any    | 业务场景对象                 |
| exception | String | 异常信息                     |

**响应示例**:

json

```json
{
    "code": "200",
    "msg": "success",
    "errorCode": "",
    "errorMsg": "",
    "data":"",
    "exception": ""
}
```

### 错误码说明

| 错误码 | 描述           |
| ------ | -------------- |
| 500    | 服务器内部错误 |