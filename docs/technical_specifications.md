# 多智能体监督者架构技术规范

## 1. 系统架构规范

### 1.1 整体架构

```
┌─────────────────┐    ┌─────────────────┐    ┌─────────────────┐
│   用户请求层     │    │   路由调度层     │    │   执行层        │
├─────────────────┤    ├─────────────────┤    ├─────────────────┤
│ FastAPI接口     │───▶│ 意图识别器       │───▶│ MCP专家Agent    │
│ 请求验证        │    │ 智能路由器       │    │ 知识库专家Agent  │
│ 参数解析        │    │ 负载均衡器       │    │ 多模态专家Agent  │
└─────────────────┘    └─────────────────┘    │ 监督者Agent     │
                                              └─────────────────┘
           │                    │                       │
           ▼                    ▼                       ▼
┌─────────────────┐    ┌─────────────────┐    ┌─────────────────┐
│   存储层        │    │   缓存层        │    │   监控层        │
├─────────────────┤    ├─────────────────┤    ├─────────────────┤
│ 达梦数据库      │    │ Redis缓存       │    │ Prometheus监控   │
│ MongoDB        │    │ 内存缓存        │    │ 日志聚合        │
│ 文件存储        │    │ 结果缓存        │    │ 性能指标        │
└─────────────────┘    └─────────────────┘    └─────────────────┘
```

### 1.2 模块依赖关系

```python
# 模块依赖图
multi_agent/
├── __init__.py
├── intent_classifier.py      # 意图识别模块
├── specialized_agents.py     # 专门化Agent模块
├── supervisor_agent.py       # 监督者Agent模块
├── router.py                # 智能路由模块
├── multimodal_tools.py      # 多模态工具模块
├── agent_pool.py            # Agent池管理模块
├── cache_manager.py         # 缓存管理模块
└── monitoring.py            # 监控指标模块
```

## 2. 数据模型规范

### 2.1 意图分类模型

```python
from typing import List, Literal, Optional
from pydantic import BaseModel, Field
from datetime import datetime

class TaskComplexity(BaseModel):
    """任务复杂度模型"""
    level: Literal["simple", "complex", "multi_modal"] = Field(
        description="任务复杂度等级"
    )
    reasoning: str = Field(
        description="复杂度判断理由",
        max_length=500
    )
    confidence: float = Field(
        description="置信度评分",
        ge=0.0,
        le=1.0
    )

class TaskType(BaseModel):
    """任务类型模型"""
    primary_type: Literal[
        "mcp", 
        "knowledge_base", 
        "file_processing", 
        "image_analysis", 
        "general_chat"
    ] = Field(description="主要任务类型")
    
    secondary_types: List[str] = Field(
        description="次要任务类型",
        default_factory=list
    )
    
    requires_collaboration: bool = Field(
        description="是否需要多Agent协作",
        default=False
    )
    
    estimated_duration: Optional[int] = Field(
        description="预估执行时间（秒）",
        default=None
    )

class IntentClassification(BaseModel):
    """意图分类结果模型"""
    complexity: TaskComplexity
    task_type: TaskType
    
    route_decision: Literal["direct", "supervisor", "multi_agent"] = Field(
        description="路由决策类型"
    )
    
    recommended_agents: List[str] = Field(
        description="推荐的Agent列表",
        default_factory=list
    )
    
    priority: Literal["low", "medium", "high", "urgent"] = Field(
        description="任务优先级",
        default="medium"
    )
    
    timestamp: datetime = Field(
        description="分类时间戳",
        default_factory=datetime.now
    )
    
    metadata: dict = Field(
        description="额外元数据",
        default_factory=dict
    )
```

### 2.2 Agent状态模型

```python
class AgentStatus(BaseModel):
    """Agent状态模型"""
    agent_id: str = Field(description="Agent唯一标识")
    agent_type: str = Field(description="Agent类型")
    status: Literal["idle", "busy", "error", "offline"] = Field(description="当前状态")
    current_task: Optional[str] = Field(description="当前执行的任务", default=None)
    load_level: float = Field(description="负载水平", ge=0.0, le=1.0)
    last_heartbeat: datetime = Field(description="最后心跳时间")
    performance_metrics: dict = Field(description="性能指标", default_factory=dict)

class TaskExecution(BaseModel):
    """任务执行模型"""
    task_id: str = Field(description="任务唯一标识")
    user_id: str = Field(description="用户ID")
    session_id: str = Field(description="会话ID")
    intent: IntentClassification = Field(description="意图分类结果")
    assigned_agents: List[str] = Field(description="分配的Agent列表")
    execution_plan: List[dict] = Field(description="执行计划")
    status: Literal["pending", "running", "completed", "failed"] = Field(description="执行状态")
    start_time: Optional[datetime] = Field(description="开始时间", default=None)
    end_time: Optional[datetime] = Field(description="结束时间", default=None)
    result: Optional[dict] = Field(description="执行结果", default=None)
    error_info: Optional[str] = Field(description="错误信息", default=None)
```

### 2.3 配置模型

```python
class MultiAgentConfig(BaseModel):
    """多Agent系统配置模型"""
    # 系统配置
    enabled: bool = Field(description="是否启用多Agent系统", default=True)
    max_concurrent_tasks: int = Field(description="最大并发任务数", default=10)
    task_timeout: int = Field(description="任务超时时间（秒）", default=300)
    
    # Agent配置
    max_agents_per_type: int = Field(description="每种类型Agent的最大数量", default=5)
    agent_idle_timeout: int = Field(description="Agent空闲超时时间（秒）", default=600)
    
    # 缓存配置
    cache_enabled: bool = Field(description="是否启用缓存", default=True)
    cache_ttl: int = Field(description="缓存生存时间（秒）", default=3600)
    intent_cache_size: int = Field(description="意图分类缓存大小", default=1000)
    
    # 监控配置
    monitoring_enabled: bool = Field(description="是否启用监控", default=True)
    metrics_retention_days: int = Field(description="指标保留天数", default=30)
    
    # 性能配置
    parallel_execution: bool = Field(description="是否启用并行执行", default=True)
    batch_size: int = Field(description="批处理大小", default=5)
```

## 3. API接口规范

### 3.1 多Agent聊天接口

```python
@router.post("/jiliang/chat/v3")
async def chat_stream_v3(request: Request, body: MultiAgentRagQARequest):
    """
    多Agent聊天接口
    
    Args:
        request: FastAPI请求对象
        body: 扩展的请求体
        
    Returns:
        StreamingResponse: 流式响应
        
    Headers:
        x-api-key: API密钥
        x-username: 用户名
        x-uri: 请求URI
        content-type: application/json
        
    Response Headers:
        Qa-Id: 问答ID
        Agent-Type: 使用的Agent类型
        Execution-Time: 执行时间
    """

class MultiAgentRagQARequest(RagQARequest):
    """扩展的请求模型"""
    # 继承原有字段
    force_route: Optional[Literal["direct", "supervisor", "multi_agent"]] = Field(
        description="强制指定路由类型",
        default=None
    )
    
    agent_preferences: Optional[List[str]] = Field(
        description="首选Agent类型列表",
        default=None
    )
    
    file_uploads: Optional[List[dict]] = Field(
        description="文件上传信息",
        default=None
    )
    
    execution_options: Optional[dict] = Field(
        description="执行选项",
        default_factory=dict
    )
```

### 3.2 Agent管理接口

```python
@router.get("/jiliang/agents/status")
async def get_agents_status():
    """获取所有Agent状态"""
    return {
        "agents": [
            {
                "agent_id": "mcp_expert_001",
                "type": "mcp_expert",
                "status": "idle",
                "load_level": 0.2,
                "last_heartbeat": "2024-01-01T12:00:00Z"
            }
        ],
        "summary": {
            "total_agents": 10,
            "active_agents": 8,
            "busy_agents": 2
        }
    }

@router.post("/jiliang/agents/{agent_type}/scale")
async def scale_agents(agent_type: str, scale_request: ScaleRequest):
    """动态扩缩容Agent"""
    
@router.get("/jiliang/tasks/{task_id}/status")
async def get_task_status(task_id: str):
    """获取任务执行状态"""
```

### 3.3 配置管理接口

```python
@router.get("/jiliang/config")
async def get_config():
    """获取系统配置"""
    
@router.put("/jiliang/config")
async def update_config(config: MultiAgentConfig):
    """更新系统配置"""
    
@router.post("/jiliang/config/reload")
async def reload_config():
    """重新加载配置"""
```

## 4. 性能指标规范

### 4.1 系统性能指标

```python
# Prometheus指标定义
from prometheus_client import Counter, Histogram, Gauge, Summary

# 请求计数器
request_counter = Counter(
    'multi_agent_requests_total',
    'Total number of multi-agent requests',
    ['route_type', 'status']
)

# 响应时间分布
response_time_histogram = Histogram(
    'multi_agent_response_duration_seconds',
    'Multi-agent response duration',
    ['route_type', 'agent_type'],
    buckets=[0.1, 0.5, 1.0, 2.5, 5.0, 10.0, 30.0, 60.0]
)

# 活跃Agent数量
active_agents_gauge = Gauge(
    'multi_agent_active_count',
    'Number of active agents',
    ['agent_type']
)

# 任务执行时间
task_execution_summary = Summary(
    'multi_agent_task_execution_seconds',
    'Task execution time',
    ['task_type', 'complexity']
)

# 错误率
error_rate_counter = Counter(
    'multi_agent_errors_total',
    'Total number of errors',
    ['error_type', 'agent_type']
)
```

### 4.2 业务指标

```python
# 意图分类准确率
intent_accuracy_gauge = Gauge(
    'intent_classification_accuracy',
    'Intent classification accuracy',
    ['model_version']
)

# Agent协作成功率
collaboration_success_rate = Gauge(
    'agent_collaboration_success_rate',
    'Agent collaboration success rate',
    ['agent_combination']
)

# 用户满意度
user_satisfaction_gauge = Gauge(
    'user_satisfaction_score',
    'User satisfaction score',
    ['session_type']
)
```

## 5. 安全性规范

### 5.1 认证和授权

```python
class SecurityConfig(BaseModel):
    """安全配置模型"""
    # API密钥管理
    api_key_required: bool = Field(description="是否需要API密钥", default=True)
    api_key_rotation_interval: int = Field(description="API密钥轮换间隔（天）", default=90)
    
    # 用户权限
    user_role_required: bool = Field(description="是否需要用户角色", default=True)
    allowed_roles: List[str] = Field(description="允许的角色列表", default=["user", "admin"])
    
    # 请求限制
    rate_limit_enabled: bool = Field(description="是否启用速率限制", default=True)
    max_requests_per_minute: int = Field(description="每分钟最大请求数", default=60)
    max_concurrent_sessions: int = Field(description="最大并发会话数", default=10)

# 安全检查装饰器
def security_check(required_roles: List[str] = None):
    """安全检查装饰器"""
    def decorator(func):
        async def wrapper(*args, **kwargs):
            # 验证API密钥
            # 检查用户角色
            # 验证请求频率
            return await func(*args, **kwargs)
        return wrapper
    return decorator
```

### 5.2 数据保护

```python
# 敏感数据脱敏
def sanitize_data(data: dict) -> dict:
    """数据脱敏处理"""
    sensitive_fields = ['password', 'api_key', 'token', 'secret']
    for field in sensitive_fields:
        if field in data:
            data[field] = "***"
    return data

# 数据加密
def encrypt_sensitive_data(data: str) -> str:
    """加密敏感数据"""
    # 使用AES加密
    pass

def decrypt_sensitive_data(encrypted_data: str) -> str:
    """解密敏感数据"""
    # 使用AES解密
    pass
```

## 6. 错误处理规范

### 6.1 错误分类

```python
class MultiAgentError(Exception):
    """多Agent系统基础异常"""
    def __init__(self, message: str, error_code: str, details: dict = None):
        self.message = message
        self.error_code = error_code
        self.details = details or {}
        super().__init__(self.message)

class IntentClassificationError(MultiAgentError):
    """意图分类异常"""
    pass

class AgentExecutionError(MultiAgentError):
    """Agent执行异常"""
    pass

class SupervisorError(MultiAgentError):
    """监督者异常"""
    pass

class ResourceExhaustionError(MultiAgentError):
    """资源耗尽异常"""
    pass
```

### 6.2 错误处理策略

```python
async def error_handler(error: Exception, context: dict) -> dict:
    """统一错误处理器"""
    
    error_response = {
        "success": False,
        "error_code": "UNKNOWN_ERROR",
        "message": "An unexpected error occurred",
        "timestamp": datetime.now().isoformat(),
        "request_id": context.get("request_id")
    }
    
    if isinstance(error, IntentClassificationError):
        error_response.update({
            "error_code": "INTENT_CLASSIFICATION_FAILED",
            "message": "Failed to classify user intent",
            "fallback_action": "redirect_to_single_agent"
        })
    
    elif isinstance(error, AgentExecutionError):
        error_response.update({
            "error_code": "AGENT_EXECUTION_FAILED",
            "message": "Agent execution failed",
            "fallback_action": "retry_with_different_agent"
        })
    
    elif isinstance(error, SupervisorError):
        error_response.update({
            "error_code": "SUPERVISOR_ERROR",
            "message": "Supervisor coordination failed",
            "fallback_action": "fallback_to_direct_routing"
        })
    
    # 记录错误日志
    logger.error(f"Multi-agent error: {error_response}", exc_info=True)
    
    # 更新错误指标
    error_rate_counter.labels(
        error_type=error_response["error_code"],
        agent_type=context.get("agent_type", "unknown")
    ).inc()
    
    return error_response
```

## 7. 测试规范

### 7.1 单元测试

```python
# 测试用例模板
import pytest
from unittest.mock import AsyncMock, patch

class TestIntentClassifier:
    """意图分类器测试"""
    
    @pytest.fixture
    async def classifier(self):
        """测试夹具"""
        return IntentClassifier(mock_llm)
    
    @pytest.mark.asyncio
    async def test_simple_intent_classification(self, classifier):
        """测试简单意图分类"""
        result = await classifier.classify("今天天气怎么样？", {})
        
        assert result.complexity.level == "simple"
        assert result.route_decision == "direct"
        assert result.task_type.primary_type == "general_chat"
    
    @pytest.mark.asyncio
    async def test_complex_intent_classification(self, classifier):
        """测试复杂意图分类"""
        question = "分析这张图片并结合知识库查找相关信息"
        result = await classifier.classify(question, {})
        
        assert result.complexity.level == "multi_modal"
        assert result.route_decision == "multi_agent"
        assert "multimodal_expert" in result.recommended_agents
        assert "knowledge_expert" in result.recommended_agents
```

### 7.2 集成测试

```python
class TestMultiAgentIntegration:
    """多Agent集成测试"""
    
    @pytest.mark.asyncio
    async def test_supervisor_workflow(self):
        """测试监督者工作流"""
        request = MultiAgentRagQARequest(
            question="分析文档并查找MCP工具",
            mcp_ids=["test_mcp"],
            extral_rag_ids=["test_rag"]
        )
        
        result = await multi_agent_router.route_request(request)
        
        assert result is not None
        assert "messages" in result
        assert len(result["messages"]) > 0
    
    @pytest.mark.asyncio
    async def test_error_recovery(self):
        """测试错误恢复机制"""
        # 模拟Agent执行失败
        with patch('multi_agent.specialized_agents.create_mcp_agent') as mock_create:
            mock_create.side_effect = AgentExecutionError("Agent creation failed", "AGENT_ERROR")
            
            request = MultiAgentRagQARequest(
                question="测试错误恢复",
                mcp_ids=["invalid_mcp"]
            )
            
            # 应该自动降级到单Agent模式
            result = await multi_agent_router.route_request(request)
            assert result is not None
```

### 7.3 性能测试

```python
class TestPerformance:
    """性能测试"""
    
    @pytest.mark.asyncio
    async def test_concurrent_requests(self):
        """测试并发请求处理"""
        import asyncio
        import time
        
        requests = [
            MultiAgentRagQARequest(question=f"测试问题{i}")
            for i in range(50)
        ]
        
        start_time = time.time()
        
        results = await asyncio.gather(*[
            multi_agent_router.route_request(req)
            for req in requests
        ])
        
        end_time = time.time()
        duration = end_time - start_time
        
        assert len(results) == 50
        assert duration < 60  # 60秒内完成50个请求
        assert all(result is not None for result in results)
    
    @pytest.mark.asyncio
    async def test_memory_usage(self):
        """测试内存使用情况"""
        import psutil
        import os
        
        process = psutil.Process(os.getpid())
        initial_memory = process.memory_info().rss
        
        # 执行大量请求
        for i in range(100):
            await multi_agent_router.route_request(
                MultiAgentRagQARequest(question=f"测试问题{i}")
            )
        
        final_memory = process.memory_info().rss
        memory_increase = final_memory - initial_memory
        
        # 内存增长不应超过100MB
        assert memory_increase < 100 * 1024 * 1024
```

## 8. 部署规范

### 8.1 Docker配置

```dockerfile
# Dockerfile优化
FROM python:3.11-slim

# 设置工作目录
WORKDIR /app

# 安装系统依赖
RUN apt-get update && apt-get install -y \
    build-essential \
    curl \
    && rm -rf /var/lib/apt/lists/*

# 复制依赖文件
COPY requirements.txt .
COPY requirements-multi-agent.txt .

# 安装Python依赖
RUN pip install --no-cache-dir -r requirements.txt
RUN pip install --no-cache-dir -r requirements-multi-agent.txt

# 复制应用代码
COPY . .

# 设置环境变量
ENV PYTHONPATH=/app
ENV MULTI_AGENT_ENABLED=true

# 健康检查
HEALTHCHECK --interval=30s --timeout=10s --start-period=60s --retries=3 \
    CMD curl -f http://localhost:18800/health || exit 1

# 暴露端口
EXPOSE 18800

# 启动命令
CMD ["python", "rag_chat/jiliang_chat_refactored.py"]
```

### 8.2 Docker Compose配置

```yaml
# docker-compose.multi-agent.yml
version: '3.8'

services:
  jiliang-chat-multi:
    build:
      context: .
      dockerfile: Dockerfile
    ports:
      - "18800:18800"
    environment:
      - MULTI_AGENT_ENABLED=true
      - INTENT_CLASSIFIER_MODEL=gpt-4o
      - SUPERVISOR_MAX_AGENTS=10
      - AGENT_POOL_SIZE=20
    volumes:
      - ./temp:/app/temp
      - ./logs:/app/logs
    depends_on:
      - redis
      - dm-database
    restart: unless-stopped
    
  redis:
    image: redis:7-alpine
    ports:
      - "6379:6379"
    volumes:
      - redis-data:/data
    restart: unless-stopped
    
  dm-database:
    image: dm-database:latest
    ports:
      - "5236:5236"
    environment:
      - DM_PASSWORD=YourPassword
    volumes:
      - dm-data:/opt/dmdbms/data
    restart: unless-stopped
    
  prometheus:
    image: prom/prometheus:latest
    ports:
      - "9090:9090"
    volumes:
      - ./monitoring/prometheus.yml:/etc/prometheus/prometheus.yml
    restart: unless-stopped
    
  grafana:
    image: grafana/grafana:latest
    ports:
      - "3000:3000"
    environment:
      - GF_SECURITY_ADMIN_PASSWORD=admin
    volumes:
      - grafana-data:/var/lib/grafana
    restart: unless-stopped

volumes:
  redis-data:
  dm-data:
  grafana-data:
```

### 8.3 Kubernetes配置

```yaml
# k8s-deployment.yaml
apiVersion: apps/v1
kind: Deployment
metadata:
  name: jiliang-chat-multi-agent
  labels:
    app: jiliang-chat
    version: multi-agent
spec:
  replicas: 3
  selector:
    matchLabels:
      app: jiliang-chat
      version: multi-agent
  template:
    metadata:
      labels:
        app: jiliang-chat
        version: multi-agent
    spec:
      containers:
      - name: jiliang-chat
        image: jiliang-chat:multi-agent-latest
        ports:
        - containerPort: 18800
        env:
        - name: MULTI_AGENT_ENABLED
          value: "true"
        - name: REDIS_HOST
          value: "redis-service"
        - name: DM_HOST
          value: "dm-database-service"
        resources:
          requests:
            memory: "1Gi"
            cpu: "500m"
          limits:
            memory: "2Gi"
            cpu: "1000m"
        livenessProbe:
          httpGet:
            path: /health
            port: 18800
          initialDelaySeconds: 60
          periodSeconds: 30
        readinessProbe:
          httpGet:
            path: /ready
            port: 18800
          initialDelaySeconds: 30
          periodSeconds: 10
---
apiVersion: v1
kind: Service
metadata:
  name: jiliang-chat-service
spec:
  selector:
    app: jiliang-chat
    version: multi-agent
  ports:
  - protocol: TCP
    port: 18800
    targetPort: 18800
  type: LoadBalancer
```

这份技术规范文档详细定义了多智能体监督者架构的各个技术细节，为实施提供了清晰的技术指导。