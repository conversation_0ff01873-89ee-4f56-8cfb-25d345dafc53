# 吉量PDF翻译接口文档

## 概述

本文档描述了吉量PDF翻译的接口规范，系统会自动进行文档版式分析、内容提取和智能翻译，最终返回保留原始文件版式的翻译文件。

## 更新记录

| 版本号 | 修订日期   | 修订人 | 修订说明                                                     |
| ------ | ---------- | ------ | ------------------------------------------------------------ |
| 1.0.0  | 2025-06-19 | 陈锡 | 初稿                                                         |
| 1.0.1  | 2025-06-26 | 陈锡 | 移除SSE查询进度的方式，统一使用http查询。|
| 1.0.2  | 2025-06-26 | 陈锡 | 修正http请求地址正确。|
| 1.0.3  | 2025-07-02 | 陈锡 | 新增进度查询异常状态信息的描述|
| 1.1.0  | 2025-07-25 | 陈锡 | 重构进度通知系统，实现页面级细粒度进度更新，解决进度卡顿问题|

### 公共请求头

| 参数名     | 类型   | 必填 | 描述                             |
| ---------- | ------ | ---- | -------------------------------- |
| X-API-Key  | String | 是   | apikey                           |
| X-Username | String | 是   | 当前操作用户的用户名(行云用户名) |

```python
X-API-Key: <your-api-key>
X-Username: <用户名>
```

### 公共响应头

| 参数名         | 类型   | 必填 | 描述                                                         |
| -------------- | ------ | ---- | ------------------------------------------------------------ |
| X-Ai-Assistant | String | 是   | AI生成内容提示语："本内容由 AI 生成，内容仅供参考，请仔细甄别。"<br />注意内容经过URL编码，前端显示需先行解码。 |

## 错误码

| 错误码 | 描述           |
| ------ | -------------- |
| 200    | 成功           |
| 400    | 请求参数错误   |
| 500    | 服务器内部错误 |

## API端点

## 1. PDF翻译接口

**功能说明**: 上传PDF文件并翻译为指定目标语言。系统使用LangGraph工作流进行智能文档解析和翻译，支持复杂版式和多模态内容处理。采用异步消息队列架构，**支持页面级细粒度进度更新**，任务状态通过消息实时推送，彻底解决翻译阶段进度卡顿问题。

**请求URL**: `/jiliang/translate/pdf`

**请求方法**: POST

**内容类型**: `multipart/form-data`

**请求参数**:

| 参数名          | 类型   | 必填 | 描述                                                       |
| --------------- | ------ | ---- | ---------------------------------------------------------- |
| file            | File   | 是   | 待翻译的PDF文件(表单参数)                                  |
| target_language | String | 否   | 目标语言，默认为"中文"。支持任意语言（如：中文、英文、日文、韩文、法文、德文等） |

**限制说明**:

- 支持的文件格式: PDF(.pdf)
- 最大文件大小: 不超过500MB
- 处理时间: 取决于服务器的实际负载情况，根据文档页数和复杂度，约每页15秒

**请求示例**:

```bash
curl -X POST "https://api.example.com/jiliang/translate/pdf" \
  -H "X-API-Key: your-api-key" \
  -H "X-Username: chen_xi17 (用户的行云用户名)" \
  -F "file=@document.pdf" \
  -F "target_language=英文"
```

**成功响应参数**:

| 参数名           | 类型    | 描述                                 |
| ---------------- | ------- | ------------------------------------ |
| code             | Integer | 状态码，200表示成功                  |
| msg              | String  | 状态信息                             |
| data             | Object  | 响应数据对象                         |
| data.task_id     | String  | 任务ID，用于跟踪处理进度             |
| data.status      | String  | 初始状态："pending"表示等待处理      |
| data.message     | String  | 状态描述                             |
| data.target_language | String | 目标语言                         |
| data.estimated_time | Integer | 预计处理时间（秒）                |
| data.file_name   | String  | 上传的文件名                         |
| data.total_pages | Integer | PDF总页数                            |

**成功响应示例**:

```json
{
  "code": 200,
  "errorCode": null,
  "msg": "success",
  "errorMsg": null,
  "data": {
    "task_id": "a1b2c3d4-e5f6-7890-abcd-ef1234567890",
    "status": "pending",
    "message": "文件验证成功，PDF翻译任务已提交",
    "target_language": "英文",
    "estimated_time": 180,
    "file_name": "document.pdf",
    "total_pages": 12
  }
}
```

**错误响应示例**:

```json
{
  "code": 400,
  "errorCode": null,
  "msg": "不支持的文件类型: image/jpeg，仅支持PDF文件",
  "errorMsg": null,
  "data": null
}
```

**说明**:

- 该接口是异步处理的，返回task_id后，需要使用进度查询接口监听实时进度
- **新特性**: 支持页面级细粒度进度更新，每页VLM分析和翻译完成都会实时推送进度通知

## 2. HTTP获取进度及结果信息

**功能说明**: 通过HTTP接口获取PDF翻译任务的进度及结果信息。

**请求URL**: `/websocket/task/getMsgByTaskId?taskId={taskId}`

**请求方法**: GET

**请求参数**:

| 参数名  | 类型   | 必填 | 描述                         |
| ------- | ------ | ---- | ---------------------------- |
| taskId  | String | 是   | 任务ID，由PDF翻译接口返回    |
| autoAck | Boolean| 否   | 默认值：true。说明：当autoAck=true，获取消息之后，默认删除此条消息；当autoAck=false，此条消息不会⾃动删除，只有autoAck=true时，默认删除 |

**成功响应参数**:

| 参数名           | 类型    | 描述                                         |
| ---------------- | ------- | -------------------------------------------- |
| code             | String  | 请求状态码                                   |
| msg              | String  | 请求说明                                     |
| errorCode        | String  | 异常码                                       |
| errorMsg         | String  | 异常信息                                     |
| data             | Object  | 任务数据                                     |
| data.taskId      | String  | 任务唯一标识                                 |
| data.status      | String  | 任务状态                                     |
| data.totalStep   | Integer | 任务总运行进度（动态计算：页数+3）                               |
| data.currentStep | Integer | 任务当前进度                                 |
| data.progress_percentage | Integer | 任务完成百分比（0-100%）                      |
| data.message     | String  | 任务当前阶段信息                             |
| data.updateTime  | String  | 更新时间                                     |
| data.createTime  | String  | 创建时间                                     |
| data.textResult  | String  | 数据结果                                     |
| data.fileResult  | String  | 文件结果                                     |
| data.taskType    | String  | 任务类型                                     |

## 3. 进度查询接口自定义返回字段介绍

本节将详细介绍任务进度查询接口返回数据中的关键自定义字段，包括 status、currentStep 和 fileResult。

**任务状态字段说明（status，currentStep，progress_percentage）**:

### 📊 新版细粒度进度更新系统

系统现在支持**页面级实时进度更新**，彻底解决了翻译阶段进度卡顿的问题。进度按以下方式分配：

- **初始化阶段 (0-10%)**：文件验证、工作流启动
- **页面处理阶段 (10-85%)**：每页VLM版式分析+翻译，根据页数均分
- **文件生成阶段 (85-95%)**：HTML、PDF文件生成  
- **上传完成阶段 (95-100%)**：文件上传

| status     | current_step | progress_percentage | 描述                                        |
| ---------- | ------------ | ------------------- | ------------------------------------------- |
| pending    | 0            | 0%                  | 文件验证成功，准备开始PDF翻译               |
| processing | 1            | 5%                  | 开始PDF翻译处理                             |
| processing | 2            | 10%                 | 开始逐页翻译PDF文档到目标语言（共X页）      |
| processing | 3~N+1        | 25%, 35%, 45%...   | 已完成第X页VLM版式分析 / 已完成第X页翻译到目标语言 |
| processing | N+2          | 85%                 | PDF翻译完成，正在生成文件                   |
| completed  | N+3          | 100%                | 翻译完成，已生成目标语言版本及相关文件      |
| failed     | 1            | 5%                  | 文件验证失败或翻译处理失败                  |
| failed     | 2            | 50%                 | PDF翻译失败: 翻译内容为空                  |
| failed     | N+3          | 95%                 | 翻译完成但文件上传失败                      |

**注意**:
- `N` = PDF总页数
- `total_step` = 页数 + 3（动态计算）
- 每页处理包含两个阶段：VLM版式分析完成 → 页面翻译完成
- 进度百分比精确到个位数，用户可实时看到每页的处理进度

### 📝 典型进度示例（3页PDF文档）

```
步骤1: 开始PDF翻译处理 (5%)
步骤2: 开始逐页翻译PDF文档到中文（共3页） (10%)
步骤3: 已完成第1页VLM版式分析 (35%)
步骤3: 已完成第1页翻译到中文 (45%)
步骤4: 已完成第2页VLM版式分析 (60%)
步骤4: 已完成第2页翻译到中文 (70%)
步骤5: 已完成第3页VLM版式分析 (80%)
步骤5: 已完成第3页翻译到中文 (85%)
步骤6: PDF翻译完成，正在生成文件 (90%)
步骤6: 翻译完成，已生成中文版本，包含3个文件 (100%)
```


**文件结果字段说明（fileResult）**:

`fileResult` 字段为JSON字符串格式，需要使用JSON解析器进行解析。解析后为**数组（List）**结构，包含**多个文件信息对象（字典）**。

**数组中每个文件信息对象（字典）的字段说明**:

| 字段名    | 类型   | 描述                                 |
| --------- | ------ | ------------------------------------ |
| file_id   | String | 文件ID，用于下载文件的唯一标识       |
| file_name | String | 文件名（如：document_翻译.html）     |
| file_type | String | 文件类型，详见下表                   |

**file_type类型说明**:

| file_type值 | 描述                                                         |
| ----------- | ------------------------------------------------------------ |
| html        | HTML文件，翻译后的网页格式文件，保持原文档布局和样式         |
| pdf         | PDF文件，翻译后的PDF格式文件，保持原文档页面结构             |
| zip         | 压缩包文件，包含原始PDF、翻译文件及原始文档中的图片的完整压缩包          |

**返回文件说明**:

| 文件类型 | 生成情况 | 描述                                                         |
| -------- | -------- | ------------------------------------------------------------ |
| HTML文件 | 必定生成 | 翻译后的网页格式文件，保持原文档的布局和样式，可直接在浏览器中查看 |
| PDF文件  | 必定生成 | 翻译后的PDF格式文件，保持原文档的页面结构 |
| 压缩包文件 | 必定生成 | 包含原始PDF、翻译后的HTML/PDF文件以及提取的原始文档中的图片的完整压缩包 |

**JSON数组示例**:

```json
[
    {
      "file_id": "文件ID",
      "file_name": "document_翻译.html", 
      "file_type": "html"
    },
    {
      "file_id": "文件ID",
      "file_name": "document_翻译.pdf", 
      "file_type": "pdf"
    },
    {
      "file_id": "文件ID",
      "file_name": "document_翻译完整包.zip", 
      "file_type": "zip"
    }
]
```
