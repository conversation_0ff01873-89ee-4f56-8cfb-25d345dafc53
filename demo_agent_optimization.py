#!/usr/bin/env python3
"""
演示AgentManager优化效果的简化脚本

展示优化前后的差异，不需要实际的LLM调用。
"""

import asyncio
import time
import os
import sys
from typing import List, Dict, Any

# 添加项目路径
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

from rag_chat.multi_agent.text_unified_graph import AgentManager


class MockLLM:
    """模拟LLM类，用于测试"""
    def __init__(self, model_name="mock-model"):
        self.model_name = model_name
        # 模拟初始化耗时
        time.sleep(0.1)


class MockAgent:
    """模拟Agent类，用于测试"""
    def __init__(self, agent_type: str, llm, **kwargs):
        self.agent_type = agent_type
        self.llm = llm
        # 模拟agent初始化耗时
        time.sleep(0.2)
        
    async def ainvoke(self, input_data, config=None):
        # 模拟agent执行
        return {"messages": [f"Mock response from {self.agent_type}"]}


class OptimizationDemo:
    """优化效果演示类"""
    
    def __init__(self):
        self.agent_cache = {}  # 模拟AgentManager的缓存
        
    def create_mock_llm(self):
        """创建模拟LLM"""
        return MockLLM()
    
    async def traditional_agent_creation(self, agent_type: str):
        """传统方式：每次都创建新agent"""
        start_time = time.time()
        
        # 每次都创建新的LLM和Agent
        llm = self.create_mock_llm()
        agent = MockAgent(agent_type, llm)
        
        end_time = time.time()
        return agent, end_time - start_time
    
    async def optimized_agent_creation(self, agent_type: str):
        """优化方式：使用缓存"""
        start_time = time.time()
        
        # 检查缓存
        cache_key = f"{agent_type}_cached"
        if cache_key not in self.agent_cache:
            # 首次创建时才有初始化耗时
            llm = self.create_mock_llm()
            agent = MockAgent(agent_type, llm)
            self.agent_cache[cache_key] = agent
            print(f"  🔧 首次创建并缓存 {agent_type} agent")
        else:
            # 从缓存获取，几乎无耗时
            agent = self.agent_cache[cache_key]
            print(f"  ⚡ 从缓存获取 {agent_type} agent")
        
        end_time = time.time()
        return agent, end_time - start_time
    
    async def simulate_multiple_requests(self):
        """模拟多个请求的场景"""
        print("🚀 开始演示Agent创建优化效果\n")
        
        agent_types = ["simple_chat", "knowledge_agent", "mcp_agent"]
        request_count = 3
        
        print("=" * 50)
        print("📊 传统方式：每次请求都重新创建Agent")
        print("=" * 50)
        
        traditional_total_time = 0
        for request_num in range(1, request_count + 1):
            print(f"\n🔄 处理第 {request_num} 个请求:")
            request_start = time.time()
            
            for agent_type in agent_types:
                agent, creation_time = await self.traditional_agent_creation(agent_type)
                print(f"  ⏱️  创建 {agent_type}: {creation_time:.3f}秒")
            
            request_end = time.time()
            request_time = request_end - request_start
            traditional_total_time += request_time
            print(f"  📈 请求总耗时: {request_time:.3f}秒")
        
        print(f"\n🏁 传统方式总耗时: {traditional_total_time:.3f}秒")
        
        print("\n" + "=" * 50)
        print("⚡ 优化方式：使用AgentManager缓存")
        print("=" * 50)
        
        optimized_total_time = 0
        for request_num in range(1, request_count + 1):
            print(f"\n🔄 处理第 {request_num} 个请求:")
            request_start = time.time()
            
            for agent_type in agent_types:
                agent, creation_time = await self.optimized_agent_creation(agent_type)
                print(f"  ⏱️  获取 {agent_type}: {creation_time:.3f}秒")
            
            request_end = time.time()
            request_time = request_end - request_start
            optimized_total_time += request_time
            print(f"  📈 请求总耗时: {request_time:.3f}秒")
        
        print(f"\n🏁 优化方式总耗时: {optimized_total_time:.3f}秒")
        
        # 计算性能提升
        time_saved = traditional_total_time - optimized_total_time
        improvement_percent = (time_saved / traditional_total_time) * 100
        
        print("\n" + "=" * 50)
        print("📊 性能对比结果")
        print("=" * 50)
        print(f"传统方式总耗时: {traditional_total_time:.3f}秒")
        print(f"优化方式总耗时: {optimized_total_time:.3f}秒")
        print(f"节省时间: {time_saved:.3f}秒")
        print(f"性能提升: {improvement_percent:.1f}%")
        
        if improvement_percent > 50:
            print("✅ 优化效果显著！")
        elif improvement_percent > 20:
            print("✅ 优化效果良好！")
        else:
            print("⚠️  优化效果一般")
        
        print("\n💡 优化原理:")
        print("  • 静态Agent（如simple_chat, knowledge_agent）预创建并缓存")
        print("  • 动态Agent（如mcp_agent）使用优化的工厂模式")
        print("  • 避免重复的LLM初始化和Agent构建过程")
        print("  • 在实际项目中，性能提升会更加明显")


async def main():
    """主函数"""
    demo = OptimizationDemo()
    await demo.simulate_multiple_requests()


if __name__ == "__main__":
    asyncio.run(main())
