# 字节编译 / 优化 / DLL 文件
__pycache__/
*.py[cod]

logs/
.idea

.claude/
.mcp.json
CLAUDE.md
.vscode/

*.db

._*

test_*.py

# C 扩展
*.so

# 发布 / 打包
.Python
env/
venv/
build/
develop-eggs/
dist/
downloads/
eggs/
.eggs/
lib/
lib64/
parts/
sdist/
var/
*.egg-info/
.installed.cfg
*.egg

# PyInstaller
# 通常这些文件是由python脚本从模板中写入的
# 在PyInstaller构建exe之前，以便将日期/其他信息注入其中。
*.manifest
*.spec

# 安装程序日志
pip-log.txt
pip-delete-this-directory.txt

# 单元测试 / 覆盖率报告
htmlcov/
.tox/
.nox/
.coverage
.coverage.*
.cache
nosetests.xml
coverage.xml
*.cover
*.py,cover
.hypothesis/
.pytest_cache/

# 翻译文件
*.mo
*.pot

# Django 相关:
*.log
local_settings.py
db.sqlite3
db.sqlite3-journal

# Flask 相关:
instance/
.webassets-cache

# Scrapy 相关:
.scrapy

# Sphinx 文档
docs/_build/
build/doctrees
build/html

# PyBuilder
target/

# Jupyter 笔记本
.ipynb_checkpoints

# pyenv
.python-version

# celery beat 调度文件
celerybeat-schedule

# SageMath 解析文件
*.sage.py

# 环境变量
.envrc
.pyenvrc
.pyre/

# Spyder 项目设置
.spyderproject
.spyproject

# Rope 项目设置
.ropeproject

# mkdocs 文档
/site

# mypy
.mypy_cache/
.dmypy.json
dmypy.json

# Pyre 类型检查器
.pyre/

# pytype 静态类型分析器
.pytype/

# Cython 调试符号
cython_debug/

.DS_Store