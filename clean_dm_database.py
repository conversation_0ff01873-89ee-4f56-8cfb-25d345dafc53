#!/usr/bin/env python3
"""
清理达梦数据库checkpoint数据

删除所有checkpoint和writes数据，解决主键冲突问题。
"""

import sys
import os
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

from rag_chat.checkpoint_dm.dm_util_simple import DMDatabaseSimple

def clean_database():
    """清理数据库checkpoint数据"""
    
    # 数据库配置
    dm_config = {
        'host': '127.0.0.1',
        'port': 5236,
        'user': 'SYSDBA',
        'password': '737498Cx',
        'database': 'TEST_CHECKPOINTER',
        'auto_commit': False
    }
    
    print("🧹 开始清理达梦数据库checkpoint数据...")
    
    try:
        with DMDatabaseSimple(**dm_config) as db:
            print("✅ 连接数据库成功")
            
            # 步骤1：查看当前数据量
            print("\n📊 当前数据统计:")
            try:
                checkpoints_count = db.execute_query("SELECT COUNT(*) as count FROM checkpoints")[0]['count']
                print(f"  checkpoints表: {checkpoints_count} 条记录")
            except Exception as e:
                print(f"  checkpoints表: 不存在或查询失败 ({e})")
                checkpoints_count = 0
            
            try:
                writes_count = db.execute_query("SELECT COUNT(*) as count FROM writes")[0]['count'] 
                print(f"  writes表: {writes_count} 条记录")
            except Exception as e:
                print(f"  writes表: 不存在或查询失败 ({e})")
                writes_count = 0
            
            # 步骤2：清理writes表
            print("\n🗑️  步骤1：清理writes表")
            try:
                deleted_writes = db.execute_update("DELETE FROM writes")
                print(f"✅ 删除了 {deleted_writes} 条writes记录")
            except Exception as e:
                print(f"⚠️  清理writes表跳过: {e}")
            
            # 步骤3：清理checkpoints表
            print("\n🗑️  步骤2：清理checkpoints表")
            try:
                deleted_checkpoints = db.execute_update("DELETE FROM checkpoints")
                print(f"✅ 删除了 {deleted_checkpoints} 条checkpoints记录")
            except Exception as e:
                print(f"⚠️  清理checkpoints表跳过: {e}")
            
            # 步骤4：重置序列（如果存在）
            print("\n🔄 步骤3：重置序列")
            try:
                # 查询所有序列
                sequences = db.execute_query("SELECT SEQUENCE_NAME FROM USER_SEQUENCES")
                for seq in sequences:
                    seq_name = seq['SEQUENCE_NAME']
                    try:
                        db.execute_update(f"DROP SEQUENCE {seq_name}")
                        print(f"✅ 删除序列 {seq_name}")
                    except Exception as e:
                        print(f"⚠️  删除序列 {seq_name} 失败: {e}")
            except Exception as e:
                print(f"⚠️  重置序列跳过: {e}")
            
            # 提交更改
            db.commit()
            print("\n🎉 数据清理完成！")
            
            # 验证清理结果
            print("\n🔍 验证清理结果:")
            try:
                final_checkpoints = db.execute_query("SELECT COUNT(*) as count FROM checkpoints")[0]['count']
                final_writes = db.execute_query("SELECT COUNT(*) as count FROM writes")[0]['count']
                print(f"  checkpoints表: {final_checkpoints} 条记录")
                print(f"  writes表: {final_writes} 条记录")
                
                if final_checkpoints == 0 and final_writes == 0:
                    print("✅ 数据库已完全清理")
                else:
                    print("⚠️  仍有部分数据残留")
                    
            except Exception as e:
                print(f"⚠️  验证失败: {e}")
            
            print("\n✨ 清理完成！现在可以重新运行测试了。")
            
    except Exception as e:
        print(f"\n❌ 清理过程中出现错误: {e}")
        import traceback
        traceback.print_exc()
        return False
    
    return True

if __name__ == "__main__":
    success = clean_database()
    if success:
        print("\n🎯 建议：现在重新运行测试")
        print("python test_checkpoint_recovery.py")
    sys.exit(0 if success else 1)