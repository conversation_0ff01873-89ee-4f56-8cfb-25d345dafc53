#!/usr/bin/env python3
"""
替换 test_text_mode_system.py 中的 test_text_mode_message_streaming 函数

使用 MultiAgentService 进行智能流式输出测试
"""

import asyncio
import json
from langgraph.checkpoint.memory import MemorySaver
from rag_chat.multi_agent_service import MultiAgentService, initialize_multi_agent_service
from rag_chat.models import RagQARequest


async def test_text_mode_message_streaming():
    """测试文本模式消息级流式输出 - 使用 MultiAgentService"""
    
    print("\n💬 开始测试文本模式消息级流式输出...")
    
    try:
        # 1. 初始化 MultiAgentService
        print("📊 初始化 MultiAgentService...")
        await initialize_multi_agent_service(checkpointer=MemorySaver())
        
        # 2. 获取服务实例
        from rag_chat.multi_agent_service import multi_agent_service
        
        # 3. 测试消息级流式查询
        test_question = "请介绍三峡集团的所有业务"
        
        # 4. 创建请求
        request = RagQARequest(
            question=test_question,
            model="deepseek-chat",
            mcp_ids=[],
            extral_rag_ids=[],
            qa_id="test_text_message_streaming"
        )
        
        print(f"📝 消息流测试问题: {test_question}")
        print("💬 开始消息级流式输出...")
        print("=" * 60)
        
        # 5. 跟踪消息流输出
        message_count = 0
        intelligent_events = {
            "tool_actions": [],
            "agent_assignments": [],
            "agent_handoffs": [],
            "task_completions": [],
            "agents_used": set(),
            "tools_used": set()
        }
        
        # 6. 流式处理并显示智能输出
        async for sse_data in multi_agent_service.process_question(
            request=request,
            userid="test_user",
            apikey="test_key", 
            qa_id="test_text_message_streaming",
            show_thinking=False,  # 不显示思考过程，只显示用户友好的输出
            execution_mode="react_mode"  # 使用文本解析模式
        ):
            message_count += 1
            
            try:
                # 解析SSE数据
                sse_json = json.loads(sse_data.split('data: ')[1])
                event_type = sse_json['type']
                event_data = sse_json['data']
                
                # 根据事件类型显示智能输出
                if event_type == "status":
                    print(f"📢 {event_data['message']}")
                
                elif event_type == "tool_action":
                    action = event_data['action']
                    agent_name = event_data['agent_name']
                    tool_name = event_data.get('tool_name', 'unknown')
                    
                    intelligent_events["tool_actions"].append({
                        "agent": agent_name,
                        "tool": tool_name,
                        "action": action
                    })
                    intelligent_events["agents_used"].add(agent_name)
                    intelligent_events["tools_used"].add(tool_name)
                    
                    print(f"🤖 [{agent_name}] {action}")
                
                elif event_type == "agent_assignment":
                    action = event_data['action']
                    agent_name = event_data['agent_name']
                    target_agent = event_data.get('target_agent', 'unknown')
                    
                    intelligent_events["agent_assignments"].append({
                        "from_agent": agent_name,
                        "to_agent": target_agent,
                        "action": action
                    })
                    intelligent_events["agents_used"].add(agent_name)
                    
                    print(f"🤖 [{agent_name}] {action}")
                
                elif event_type == "agent_handoff":
                    action = event_data['action']
                    agent_name = event_data['agent_name']
                    target_agent = event_data.get('target_agent', 'unknown')
                    
                    intelligent_events["agent_handoffs"].append({
                        "from_agent": agent_name,
                        "to_agent": target_agent,
                        "action": action
                    })
                    intelligent_events["agents_used"].add(agent_name)
                    
                    print(f"🤖 [{agent_name}] {action}")
                
                elif event_type == "task_completion":
                    action = event_data['action']
                    agent_name = event_data['agent_name']
                    
                    intelligent_events["task_completions"].append({
                        "agent": agent_name,
                        "action": action
                    })
                    
                    print(f"🤖 [{agent_name}] {action}")
                
                elif event_type == "tool_result_summary":
                    action = event_data['action']
                    agent_name = event_data['agent_name']
                    print(f"🤖 [{agent_name}] {action}")
                
                elif event_type == "text":
                    agent_name = event_data['agent_name']
                    content = event_data['content']
                    
                    # 过滤掉技术性内容，只显示最终回答
                    if not any(marker in content for marker in [
                        '<tool_call>', '<agent_selection>', '<task_complete>', '<handoff>'
                    ]):
                        # 检查是否是新Agent的输出
                        print(content, end="", flush=True)
                
                elif event_type == "completion":
                    print(f"\n\n✅ {event_data['message']}")
                    print(f"📊 执行统计:")
                    print(f"   - 执行模式: {event_data['execution_mode']}")
                    print(f"   - 使用的Agent: {', '.join(event_data.get('agents_used', []))}")
                    print(f"   - 调用的工具: {', '.join(event_data.get('tools_called', []))}")
                    if event_data.get('custom_rag_used'):
                        print(f"   - ✨ 使用了自定义知识库")
                
                elif event_type == "error":
                    print(f"❌ 错误: {event_data}")
                    
            except Exception as parse_error:
                # 如果解析失败，显示原始输出（兼容模式）
                if "🤖" in sse_data:
                    print(sse_data.replace('data: ', '').replace('"', ''), end="", flush=True)
        
        # 7. 显示智能输出统计
        print(f"\n\n📊 智能流式输出统计:")
        print(f"   - 总SSE事件: {message_count}")
        print(f"   - 工具操作: {len(intelligent_events['tool_actions'])}")
        print(f"   - Agent分配: {len(intelligent_events['agent_assignments'])}")
        print(f"   - Agent转交: {len(intelligent_events['agent_handoffs'])}")
        print(f"   - 任务完成: {len(intelligent_events['task_completions'])}")
        print(f"   - 涉及的Agent: {', '.join(intelligent_events['agents_used'])}")
        print(f"   - 使用的工具: {', '.join(intelligent_events['tools_used'])}")
        
        # 8. 对比展示
        print(f"\n🎯 智能输出效果对比:")
        print(f"传统输出: 🤖 [agent] <tool_call>{{\"name\": \"search_knowledge_base\", ...}}</tool_call>")
        print(f"智能输出: 🤖 [knowledge_agent] 🔧 正在使用 知识库搜索...")
        print()
        print(f"传统输出: 🤖 [text_supervisor] <agent_selection>{{\"agent\": \"knowledge_agent\", ...}}</agent_selection>")
        print(f"智能输出: 🤖 [text_supervisor] 📋 将任务分配给 知识专家")
        
        print(f"\n✅ 消息级流式输出测试完成! 共处理 {message_count} 条SSE事件")
        
    except Exception as e:
        print(f"❌ 消息级流式测试失败: {e}")
        import traceback
        traceback.print_exc()


# 如果直接运行此文件，则执行测试
if __name__ == "__main__":
    asyncio.run(test_text_mode_message_streaming())