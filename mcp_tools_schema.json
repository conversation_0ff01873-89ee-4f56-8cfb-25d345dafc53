{"properties": {"context7CompatibleLibraryID": {"type": "string", "description": "Exact Context7-compatible library ID (e.g., '/mongodb/docs', '/vercel/next.js', '/supabase/supabase', '/vercel/next.js/v14.3.0-canary.87') retrieved from 'resolve-library-id' or directly from user query in the format '/org/project' or '/org/project/version'."}, "topic": {"type": "string", "description": "Topic to focus documentation on (e.g., 'hooks', 'routing')."}, "tokens": {"type": "number", "description": "Maximum number of tokens of documentation to retrieve (default: 10000). Higher values provide more context but consume more tokens."}}, "required": ["context7CompatibleLibraryID"]}