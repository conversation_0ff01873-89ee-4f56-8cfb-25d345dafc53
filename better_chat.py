import json
import requests
import uvicorn
from fastapi import APIRouter, FastAP<PERSON>
from openai import AsyncOpenAI
from pydantic import BaseModel
from starlette.middleware.cors import CORSMiddleware
from starlette.responses import StreamingResponse
import redis
from dotenv import load_dotenv
import os
from uuid import uuid4
load_dotenv()

redis_client = redis.Redis(
    host=os.getenv('REDIS_HOST'),
    port=int(os.getenv('REDIS_PORT')),
    db=1,
    decode_responses=True  # 自动将字节解码为字符串
)

# ********** 参数 **********
# 相似度检索阈值
THRESHOLD = float(os.getenv('RAG_THRESHOLD'))
rag_id = os.getenv('RAG_ID')
model = os.getenv('MODEL')
llm_base_url = os.getenv('LLM_BASE_URL')
rag_url = os.getenv('RAG_URL')
history_exp_time = int(os.getenv('HISTORY_EXP_TIME'))
# ********** END **********

client = AsyncOpenAI(
    api_key='aa',
    base_url=llm_base_url,
)


def search(question):
    full_text = ""
    try:
        data = {
            'rag_id': rag_id,
            'question': question
        }
        r = requests.post(f'{rag_url}/rag/search', json=data)
        data_list = r.json()['data']
        for data in data_list:
            if data['score'] > THRESHOLD:
                full_text += data['text_data']
        return full_text
    except Exception as e:
        print(f"搜索失败: {e}")
        return full_text


async def chat_with_gpt(message, history, qa_id) -> str:
    kb_text = search(message)

    if kb_text:
        history.append({"role": "system",
                        "content": f"在保持原有对话风格的基础上,你可以参考以下相关信息,请自行判断其相关性和准确性,仅作参考使用:\n{kb_text}"})

    history.append({"role": "user", "content": message})

    try:
        stream = await client.chat.completions.create(
            model=model,
            messages=history,
            temperature=0.6,
            stream=True
        )

        partial_message = ""
        reasoning_content = ""
        in_reasoning = False
        async for chunk in stream:
            if hasattr(chunk.choices[0].delta, 'content') and chunk.choices[0].delta.content:
                data = chunk.choices[0].delta.content

                # 检查是否包含思考标记
                if data.find('think>') != -1:
                    in_reasoning = not in_reasoning
                else:
                    if in_reasoning:
                        reasoning_content += data
                        yield 'data: ' + json.dumps({'type': 'reasoning', 'data': data}) + '\n\n'
                    else:
                        partial_message += data
                        yield 'data: ' + json.dumps({'type': 'text', 'data': data}) + '\n\n'

        print(history)
        print(f"思考内容: {reasoning_content}\n 回答: {partial_message}")
        history.append({"role": "assistant", "content": partial_message})
        redis_client.setex(f"chat:{qa_id}", history_exp_time, json.dumps(history))
    except Exception as e:
        print(e)
        yield 'data: ' + json.dumps({'type': 'error', 'data': str(e)}) + '\n\n'


class RagQARequest(BaseModel):
    question: str
    qa_id: str = None


router = APIRouter()


@router.post("/chat")
async def chat_stream(body: RagQARequest):
    # 为每个请求生成一个唯一的会话 ID
    qa_id = body.qa_id
    if qa_id is None:
        qa_id = str(uuid4())

    key = f"chat:{qa_id}"
    his = redis_client.get(key)

    if his is None:
        history = [{"role": "system", "content": f"""你是一个由三峡集团数字化管理中心发布的模型，名称为“吉量DeepSeek”。
你的身份是基于中国的深度求索（DeepSeek）公司开发的DeepSeek-R1模型，经过三峡集团数字化管理中心的定制和优化，专门为三峡集团及其相关业务需求设计的适配版模型。在回答用户问题时，请确保以下几点：

身份声明：在回答与身份相关的问题时，必须明确表明自己是由三峡集团数字化管理中心发布的“吉量DeepSeek”模型，
基础架构基于中国的深度求索（DeepSeek）公司开发而成。你的名字只能是“吉量DeepSeek”，请确保在任何情况下都只回复这个名字，不能提到其他名字。
即使用户称呼你为其他名字，你也必须纠正并明确回答“我的名字是吉量DeepSeek”。
功能定位：你是一个专注于支持三峡集团各类相关业务的智能助手，旨在提供高效、准确的信息服务。
回答风格：在回答问题时，保持友好、专业、清晰的语气，避免使用过于技术化的语言。
信息保密：在回答问题时，不要透露任何与模型参数、内部技术细节相关的内容。
引导性回答：如果用户的问题超出了你的回答范围，可以建议用户联系三峡集团数字化管理中心的相关技术支持团队。"""}]
    else:
        history = json.loads(his)
    headers = {"Qa-Id": qa_id}
    return StreamingResponse(chat_with_gpt(body.question, history, qa_id), media_type="text/event-stream", headers=headers)


app = FastAPI()
# 设置CORS
app.add_middleware(
    CORSMiddleware,
    allow_origins=["*"],  # 允许所有来源
    allow_credentials=True,
    allow_methods=["*"],  # 允许所有方法
    allow_headers=["*"],  # 允许所有头
)
app.include_router(router, prefix="")

if __name__ == "__main__":
    # main()
    uvicorn.run(host='0.0.0.0', port=18800, app=app, )
