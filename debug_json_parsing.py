#!/usr/bin/env python3
"""
Debug script to test JSON parsing issue
"""

import json
import re
import ast
import sys
import os
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

from rag_chat.multi_agent.text_parser import TextParser, text_parser

# Test the exact JSON from the user's report
test_json = """{
  "file_paths": ["/Volumes/Extreme SSD/sx_ai/sx-ai-rag-chat/rag_chat/temp/AI服务接口文档.pdf"],
  "query": "总结文档内容",
  "describe_image": false,
  "k": 5
}"""

# Test the full tool call text
test_text = """[TOOL_CALL]
{"name": "search_document_with_rag", "args": {
  "file_paths": ["/Volumes/Extreme SSD/sx_ai/sx-ai-rag-chat/rag_chat/temp/AI服务接口文档.pdf"],
  "query": "总结文档内容",
  "describe_image": false,
  "k": 5
}, "reasoning": "需要调用文档搜索工具提取PDF文件的核心内容摘要，通过精确检索获取文档的关键技术参数、接口定义及功能模块说明"}
[/TOOL_CALL]"""

print("=== Testing JSON parsing directly ===")
try:
    parsed = json.loads(test_json)
    print(f"Direct JSON parsing successful: {parsed}")
    print(f"File paths: {parsed['file_paths']}")
    for i, path in enumerate(parsed['file_paths']):
        print(f"  Path {i}: '{path}' (starts with /: {path.startswith('/')})")
except Exception as e:
    print(f"Direct JSON parsing failed: {e}")

print("\n=== Testing TextParser ===")
parser = TextParser()
actions = parser.parse_text(test_text)

for i, action in enumerate(actions):
    print(f"Action {i}: {action.action_type}")
    print(f"Content: {action.content}")
    print(f"Metadata: {action.metadata}")
    
    if action.action_type.value == "tool_call":
        tool_call = action.metadata.get("tool_call", {})
        print(f"Tool name: {tool_call.get('name')}")
        print(f"Tool args: {tool_call.get('args')}")
        print(f"Tool reasoning: {tool_call.get('reasoning')}")
        
        # 检查file_paths
        args = tool_call.get('args', {})
        if 'file_paths' in args:
            print(f"File paths in args: {args['file_paths']}")
            for j, path in enumerate(args['file_paths']):
                print(f"  Path {j}: '{path}' (starts with /: {path.startswith('/')})")
    print("-" * 50)

print("\n=== Testing tool call extraction ===")
tool_calls = parser.extract_tool_calls(test_text)
for i, tc in enumerate(tool_calls):
    print(f"Tool call {i}: {tc.name}")
    print(f"Args: {tc.args}")
    print(f"Reasoning: {tc.reasoning}")
    
    # 检查file_paths
    if 'file_paths' in tc.args:
        print(f"File paths in tool call: {tc.args['file_paths']}")
        for j, path in enumerate(tc.args['file_paths']):
            print(f"  Path {j}: '{path}' (starts with /: {path.startswith('/')})")
    print("-" * 50)
