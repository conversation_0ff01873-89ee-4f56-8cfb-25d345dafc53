#!/usr/bin/env python3
"""
流式响应调试脚本

专门调试多智能体服务的流式响应问题，找出为什么只有开始、结束事件，没有中间过程。
"""

import asyncio
import json
import sys
import os
from pathlib import Path
from datetime import datetime

# 添加项目根目录到路径
project_root = Path(__file__).parent
sys.path.insert(0, str(project_root))

from dotenv import load_dotenv
from rag_chat.models import MultiAgentQARequest
from rag_chat.multi_agent_service import get_multi_agent_service

# 加载环境变量
load_dotenv(dotenv_path='.env', override=True)

async def debug_streaming_response():
    """调试流式响应过程"""
    
    print("🔍 开始调试多智能体流式响应...")
    
    try:
        # 1. 获取多智能体服务
        service = get_multi_agent_service()
        
        if not service.initialized:
            print("⚠️ 服务未初始化，开始初始化...")
            await service.initialize()
        
        print("✅ 多智能体服务已准备就绪")
        
        # 2. 创建测试请求
        test_request = MultiAgentQARequest(
            question="请简单介绍一下三峡集团的主要业务",
            model="deepseek-chat",
            mcp_ids=[],
            extral_rag_ids=[],
            uploaded_files=[]
        )
        
        print(f"📝 测试问题: {test_request.question}")
        print(f"🎯 执行模式: react_mode")
        print(f"💭 显示思考过程: True")
        
        # 3. 记录所有流式事件
        events = []
        event_count = 0
        
        print("\n🚀 开始流式处理...")
        print("=" * 60)
        
        async for sse_data in service.process_question(
            request=test_request,
            userid="debug_user",
            qa_id="debug_001",
            show_thinking=True,  # 显示思考过程
            execution_mode="react_mode"
        ):
            try:
                # 解析SSE数据
                if sse_data.startswith("data: "):
                    json_str = sse_data[6:].strip()
                    if json_str and json_str != "\n":
                        event_data = json.loads(json_str)
                        event_count += 1
                        
                        # 记录事件
                        timestamp = datetime.now().strftime("%H:%M:%S.%f")[:-3]
                        event_type = event_data.get("type", "unknown")
                        data = event_data.get("data", {})
                        
                        events.append({
                            "timestamp": timestamp,
                            "event_type": event_type,
                            "data": data
                        })
                        
                        # 实时显示事件
                        if event_type == "status":
                            print(f"[{timestamp}] 🔄 状态: {data.get('message', '')}")
                        elif event_type == "agent_start":
                            agent_name = data.get('agent_name', 'unknown')
                            print(f"[{timestamp}] 🤖 Agent启动: {agent_name}")
                        elif event_type == "thinking_start":
                            agent_name = data.get('agent_name', 'unknown')
                            print(f"[{timestamp}] 💭 开始思考: {agent_name}")
                        elif event_type == "thinking":
                            agent_name = data.get('agent_name', 'unknown')
                            content = data.get('content', '')[:50] + "..." if len(data.get('content', '')) > 50 else data.get('content', '')
                            print(f"[{timestamp}] 🧠 思考内容: {agent_name} - {content}")
                        elif event_type == "thinking_complete":
                            agent_name = data.get('agent_name', 'unknown')
                            print(f"[{timestamp}] ✅ 思考完成: {agent_name}")
                        elif event_type == "content":
                            agent_name = data.get('agent_name', 'unknown')
                            content = data.get('content', '')[:50] + "..." if len(data.get('content', '')) > 50 else data.get('content', '')
                            print(f"[{timestamp}] 📝 内容输出: {agent_name} - {content}")
                        elif event_type == "tool_call":
                            agent_name = data.get('agent_name', 'unknown')
                            tool_name = data.get('tool_name', 'unknown')
                            print(f"[{timestamp}] 🔧 工具调用: {agent_name} -> {tool_name}")
                        elif event_type == "tool_result":
                            agent_name = data.get('agent_name', 'unknown')
                            tool_name = data.get('tool_name', 'unknown')
                            print(f"[{timestamp}] 📊 工具结果: {agent_name} <- {tool_name}")
                        elif event_type == "agent_complete":
                            agent_name = data.get('agent_name', 'unknown')
                            print(f"[{timestamp}] 🏁 Agent完成: {agent_name}")
                        elif event_type == "completion":
                            print(f"[{timestamp}] 🎯 处理完成")
                            agents_used = data.get('agents_used', [])
                            tools_called = data.get('tools_called', [])
                            token_usage = data.get('token_usage', {})
                            print(f"              📊 使用的Agent: {agents_used}")
                            print(f"              🔧 调用的工具: {tools_called}")
                            print(f"              💰 Token消耗: {token_usage.get('total_tokens', 0)}")
                        elif event_type == "error":
                            print(f"[{timestamp}] ❌ 错误: {data}")
                        else:
                            print(f"[{timestamp}] 🔍 其他事件: {event_type} - {str(data)[:100]}")
                            
            except json.JSONDecodeError as e:
                print(f"❌ JSON解析错误: {e}")
                print(f"   原始数据: {sse_data[:200]}")
            except Exception as e:
                print(f"❌ 事件处理错误: {e}")
                print(f"   原始数据: {sse_data[:200]}")
        
        print("=" * 60)
        print(f"📊 流式处理完成，共接收 {event_count} 个事件")
        
        # 4. 分析事件分布
        print("\n📈 事件类型统计:")
        event_types = {}
        for event in events:
            event_type = event["event_type"]
            event_types[event_type] = event_types.get(event_type, 0) + 1
        
        for event_type, count in sorted(event_types.items()):
            print(f"   {event_type}: {count}")
        
        # 5. 检查缺失的事件类型
        expected_events = [
            "status", "agent_start", "thinking_start", "thinking", 
            "thinking_complete", "content", "tool_call", "tool_result",
            "agent_complete", "completion"
        ]
        
        missing_events = [e for e in expected_events if e not in event_types]
        if missing_events:
            print(f"\n⚠️ 缺失的事件类型: {missing_events}")
        else:
            print("\n✅ 所有预期的事件类型都已出现")
        
        # 6. 时间线分析
        print(f"\n⏰ 事件时间线:")
        for i, event in enumerate(events[:10]):  # 只显示前10个事件
            timestamp = event["timestamp"]
            event_type = event["event_type"]
            agent_name = event["data"].get("agent_name", "N/A")
            print(f"   {i+1:2d}. [{timestamp}] {event_type:<15} - {agent_name}")
        
        if len(events) > 10:
            print(f"   ... 还有 {len(events) - 10} 个事件")
        
        # 7. 保存详细日志
        log_file = "debug_streaming_events.json"
        with open(log_file, "w", encoding="utf-8") as f:
            json.dump(events, f, ensure_ascii=False, indent=2)
        print(f"\n💾 详细事件日志已保存到: {log_file}")
        
    except Exception as e:
        print(f"❌ 调试过程中发生错误: {e}")
        import traceback
        traceback.print_exc()

if __name__ == "__main__":
    asyncio.run(debug_streaming_response())