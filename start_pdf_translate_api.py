#!/usr/bin/env python3
"""
PDF翻译API本地启动脚本
启动单独的PDF翻译API服务
"""

import os
import sys
import uvicorn
from pathlib import Path
from fastapi import FastAPI
from fastapi.middleware.cors import CORSMiddleware

# 设置项目路径
project_root = Path(__file__).parent
rag_chat_path = project_root / "rag_chat"
sys.path.insert(0, str(rag_chat_path))

# 导入PDF翻译API路由
from pdf_translate.pdf_translate_api import router as pdf_translate_router

def create_app():
    """创建FastAPI应用"""
    
    app = FastAPI(
        title="PDF翻译API",
        description="PDF文档翻译服务",
        version="1.0.0",
        docs_url="/docs",
        redoc_url="/redoc"
    )
    
    # 添加CORS支持
    app.add_middleware(
        CORSMiddleware,
        allow_origins=["*"],
        allow_credentials=True,
        allow_methods=["*"],
        allow_headers=["*"],
    )
    
    # 注册PDF翻译路由
    app.include_router(pdf_translate_router, prefix="", tags=["PDF翻译"])
    
    @app.get("/")
    async def root():
        """根路径"""
        return {
            "message": "PDF翻译API服务",
            "version": "1.0.0",
            "docs": "/docs",
            "endpoints": {
                "translate_pdf": "/jiliang/translate/pdf"
            }
        }
    
    @app.get("/health")
    async def health_check():
        """健康检查"""
        return {"status": "healthy", "service": "pdf_translate_api"}
    
    return app

def main():
    """主函数"""
    
    # 检查环境变量
    if not os.getenv("OPENAI_API_KEY") and not os.getenv("OPENAI_BASE_URL"):
        print("警告: 未设置OPENAI_API_KEY或OPENAI_BASE_URL环境变量")
        print("请确保在.env文件中配置了相关的AI服务配置")
    
    # 检查必要的目录
    temp_dir = os.path.join(os.path.expanduser("~"), "temp")
    if not os.path.exists(temp_dir):
        os.makedirs(temp_dir, exist_ok=True)
        print(f"创建临时目录: {temp_dir}")
    
    # 选择可用端口
    port = 18888
    
    print("启动PDF翻译API服务...")
    print("=" * 50)
    print("服务信息:")
    print(f"  - 地址: http://localhost:{port}")
    print(f"  - API文档: http://localhost:{port}/docs")
    print(f"  - 健康检查: http://localhost:{port}/health")
    print(f"  - 翻译接口: http://localhost:{port}/jiliang/translate/pdf")
    print("=" * 50)
    
    # 创建应用
    app = create_app()
    
    # 启动服务
    uvicorn.run(
        app,
        host="127.0.0.1",
        port=port,
        log_level="info",
        reload=False,
        access_log=True
    )

if __name__ == "__main__":
    main()