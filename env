# 模型
MODEL=deepseek
LONG_MODEL=deepseek-long
#LLM_BASE_URL=http://************:28880/v1
LLM_BASE_URL=http://*************:19111/ai/v1
#LLM_LONG_BASE_URL=http://*************:19111/ai/v1
#LLM_LONG_BASE_URL=http://10.183.15.3:18888/v1
MAX_FILE_CHAT_SIZE=50000
# 72b模型配置
TRANSLATE_MODEL=qwen2---5-72b-goxmbugy
TRANSLATE_AUTH_TOKEN=Bearer 01VvHINDRVQyOGJZWkxWUprH3UVwwDyn
TRANSLATE_BASE_URL=http://*************:19111/v1/
# 图片问答配置
IMAGE_QA_BASE_URL=http://*************:19111/ai/v1
IMAGE_QA_API_KEY=c2b2be0e-358d-4737-82bc-93308c3a9048
IMAGE_QA_MODEL=qwen2-5-vl-32b
IMAGE_QA_TEMPERATURE=0.6
# 平台地址
RAG_URL=http://*************:17880
# 知识库ID
#RAG_ID=67bda540757b77d636e3c90b
RAG_ID=6863d12ebb179141e29fc9a0
# 向量检索阈值
RAG_THRESHOLD=0.2
# 对话历史缓存时间
HISTORY_EXP_TIME=1800
# Redis 集群
#REDIS_MODE=cluster
#REDIS_HOSTS=*************:6379,*************:6379,*************:6379
#REDIS_PASSWORD=CTG@redis
#REDIS_TABLE_JILIANG=sx_jiliang_records
#REDIS_TABLE_WPS=sx_wps_records

# Redis 单机
REDIS_MODE=single
REDIS_HOSTS=*************:6379
REDIS_PASSWORD=CTG@redis
REDIS_DB=4
REDIS_TABLE_JILIANG=sx_jiliang_records
REDIS_TABLE_WPS=sx_wps_records

# redis local test
# REDIS_MODE=single
# REDIS_HOSTS=127.0.0.1:4068
# REDIS_PASSWORD=""
# REDIS_DB=0
# REDIS_TABLE_JILIANG=sx_jiliang_records
# REDIS_TABLE_WPS=sx_wps_records

# Mongodb 集群
#MONGO_MODE=cluster
#MONGO_HOSTS=*************:27017,*************:27017,*************:27017
#MONGO_PORT=27017
#MONGO_USER=admin
#MONGO_PASSWORD=ctg%40mongo%40ai
#MONGO_DB=indexNo
#MONGO_COLLECTION_JILIANG=sx_jiliang_records
#MONGO_COLLECTION_WPS=sx_wps_records
# 达梦
DM_HOST=************
DM_PORT=3529
DM_USER=AIDATA
DM_PASSWORD=HJ9_3Den6Vuy
DM_DB=SX_AIPLATFORM
DM_AUTO_COMMIT=False

# pdf 翻译
OPENAI_API_KEY = 690c65fc-784e-415c-9abf-0ad8c2adc5be
OPENAI_BASE_URL = http://*************:19111/ai/v1
# OPENAI_BASE_URL = http://*************:19111/ai/v1/
# OPENAI_BASE_URL = http://127.0.0.1:1234/v1
OPENAI_MODEL = qwen2---5-72b-goxmbugy

VLLM_API_KEY = 690c65fc-784e-415c-9abf-0ad8c2adc5be
VLLM_BASE_URL = http://*************:19111/ai/v1
VLLM_MODEL = qwen2-5-vl-32b

# RABBITMQ配置
RABBITMQ_HOST=*************
RABBITMQ_PORT=80
RABBITMQ_USERNAME=admin
RABBITMQ_PASSWORD=ctg@rabbitmq@ai
#routing_key规则：部门.业务.类型.版本(topic,direct,fanout)
RABBITMQ_MSG_DEPT=investment
RABBITMQ_MSG_FEATURE=fanout

# langgraph Checkpointer 类型选择
# 可选值: dm (达梦数据库), redis (Redis), memory (内存)
CHECKPOINTER_TYPE=dm

# MCP路由器类型选择
# 可选值: rag (RAG向量化路由), simple (简单API路由)
MCP_ROUTER_TYPE=simple

# MCP Simple Router API配置 (当MCP_ROUTER_TYPE=simple时使用)
MCP_API_BASE_URL=http://*************:19111
MCP-X-API-Key=ca1f47b-58cc-4732-b675-0e79b22dc304
MCP-X-Username=jiliang

# 上下文压缩配置
LANGMEM_ENABLED=true
LANGMEM_MAX_TOKENS=25000
LANGMEM_MAX_TOKENS_BEFORE_SUMMARY=20000
LANGMEM_TEMPERATURE=0.3
LANGMEM_MODEL_BACKEND=openai
LANGMEM_MODEL=qwen2---5-72b-goxmbugy

# langfuse
LANGFUSE_PUBLIC_KEY=pk-lf-d10fcf56-b01d-4bb8-837d-6d9ec79fbd3b
LANGFUSE_SECRET_KEY=******************************************
LANGFUSE_HOST=http://localhost:3000

# 多智能体模型配置-server
SUPERVISOR_MODEL=qwq32b
KNOWLEDGE_MODEL=qwen2---5-72b-goxmbugy
MULTIMODAL_MODEL=qwen2-5-vl-32b
CHAT_MODEL=qwen2---5-72b-goxmbugy
TOOL_MODEL=qwen2---5-72b-goxmbugy
INTENT_MODEL=qwen2---5-72b-goxmbugy
EMBEDDING_MODEL=text-embedding-nomic-embed-text-v1.5
EMBEDDING_BASE_URL=http://************:28882/encode

# 多智能体模型配置-local
# SUPERVISOR_MODEL=qwen/qwen3-4b
# KNOWLEDGE_MODEL=qwen/qwen3-4b
# MULTIMODAL_MODEL=google/gemma-3-12b
# CHAT_MODEL=qwen/qwen3-4b
# TOOL_MODEL=qwen/qwen3-4b
# INTENT_MODEL=qwen/qwen3-4b
# EMBEDDING_MODEL=text-embedding-nomic-embed-text-v1.5
# EMBEDDING_BASE_URL=http://127.0.0.1:1234/v1

ENABLE_TOOLMESSAGE=false
MAX_FILE_CHAT_SIZE=15728640

# 长期记忆配置 (Mem0)
LONGTERM_MEMORY_ENABLED=true
LONGTERM_MEMORY_BASE_URL=http://*************:19111
LONGTERM_MEMORY_API_KEY=ca1f47b-58cc-4732-b675-0e79b22dc304
LONGTERM_MEMORY_USERNAME=jiliang
LONGTERM_MEMORY_TIMEOUT=30
LONGTERM_MEMORY_STORE_THRESHOLD=0.1
LONGTERM_MEMORY_SEARCH_LIMIT=5
LONGTERM_MEMORY_SEARCH_THRESHOLD=0.8

# MILVUS配置
MILVUS_DB_NAME=default
MILVUS_COLLECTION_NAME=T_USER_CHAT_MESSAGE_MEMORIES
MILVUS_URL=http://************:28889
MILVUS_TOKEN=root:ctg@milvus
MILVUS_HOST=************
MILVUS_PORT=28889
MILVUS_EMBEDDING_DIMS=1024