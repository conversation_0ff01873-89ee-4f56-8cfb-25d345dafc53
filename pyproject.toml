[project]
name = "rag-chat"
version = "0.1.0"
description = ""
authors = [
]
readme = "README.md"
requires-python = ">=3.10,<4.0"  # 修改这里，限制Python版本上限为4.0
dependencies = [
    "pymupdf (>=1.25.3,<2.0.0)",
    "uvicorn (>=0.34.0,<0.35.0)",
    "fastapi (>=0.115.8,<0.116.0)",
    "requests (>=2.32.3,<3.0.0)",
    "openai (>=1.64.0,<2.0.0)",
    "python-dotenv",
    "redis (>=5.0.0,<6.0.0)",
    "aiohttp (>=3.11.13,<4.0.0)",
    "pymongo (>=4.11.1,<5.0.0)",
    "openparse (>=0.7.0,<0.8.0)",
    "tiktoken (>=0.9.0,<0.10.0)",
    "python-multipart (>=0.0.20,<0.0.21)",
    "dmpython (>=2.5.8,<3.0.0)",
    "loguru (>=0.7.3,<0.8.0)",
    "docx2txt (>=0.8)",
    "python-docx (>=1.1.0)",
    "langchain-core (>=0.3.66,<0.4.0)",
    "langchain-openai (>=0.3.24,<0.4.0)",
    "langgraph (>=0.4.8,<0.5.0)",
    "langgraph-checkpoint-redis (>=0.0.7,<1.0.0)",
    "langmem (>=0.1.0,<1.0.0)",
    "typing_extensions (>=4.0.0)",
]


[build-system]
requires = ["poetry-core>=2.0.0,<3.0.0"]
build-backend = "poetry.core.masonry.api"
