---
config:
  flowchart:
    curve: linear
---
graph TD;
	__start__([<p>__start__</p>]):::first
	__end__([<p>__end__</p>]):::last
	__start__ --> supervisor___start__;
	knowledge_agent___end__ --> supervisor___start__;
	mcp_agent___end__ --> supervisor___start__;
	supervisor___end__ -.-> __end__;
	supervisor___end__ -.-> knowledge_agent___start__;
	supervisor___end__ -.-> mcp_agent___start__;
	subgraph supervisor
	supervisor___start__(<p>__start__</p>)
	supervisor_agent(agent)
	supervisor_tools(tools)
	supervisor___end__(<p>__end__</p>)
	supervisor___start__ --> supervisor_agent;
	supervisor_agent -.-> supervisor___end__;
	supervisor_agent -.-> supervisor_tools;
	supervisor_tools --> supervisor_agent;
	end
	subgraph knowledge_agent
	knowledge_agent___start__(<p>__start__</p>)
	knowledge_agent_agent(agent)
	knowledge_agent_tools(tools)
	knowledge_agent_pre_model_hook(pre_model_hook)
	knowledge_agent___end__(<p>__end__</p>)
	knowledge_agent___start__ --> knowledge_agent_pre_model_hook;
	knowledge_agent_agent -.-> knowledge_agent___end__;
	knowledge_agent_agent -.-> knowledge_agent_tools;
	knowledge_agent_pre_model_hook --> knowledge_agent_agent;
	knowledge_agent_tools --> knowledge_agent_pre_model_hook;
	end
	subgraph mcp_agent
	mcp_agent___start__(<p>__start__</p>)
	mcp_agent_agent(agent)
	mcp_agent_tools(tools)
	mcp_agent_pre_model_hook(pre_model_hook)
	mcp_agent___end__(<p>__end__</p>)
	mcp_agent___start__ --> mcp_agent_pre_model_hook;
	mcp_agent_agent -.-> mcp_agent___end__;
	mcp_agent_agent -.-> mcp_agent_tools;
	mcp_agent_pre_model_hook --> mcp_agent_agent;
	mcp_agent_tools --> mcp_agent_pre_model_hook;
	end
	classDef default fill:#f2f0ff,line-height:1.2
	classDef first fill-opacity:0
	classDef last fill:#bfb6fc
