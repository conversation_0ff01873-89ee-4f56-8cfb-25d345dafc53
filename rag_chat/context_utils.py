"""
上下文工具模块
提供用户上下文管理功能
"""

import threading
from typing import Dict, Any, Optional

# 线程本地存储用户上下文
_local = threading.local()

def set_current_user_context(userid: str, qa_id: str = "", **kwargs):
    """设置当前用户上下文"""
    context = {
        "userid": userid,
        "qa_id": qa_id,
        **kwargs
    }
    _local.user_context = context

def get_current_user_context() -> Optional[Dict[str, Any]]:
    """获取当前用户上下文"""
    return getattr(_local, 'user_context', None)

def clear_current_user_context():
    """清除当前用户上下文"""
    if hasattr(_local, 'user_context'):
        delattr(_local, 'user_context')