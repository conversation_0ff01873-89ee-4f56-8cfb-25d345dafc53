---
config:
  flowchart:
    curve: linear
---
graph TD;
	__start__([<p>__start__</p>]):::first
	message_cleaner(message_cleaner)
	global_context_summarization(global_context_summarization)
	intent_analyzer(intent_analyzer)
	simple_chat(simple_chat)
	knowledge_agent(knowledge_agent)
	mcp_agent(mcp_agent)
	multimodal_agent(multimodal_agent)
	custom_rag_agent(custom_rag_agent)
	supervisor(supervisor)
	parallel_coordinator(parallel_coordinator)
	__end__([<p>__end__</p>]):::last
	__start__ --> message_cleaner;
	global_context_summarization --> intent_analyzer;
	intent_analyzer -.-> custom_rag_agent;
	intent_analyzer -.-> knowledge_agent;
	intent_analyzer -.-> mcp_agent;
	intent_analyzer -.-> multimodal_agent;
	intent_analyzer -.-> parallel_coordinator;
	intent_analyzer -.-> simple_chat;
	intent_analyzer -.-> supervisor;
	message_cleaner --> global_context_summarization;
	custom_rag_agent --> __end__;
	knowledge_agent --> __end__;
	mcp_agent --> __end__;
	multimodal_agent --> __end__;
	parallel_coordinator --> __end__;
	simple_chat --> __end__;
	supervisor --> __end__;
	classDef default fill:#f2f0ff,line-height:1.2
	classDef first fill-opacity:0
	classDef last fill:#bfb6fc
