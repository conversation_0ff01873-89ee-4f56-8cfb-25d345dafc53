"""
有界缓存管理器

解决无界增长的缓冲区和去重集合导致的内存泄漏问题。
"""

import time
import threading
from collections import OrderedDict
from typing import Dict, Any, Optional, Set
from rag_chat.logger import logger


class BoundedMessageBuffer:
    """
    带TTL（生存时间）的有界消息缓冲区
    
    特性：
    - 最大容量限制，防止内存无限增长
    - TTL过期清理，自动清理过期数据
    - LRU淘汰策略，优先清理最久未使用的数据
    - 线程安全
    """
    
    def __init__(self, max_size: int = 1000, ttl: int = 3600):
        """
        初始化有界缓冲区
        
        Args:
            max_size: 最大缓存条目数量
            ttl: 生存时间（秒）
        """
        self.max_size = max_size
        self.ttl = ttl
        self._cache: OrderedDict[str, tuple[Any, float]] = OrderedDict()
        self._lock = threading.RLock()
        self._last_cleanup = time.time()
        self._cleanup_interval = 300  # 5分钟清理一次
        
        logger.info(f"初始化有界消息缓冲区: max_size={max_size}, ttl={ttl}s")
    
    def get(self, key: str, default: Any = None) -> Any:
        """获取缓存值"""
        with self._lock:
            self._maybe_cleanup()
            
            if key in self._cache:
                value, timestamp = self._cache[key]
                
                # 检查是否过期
                if time.time() - timestamp <= self.ttl:
                    # 更新访问顺序（LRU）
                    self._cache.move_to_end(key)
                    return value
                else:
                    # 过期，删除
                    del self._cache[key]
            
            return default
    
    def set(self, key: str, value: Any) -> None:
        """设置缓存值"""
        with self._lock:
            current_time = time.time()
            
            # 如果键已存在，更新值和时间戳
            if key in self._cache:
                self._cache[key] = (value, current_time)
                self._cache.move_to_end(key)
            else:
                # 新键，检查容量
                if len(self._cache) >= self.max_size:
                    # 达到最大容量，移除最旧的项
                    oldest_key = next(iter(self._cache))
                    del self._cache[oldest_key]
                    logger.debug(f"缓冲区达到最大容量，移除最旧项: {oldest_key}")
                
                self._cache[key] = (value, current_time)
            
            self._maybe_cleanup()
    
    def pop(self, key: str, default: Any = None) -> Any:
        """弹出并删除缓存值"""
        with self._lock:
            if key in self._cache:
                value, timestamp = self._cache.pop(key)
                
                # 检查是否过期
                if time.time() - timestamp <= self.ttl:
                    return value
                else:
                    return default
            
            return default
    
    def clear(self) -> None:
        """清空所有缓存"""
        with self._lock:
            old_size = len(self._cache)
            self._cache.clear()
            logger.debug(f"清空消息缓冲区，原有 {old_size} 项")
    
    def _maybe_cleanup(self) -> None:
        """如果需要，执行过期清理"""
        current_time = time.time()
        if current_time - self._last_cleanup > self._cleanup_interval:
            self._cleanup_expired()
            self._last_cleanup = current_time
    
    def _cleanup_expired(self) -> None:
        """清理过期项"""
        current_time = time.time()
        expired_keys = []
        
        for key, (value, timestamp) in self._cache.items():
            if current_time - timestamp > self.ttl:
                expired_keys.append(key)
        
        for key in expired_keys:
            del self._cache[key]
        
        if expired_keys:
            logger.debug(f"清理了 {len(expired_keys)} 个过期缓存项")
    
    def get_stats(self) -> Dict[str, Any]:
        """获取缓存统计信息"""
        with self._lock:
            return {
                "current_size": len(self._cache),
                "max_size": self.max_size,
                "ttl": self.ttl,
                "usage_ratio": len(self._cache) / self.max_size if self.max_size > 0 else 0
            }


class BoundedSet:
    """
    带TTL的有界集合，用于去重
    
    特性：
    - 最大容量限制
    - TTL过期清理
    - 线程安全
    """
    
    def __init__(self, max_size: int = 5000, ttl: int = 1800):
        """
        初始化有界集合
        
        Args:
            max_size: 最大元素数量
            ttl: 生存时间（秒），默认30分钟
        """
        self.max_size = max_size
        self.ttl = ttl
        self._data: OrderedDict[str, float] = OrderedDict()
        self._lock = threading.RLock()
        self._last_cleanup = time.time()
        self._cleanup_interval = 300  # 5分钟清理一次
        
        logger.info(f"初始化有界去重集合: max_size={max_size}, ttl={ttl}s")
    
    def add(self, item: str) -> None:
        """添加元素到集合"""
        with self._lock:
            current_time = time.time()
            
            # 如果元素已存在，更新时间戳
            if item in self._data:
                self._data[item] = current_time
                self._data.move_to_end(item)
            else:
                # 新元素，检查容量
                if len(self._data) >= self.max_size:
                    # 达到最大容量，移除最旧的项
                    oldest_item = next(iter(self._data))
                    del self._data[oldest_item]
                    logger.debug(f"去重集合达到最大容量，移除最旧项: {oldest_item}")
                
                self._data[item] = current_time
            
            self._maybe_cleanup()
    
    def __contains__(self, item: str) -> bool:
        """检查元素是否在集合中"""
        with self._lock:
            self._maybe_cleanup()
            
            if item in self._data:
                timestamp = self._data[item]
                
                # 检查是否过期
                if time.time() - timestamp <= self.ttl:
                    # 更新访问顺序
                    self._data.move_to_end(item)
                    return True
                else:
                    # 过期，删除
                    del self._data[item]
                    return False
            
            return False
    
    def clear(self) -> None:
        """清空集合"""
        with self._lock:
            old_size = len(self._data)
            self._data.clear()
            logger.debug(f"清空去重集合，原有 {old_size} 项")
    
    def _maybe_cleanup(self) -> None:
        """如果需要，执行过期清理"""
        current_time = time.time()
        if current_time - self._last_cleanup > self._cleanup_interval:
            self._cleanup_expired()
            self._last_cleanup = current_time
    
    def _cleanup_expired(self) -> None:
        """清理过期项"""
        current_time = time.time()
        expired_items = []
        
        for item, timestamp in self._data.items():
            if current_time - timestamp > self.ttl:
                expired_items.append(item)
        
        for item in expired_items:
            del self._data[item]
        
        if expired_items:
            logger.debug(f"清理了 {len(expired_items)} 个过期去重项")
    
    def get_stats(self) -> Dict[str, Any]:
        """获取集合统计信息"""
        with self._lock:
            return {
                "current_size": len(self._data),
                "max_size": self.max_size,
                "ttl": self.ttl,
                "usage_ratio": len(self._data) / self.max_size if self.max_size > 0 else 0
            }


class ResourceManager:
    """
    资源管理器，统一管理所有有界资源
    """
    
    def __init__(self):
        self.message_buffers = BoundedMessageBuffer(max_size=1000, ttl=3600)  # 1小时TTL
        self.processed_messages = BoundedSet(max_size=5000, ttl=1800)  # 30分钟TTL
        self._lock = threading.Lock()
        
        logger.info("资源管理器初始化完成")
    
    def get_message_buffer(self) -> BoundedMessageBuffer:
        """获取消息缓冲区"""
        return self.message_buffers
    
    def get_processed_messages(self) -> BoundedSet:
        """获取已处理消息集合"""
        return self.processed_messages
    
    def clear_all(self) -> None:
        """清空所有资源"""
        with self._lock:
            self.message_buffers.clear()
            self.processed_messages.clear()
            logger.info("已清空所有资源管理器缓存")
    
    def get_memory_stats(self) -> Dict[str, Any]:
        """获取内存使用统计"""
        return {
            "message_buffers": self.message_buffers.get_stats(),
            "processed_messages": self.processed_messages.get_stats()
        }


# 全局资源管理器实例
resource_manager = ResourceManager()


def get_resource_manager() -> ResourceManager:
    """获取全局资源管理器实例"""
    return resource_manager