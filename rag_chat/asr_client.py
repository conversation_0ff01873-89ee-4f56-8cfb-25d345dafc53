"""
ASR语音识别客户端
"""

import json
import base64
import asyncio
import aiohttp
import ssl
from typing import Dict, Any, Optional
import logging

logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)


class ASRClient:
    """ASR语音识别客户端"""
    
    def __init__(self, username: str = "username", password: str = "password"):
        self.username = username
        self.password = password
        
        # 短语音配置
        self.short_base_url = "https://aimp.ctg.com.cn"
        self.short_token = "56F2BC3A27502FA135FE2E6CFB466E98"
        self.short_result_url = f"{self.short_base_url}/Others/yysb3fls"
        
        # 长语音配置
        self.long_token = "E75C9B11D88FD510AF55FC2541EC2F65"
        self.long_transcribe_url = f"{self.short_base_url}/Others/asr/transcribe-audio"
        
        # 固定的默认参数
        self.default_product_id = "1912"
        self.default_username = "username"
        self.default_password = "password"
    
    def _create_ssl_context(self):
        """创建SSL上下文"""
        ssl_context = ssl.create_default_context()
        # ssl_context.check_hostname = False
        # ssl_context.verify_mode = ssl.CERT_NONE
        return ssl_context
    
    async def recognize_short_audio(self, audio_data: bytes, product_id: str = None) -> Dict[str, Any]:
        """短语音非流式识别"""
        try:
            audio_base64 = base64.b64encode(audio_data).decode('utf-8')
            
            # 使用固定的默认参数
            payload = {
                "productId": product_id or self.default_product_id,
                "userName": self.default_username,
                "password": self.default_password,
                "data": audio_base64
            }
            
            headers = {
                "Authorization": f"ACCESSCODE {self.short_token}",
                "Content-Type": "application/json"
            }
            
            logger.info(f"开始短语音ASR识别，音频大小: {len(audio_data)} bytes")
            
            # 创建SSL上下文
            ssl_context = self._create_ssl_context()
            
            # 创建连接器，配置SSL和连接参数
            connector = aiohttp.TCPConnector(
                ssl=ssl_context,
                limit=100,
                limit_per_host=30,
                ttl_dns_cache=300,
                use_dns_cache=True,
                keepalive_timeout=30,
                enable_cleanup_closed=True
            )
            
            timeout = aiohttp.ClientTimeout(
                total=120,  # 增加总超时时间
                connect=30,  # 连接超时
                sock_read=60  # 读取超时
            )
            
            async with aiohttp.ClientSession(
                connector=connector,
                timeout=timeout
            ) as session:
                try:
                    async with session.post(
                        self.short_result_url,
                        json=payload,
                        headers=headers
                    ) as response:
                        result = await response.json()
                        
                        if response.status == 200:
                            logger.info("短语音ASR识别成功")
                            return {
                                "success": True,
                                "data": result,
                                "message": "识别成功"
                            }
                        else:
                            logger.error(f"短语音ASR识别失败，状态码: {response.status}")
                            return {
                                "success": False,
                                "message": f"HTTP错误: {response.status}",
                                "data": result
                            }
                except (aiohttp.ClientConnectorError, aiohttp.ClientError) as e:
                    logger.error(f"连接错误: {e}")
                    return {
                        "success": False,
                        "message": f"连接错误: {str(e)}",
                        "error": str(e)
                    }
                except asyncio.TimeoutError as e:
                    logger.error(f"请求超时: {e}")
                    return {
                        "success": False,
                        "message": f"请求超时: {str(e)}",
                        "error": str(e)
                    }
                    
        except Exception as e:
            logger.error(f"短语音ASR识别异常: {e}")
            return {
                "success": False,
                "message": f"识别异常: {str(e)}",
                "error": str(e)
            }
    
    async def transcribe_audio(self, audio_data: bytes,
                              identify_speakers: bool = False,
                              speakers_num: int = 2,
                              is_correct_text: bool = False,
                              speaker_summary_needed: bool = False) -> Dict[str, Any]:
        """音频转写服务"""
        try:
            # 构建查询参数
            params = {
                "identify_speakers": str(identify_speakers).lower(),
                "speakers_num": speakers_num,
                "is_correct_text": str(is_correct_text).lower(),
                "speaker_summary_needed": str(speaker_summary_needed).lower()
            }
            
            logger.info(f"开始音频转写，音频大小: {len(audio_data)} bytes")
            logger.info(f"请求URL: {self.long_transcribe_url}")
            logger.info(f"请求参数: {params}")
            logger.info(f"认证token: {self.long_token}")
            
            # 创建SSL上下文
            ssl_context = self._create_ssl_context()
            
            # 创建连接器，配置SSL和连接参数
            connector = aiohttp.TCPConnector(
                ssl=ssl_context,
                limit=100,
                limit_per_host=30,
                ttl_dns_cache=300,
                use_dns_cache=True,
                keepalive_timeout=30,
                enable_cleanup_closed=True
            )
            
            timeout = aiohttp.ClientTimeout(
                total=300,  # 5分钟总超时时间
                connect=30,  # 连接超时
                sock_read=240  # 读取超时
            )
            
            headers = {
                "Authorization": f"ACCESSCODE {self.long_token}"
            }
            
            # 准备multipart/form-data
            form_data = aiohttp.FormData()
            form_data.add_field('file', audio_data, filename='audio.wav', content_type='audio/wav')
            
            async with aiohttp.ClientSession(
                connector=connector,
                timeout=timeout
            ) as session:
                try:
                    async with session.post(
                        self.long_transcribe_url,
                        params=params,
                        headers=headers,
                        data=form_data
                    ) as response:
                        logger.info(f"响应状态码: {response.status}")
                        logger.info(f"响应Content-Type: {response.headers.get('content-type', 'unknown')}")
                        
                        response_text = await response.text()
                        logger.debug(f"响应内容: {response_text[:500]}...")
                        
                        try:
                            result = json.loads(response_text)
                        except json.JSONDecodeError as e:
                            logger.error(f"JSON解析失败: {e}")
                            return {
                                "success": False,
                                "message": f"响应格式错误，状态码: {response.status}",
                                "error": f"JSON解析失败: {str(e)}",
                                "raw_response": response_text[:1000]  # 只保留前1000个字符
                            }
                        
                        if response.status == 200:
                            logger.info("音频转写成功")
                            return {
                                "success": True,
                                "data": result,
                                "message": "转写成功"
                            }
                        else:
                            logger.error(f"音频转写失败，状态码: {response.status}")
                            return {
                                "success": False,
                                "message": f"HTTP错误: {response.status}",
                                "data": result,
                                "raw_response": response_text[:1000]
                            }
                except (aiohttp.ClientConnectorError, aiohttp.ClientError) as e:
                    logger.error(f"连接错误: {e}")
                    return {
                        "success": False,
                        "message": f"连接错误: {str(e)}",
                        "error": str(e)
                    }
                except asyncio.TimeoutError as e:
                    logger.error(f"请求超时: {e}")
                    return {
                        "success": False,
                        "message": f"请求超时: {str(e)}",
                        "error": str(e)
                    }
                    
        except Exception as e:
            logger.error(f"音频转写异常: {e}")
            return {
                "success": False,
                "message": f"转写异常: {str(e)}",
                "error": str(e)
            }
    
    async def recognize_file(self, file_path: str, is_long_audio: bool = False, **kwargs):
        """识别音频文件"""
        try:
            with open(file_path, 'rb') as f:
                audio_data = f.read()
            
            if is_long_audio:
                # 使用新的转写接口
                result = await self.transcribe_audio(audio_data, **kwargs)
                return {
                    "success": result["success"],
                    "mode": "transcribe_audio",
                    "result": result,
                    "transcription_text": result.get("data", {}).get("transcription_text", "") if result["success"] else ""
                }
            else:
                result = await self.recognize_short_audio(audio_data, **kwargs)
                return {
                    "success": result["success"],
                    "mode": "short_audio",
                    "result": result
                }
                
        except Exception as e:
            logger.error(f"文件识别失败: {e}")
            return {
                "success": False,
                "message": f"文件识别失败: {str(e)}",
                "error": str(e)
            }



async def test_asr():
    """测试ASR功能"""
    client = ASRClient()
    
    print("=== 测试短语音识别 ===")
    with open("test_audio.wav", "rb") as f:
        audio_data = f.read()
    
    result = await client.recognize_short_audio(audio_data)
    if result["success"]:
        print("短语音识别成功:")
        print(json.dumps(result["data"], indent=2, ensure_ascii=False))
    else:
        print(f"短语音识别失败: {result['message']}")
    
    print("\n=== 测试音频转写服务 ===")
    result = await client.transcribe_audio(
        audio_data,
        identify_speakers=True,
        speakers_num=2,
        is_correct_text=False,
        speaker_summary_needed=False
    )
    
    if result["success"]:
        print("音频转写成功:")
        print(json.dumps(result["data"], indent=2, ensure_ascii=False))
        print(f"转写文本: {result['data'].get('transcription_text', '')}")
    else:
        print(f"音频转写失败: {result['message']}")


if __name__ == "__main__":
    asyncio.run(test_asr())