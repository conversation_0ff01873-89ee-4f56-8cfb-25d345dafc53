from typing import TypedDict, List, Dict, Annotated, Literal, Optional, Any
import operator

def extend_field(existing: list, update: list | str):
    if update == "DELETE":
        return []
    return (existing or []) + update if isinstance(update, list) else (existing or []) + [update]

class PageAnalysisState(TypedDict):
    """单页分析状态"""
    page_index: int
    page_image_path: str
    rect_images: List[str]
    region_info: Dict[int, Dict[str, str]]
    region_bboxes: List[tuple]
    extracted_content: str
    translated_content: str

class PDFWorkflowState(TypedDict):
    """PDF工作流状态"""
    pdf_path: str
    output_dir: str
    file_name_without_ext: str
    use_vlm_layout: bool
    verbose: bool
    enable_pdf_translation: bool
    language: str
    
    # 页面分析相关
    vlm_scale_factor: float
    image_format: Literal["png", "jpeg"]  # VLM分析时使用的图像格式
    # bbox扩展配置
    enable_intelligent_bbox_expansion: bool  # 是否启用智能bbox扩展
    bbox_overlap_merge_threshold: float  # 重叠区域合并阈值
    enable_complex_table_optimization: bool  # 是否启用复杂表格优化
    
    total_pages: int
    pages_info: List[Dict]  # 每页的基本信息
    # page_analysis_results: Annotated[List[PageAnalysisState], extend_field]  # 已移除，流水线架构不需要中间状态
    page_analysis_results_translated: Annotated[List[PageAnalysisState], extend_field]  # 所有页面的完整处理结果（分析+翻译）
    
    # 批处理并发控制相关
    max_concurrent: Optional[int]  # 最大并发数
    remaining_page_indices: List[int]  # 待处理页面索引列表
    all_completed_pages: List[PageAnalysisState]  # 所有已完成页面结果
    
    # 最终结果
    final_content: str
    all_rect_images: List[str]
    translated_pdf_path: str
    
    # 进度回调相关（可选）- 注意：不存储函数对象以避免序列化问题
    # progress_callback: Optional[Callable]  # 进度回调函数 - 已移除避免序列化问题
    callback_total_pages: Optional[int]  # 用于进度计算的总页数 