from dataclasses import dataclass
from typing import Dict, Any, Literal
import os

@dataclass
class PDFWorkflowConfig:
    """PDF工作流配置类"""
    
    # VLM配置
    use_vlm_layout: bool = True
    vlm_scale_factor: float = 1.5
    image_format: Literal["png", "jpeg"] = "jpeg"  # VLM分析时使用的图像格式
    
    # 智能bbox扩展配置
    enable_intelligent_bbox_expansion: bool = False
    bbox_overlap_merge_threshold: float = 1.0
    enable_complex_table_optimization: bool = False
    
    # PDF翻译配置
    enable_pdf_translation: bool = True
    
    # 输出配置
    verbose: bool = True
    
    # 环境变量检查
    required_env_vars: Dict[str, str] = None
    
    def __post_init__(self):
        if self.required_env_vars is None:
            self.required_env_vars = {
                "VLLM_MODEL": "VLM模型名称",
                "VLLM_API_KEY": "VLM API密钥", 
                "VLLM_BASE_URL": "VLM API基础URL",
                "OPENAI_MODEL": "OpenAI模型名称",
                "OPENAI_API_KEY": "OpenAI API密钥",
                "OPENAI_BASE_URL": "OpenAI API基础URL",
                "LANGFUSE_PUBLIC_KEY": "Langfuse公钥",
                "LANGFUSE_SECRET_KEY": "Langfuse私钥", 
                "LANGFUSE_HOST": "Langfuse主机地址"
            }
    
    def check_environment(self) -> Dict[str, bool]:
        """检查环境变量是否设置"""
        env_status = {}
        for var_name, description in self.required_env_vars.items():
            env_status[var_name] = bool(os.getenv(var_name))
        return env_status
    
    def get_missing_env_vars(self) -> list:
        """获取缺失的环境变量"""
        env_status = self.check_environment()
        return [var for var, status in env_status.items() if not status]
    
    def is_vlm_available(self) -> bool:
        """检查VLM相关环境变量是否可用"""
        vlm_vars = ["VLLM_MODEL", "VLLM_API_KEY", "VLLM_BASE_URL"]
        return all(os.getenv(var) for var in vlm_vars)
    
    def is_translation_available(self) -> bool:
        """检查翻译相关环境变量是否可用"""
        translation_vars = ["OPENAI_MODEL", "OPENAI_API_KEY"]
        return all(os.getenv(var) for var in translation_vars)
    
    def to_workflow_state_dict(self, pdf_path: str, 
                               output_dir: str, 
                               file_name_without_ext: str,
                               language: str) -> Dict[str, Any]:
        """转换为PDFWorkflowState所需的字典格式"""
        return {
            "pdf_path": pdf_path,
            "output_dir": output_dir,
            "file_name_without_ext": file_name_without_ext,
            "use_vlm_layout": self.use_vlm_layout and self.is_vlm_available(),
            "verbose": self.verbose,
            "enable_pdf_translation": self.enable_pdf_translation and self.is_translation_available(),
            "language": language,
            "vlm_scale_factor": self.vlm_scale_factor,
            "image_format": self.image_format,
            "enable_intelligent_bbox_expansion": self.enable_intelligent_bbox_expansion,
            "bbox_overlap_merge_threshold": self.bbox_overlap_merge_threshold,
            "enable_complex_table_optimization": self.enable_complex_table_optimization,
            "total_pages": 0,
            "pages_info": [],
            "page_analysis_results": [],
            "final_content": "",
            "all_rect_images": [],
            "translated_pdf_path": ""
        }

# 默认配置实例
DEFAULT_CONFIG = PDFWorkflowConfig()