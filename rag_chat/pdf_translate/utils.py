from PIL import Image
from typing import Tuple, List, Optional, Dict
import fitz
from shapely.geometry.base import BaseGeometry
from shapely.validation import explain_validity
import shapely.geometry as sg

# 导入项目统一的logger
import sys
from pathlib import Path
project_root = Path(__file__).parent.parent
sys.path.append(str(project_root))
from logger import logger as logging

MIN_IMAGE_SIZE = 28
TARGET_MIN_SIZE = 28 

# 图片处理相关工具
def _check_image_size(image_path: str, min_size: int = MIN_IMAGE_SIZE) -> Tuple[bool, Tuple[int, int]]:
    """检查图片尺寸是否满足模型要求"""
    try:
        with Image.open(image_path) as img:
            width, height = img.size
            is_valid = width > min_size and height > min_size
            return is_valid, (width, height)
    except Exception as e:
        logging.error(f"检查图片尺寸失败 {image_path}: {e}")
        return False, (0, 0)

def _resize_image_if_needed(image_path: str, min_size: int = TARGET_MIN_SIZE) -> bool:
    """如果图片尺寸过小，则调整到最小要求"""
    try:
        is_valid, (width, height) = _check_image_size(image_path, MIN_IMAGE_SIZE)
        
        if is_valid:
            logging.info(f"图片尺寸已满足要求: {image_path} ({width}x{height})")
            return True
            
        if width < min_size or height < min_size:
            scale_factor = max(min_size / width, min_size / height)
            new_width = int(width * scale_factor)
            new_height = int(height * scale_factor)
            
            with Image.open(image_path) as img:
                resized_img = img.resize((new_width, new_height), Image.Resampling.LANCZOS)
                resized_img.save(image_path)
                
            logging.info(f"图片尺寸已调整: {image_path} ({width}x{height}) -> ({new_width}x{new_height})")
            return True
            
    except Exception as e:
        logging.error(f"调整图片尺寸失败 {image_path}: {e}")
        return False
    
    return False

def _is_near(rect1: BaseGeometry, rect2: BaseGeometry, distance: float = 20) -> bool:
    """判断两个矩形是否接近"""
    return rect1.buffer(0.1).distance(rect2.buffer(0.1)) < distance

def _is_horizontal_near(rect1: BaseGeometry, rect2: BaseGeometry, distance: float = 100) -> bool:
    """Check if two rectangles are near horizontally if one of them is a horizontal line."""
    result = False
    if abs(rect1.bounds[3] - rect1.bounds[1]) < 0.1 or abs(rect2.bounds[3] - rect2.bounds[1]) < 0.1:
        if abs(rect1.bounds[0] - rect2.bounds[0]) < 0.1 and abs(rect1.bounds[2] - rect2.bounds[2]) < 0.1:
            result = abs(rect1.bounds[3] - rect2.bounds[3]) < distance
    return result

def _union_rects(rect1: BaseGeometry, rect2: BaseGeometry) -> BaseGeometry:
    """合并两个矩形"""
    return sg.box(*(rect1.union(rect2).bounds))

def _merge_rects(rect_list: List[BaseGeometry], distance: float = 20, horizontal_distance: Optional[float] = None) -> List[BaseGeometry]:
    """合并矩形列表中的矩形"""
    merged = True
    while merged:
        merged = False
        new_rect_list = []
        while rect_list:
            rect = rect_list.pop(0)
            for other_rect in rect_list:
                if _is_near(rect, other_rect, distance) or (
                        horizontal_distance and _is_horizontal_near(rect, other_rect, horizontal_distance)):
                    rect = _union_rects(rect, other_rect)
                    rect_list.remove(other_rect)
                    merged = True
            new_rect_list.append(rect)
        rect_list = new_rect_list
    return rect_list

def _adsorb_rects_to_rects(source_rects: List[BaseGeometry], target_rects: List[BaseGeometry], distance: float = 10) -> Tuple[List[BaseGeometry], List[BaseGeometry]]:
    """将source_rects中的矩形吸附到target_rects中的矩形上"""
    new_source_rects = []
    for text_area_rect in source_rects:
        adsorbed = False
        for index, rect in enumerate(target_rects):
            if _is_near(text_area_rect, rect, distance):
                rect = _union_rects(text_area_rect, rect)
                target_rects[index] = rect
                adsorbed = True
                break
        if not adsorbed:
            new_source_rects.append(text_area_rect)
    return new_source_rects, target_rects

def _parse_rects_traditional(page: fitz.Page) -> List[BaseGeometry]:
    """传统的基于几何分析的矩形识别方法"""
    drawings = page.get_drawings()
    
    is_short_line = lambda x: abs(x['rect'][3] - x['rect'][1]) < 1 and abs(x['rect'][2] - x['rect'][0]) < 30
    drawings = [drawing for drawing in drawings if not is_short_line(drawing)]
    
    rect_list = [sg.box(*drawing['rect']) for drawing in drawings]
    
    images = page.get_image_info()
    image_rects = [sg.box(*image['bbox']) for image in images]
    
    rect_list += image_rects
    
    merged_rects = _merge_rects(rect_list, distance=10, horizontal_distance=100)
    merged_rects = [rect for rect in merged_rects if explain_validity(rect) == 'Valid Geometry']
    
    is_large_content = lambda x: (len(x[4]) / max(1, len(x[4].split('\n')))) > 5
    small_text_area_rects = [sg.box(*x[:4]) for x in page.get_text('blocks') if not is_large_content(x)]
    large_text_area_rects = [sg.box(*x[:4]) for x in page.get_text('blocks') if is_large_content(x)]
    _, merged_rects = _adsorb_rects_to_rects(large_text_area_rects, merged_rects, distance=0.1)
    _, merged_rects = _adsorb_rects_to_rects(small_text_area_rects, merged_rects, distance=5)
    
    merged_rects = _merge_rects(merged_rects, distance=10)
    
    merged_rects = [rect for rect in merged_rects if rect.bounds[2] - rect.bounds[0] > 20 and rect.bounds[3] - rect.bounds[1] > 20]
    
    return merged_rects

# 内容处理相关工具
def remove_region_markers(content: str) -> str:
    """去除内容中的特殊分割符标记"""
    import re
    
    # 匹配所有的REGION_START和REGION_END标记（更新为新的方括号格式）
    pattern = r'\[\[REGION_(START|END)_\d+_\d+_\w+\]\]\n?'
    cleaned_content = re.sub(pattern, '', content)
    
    # 兼容旧的尖括号格式（如果存在）
    old_pattern = r'<<<REGION_(START|END)_\d+_\d+_\w+>>>\n?'
    cleaned_content = re.sub(old_pattern, '', cleaned_content)
    
    # 清理多余的空行
    cleaned_content = re.sub(r'\n{3,}', '\n\n', cleaned_content)
    
    return cleaned_content

# 复杂内容修复相关工具
# 坐标验证和裁剪
def _validate_coordinate_transform(original_bbox: tuple, scale_factor: float, page_bounds: tuple) -> tuple:
    """验证坐标转换的正确性并裁剪到页面边界内
    
    Args:
        original_bbox: 原始边界框 (x1, y1, x2, y2)
        scale_factor: 缩放因子
        page_bounds: 页面边界 (x0, y0, width, height)
        
    Returns:
        (is_valid, clipped_bbox): 验证结果和裁剪后的边界框
    """
    try:
        x1, y1, x2, y2 = original_bbox
        page_width, page_height = page_bounds[2], page_bounds[3]
        
        # 记录原始坐标是否超出页面范围
        out_of_bounds = x1 < 0 or y1 < 0 or x2 > page_width or y2 > page_height
        if out_of_bounds:
            logging.warning(f"坐标超出页面范围: {original_bbox}, 页面大小: {page_bounds}")
            
        # 裁剪坐标到页面边界内
        x1_clipped = max(0, min(x1, page_width))
        y1_clipped = max(0, min(y1, page_height))
        x2_clipped = max(0, min(x2, page_width))
        y2_clipped = max(0, min(y2, page_height))
        
        # 确保裁剪后的区域仍然有效
        if x2_clipped <= x1_clipped or y2_clipped <= y1_clipped:
            logging.warning(f"区域裁剪后无效，调整大小: 原始{original_bbox} -> 裁剪后({x1_clipped:.2f}, {y1_clipped:.2f}, {x2_clipped:.2f}, {y2_clipped:.2f})")
            
            # 如果裁剪后区域无效，尝试给它一个最小的有效大小
            if x2_clipped <= x1_clipped:
                if x1_clipped < page_width - 10:
                    x2_clipped = x1_clipped + 10
                else:
                    x1_clipped = max(0, x2_clipped - 10)
                    
            if y2_clipped <= y1_clipped:
                if y1_clipped < page_height - 10:
                    y2_clipped = y1_clipped + 10
                else:
                    y1_clipped = max(0, y2_clipped - 10)
                    
            logging.info(f"区域调整后坐标: ({x1_clipped:.2f}, {y1_clipped:.2f}, {x2_clipped:.2f}, {y2_clipped:.2f})")
        
        # 最终验证裁剪后的区域
        if x2_clipped <= x1_clipped or y2_clipped <= y1_clipped:
            logging.error(f"无效区域，即使调整后仍无效: 宽度={x2_clipped-x1_clipped}, 高度={y2_clipped-y1_clipped}")
            return False, original_bbox
            
        # 检查区域大小是否合理
        area = (x2_clipped - x1_clipped) * (y2_clipped - y1_clipped)
        if area < 100:  # 区域太小
            logging.warning(f"区域面积过小: {area:.2f}")
            
        clipped_bbox = (x1_clipped, y1_clipped, x2_clipped, y2_clipped)
        
        # 如果有裁剪发生，记录日志
        if out_of_bounds:
            logging.info(f"坐标已裁剪: {original_bbox} -> {clipped_bbox}")
            
        return True, clipped_bbox
        
    except Exception as e:
        logging.error(f"坐标验证失败: {e}")
        return False, original_bbox

# bbox 智能拓展
def _expand_bbox_intelligent(bbox: tuple, region_type: str, scale_factor: float, page_bounds: tuple) -> tuple:
    """
    根据区域类型和缩放因子智能扩展bbox边界，确保完全覆盖识别区域
    
    Args:
        bbox: 原始边界框 (x1, y1, x2, y2)
        region_type: 区域类型 ('text', 'table', 'image', 'title', 'list', etc.)
        scale_factor: VLM分析时使用的缩放因子
        page_bounds: 页面边界 (x0, y0, width, height)
        
    Returns:
        扩展后的边界框 (x1, y1, x2, y2)
    """
    x1, y1, x2, y2 = bbox
    page_width, page_height = page_bounds[2], page_bounds[3]
    
    # 计算区域的宽高
    width = x2 - x1
    height = y2 - y1
    
    # 基础扩展像素（考虑缩放因子的影响）
    # 缩放因子越大，VLM分析越精确，需要的补偿越小
    base_padding = max(1.0, 3.0 / scale_factor)
    
    # 根据区域类型确定扩展策略
    if region_type == "table":
        # 优化后的表格扩展策略：更保守但仍然有效
        
        # 计算表格密度指标
        table_density = height / max(1, width)  # 高宽比，高比例表示密集表格
        
        # 调整后的基础扩展比例（减少激进程度）
        base_h_ratio = 0.03  # 3%基础水平扩展（从5%降低）
        base_v_ratio = 0.05  # 5%基础垂直扩展（从8%降低）
        
        # 更温和的密度调整
        if table_density > 0.4:  # 提高密度阈值从0.3到0.4
            density_multiplier = min(1.5, 1.0 + table_density * 0.5)  # 减少倍数，最多1.5倍
            logging.info(f"检测到高密度表格，密度比例: {table_density:.2f}，扩展倍数: {density_multiplier:.2f}")
        else:
            density_multiplier = 1.0
        
        # 更温和的大小调整
        if width > 400 and height > 60:  # 提高大型表格阈值
            size_multiplier = 1.3  # 从1.5降低到1.3
            logging.info(f"检测到大型表格 ({width:.0f}x{height:.0f})，应用大型表格扩展")
        elif width < 150 or height < 25:  # 调整小型表格阈值
            size_multiplier = 1.5  # 从2.0降低到1.5
            logging.info(f"检测到小型表格 ({width:.0f}x{height:.0f})，应用小型表格保护扩展")
        else:
            size_multiplier = 1.0
        
        # 计算最终扩展比例
        final_h_ratio = base_h_ratio * density_multiplier * size_multiplier
        final_v_ratio = base_v_ratio * density_multiplier * size_multiplier
        
        # 设置扩展上限，防止过度扩展
        max_h_ratio = 0.08  # 最大8%水平扩展
        max_v_ratio = 0.10  # 最大10%垂直扩展
        final_h_ratio = min(final_h_ratio, max_h_ratio)
        final_v_ratio = min(final_v_ratio, max_v_ratio)
        
        # 确保最小绝对扩展值
        min_h_padding = max(base_padding * 2, 6.0)  # 最小6像素水平扩展（从8降低）
        min_v_padding = max(base_padding * 2, 8.0)  # 最小8像素垂直扩展（从10降低）
        
        pad_x = max(min_h_padding, width * final_h_ratio)
        pad_y = max(min_v_padding, height * final_v_ratio)
        
        # 设置绝对扩展上限
        max_h_padding = min(50, width * 0.15)  # 最大50像素或15%宽度
        max_v_padding = min(30, height * 0.20)  # 最大30像素或20%高度
        pad_x = min(pad_x, max_h_padding)
        pad_y = min(pad_y, max_v_padding)
        
        logging.info(f"表格智能扩展分析: 密度{table_density:.2f}, 大小({width:.0f}x{height:.0f}), 最终扩展比例({final_h_ratio:.1%},{final_v_ratio:.1%}), 实际扩展({pad_x:.1f},{pad_y:.1f})")
        
    elif region_type in ["title", "text"]:
        # 文本类型需要适中的边界，避免截断字符
        pad_x = max(base_padding * 1.5, width * 0.02)  # 至少2%的宽度
        pad_y = max(base_padding * 1.5, height * 0.08)  # 至少8%的高度，文本行间距较重要
        
    elif region_type == "list":
        # 列表可能有缩进和项目符号，左右需要更多空间
        pad_x = max(base_padding * 2, width * 0.04)  # 至少4%的宽度
        pad_y = max(base_padding * 1.5, height * 0.03)  # 至少3%的高度
        
    elif region_type == "image":
        # 图像通常边界比较精确，但仍需要小幅扩展以防边缘模糊
        pad_x = max(base_padding, width * 0.01)  # 至少1%的宽度
        pad_y = max(base_padding, height * 0.01)  # 至少1%的高度
        
    elif region_type == "formula":
        # 数学公式可能有上下标，需要更多垂直空间
        pad_x = max(base_padding, width * 0.02)  # 至少2%的宽度
        pad_y = max(base_padding * 2, height * 0.15)  # 至少15%的高度
        
    else:
        # 未知类型使用保守的扩展策略
        pad_x = max(base_padding * 1.5, width * 0.025)  # 至少2.5%的宽度
        pad_y = max(base_padding * 1.5, height * 0.05)   # 至少5%的高度
    
    # 对于非常小的区域，使用最小绝对扩展
    if width < 50 or height < 20:
        pad_x = max(pad_x, 5.0)  # 提高到5像素
        pad_y = max(pad_y, 5.0)  # 提高到5像素
    
    # 计算扩展后的边界
    x1_new = max(0, x1 - pad_x)
    y1_new = max(0, y1 - pad_y)
    x2_new = min(page_width, x2 + pad_x)
    y2_new = min(page_height, y2 + pad_y)
    
    # 验证扩展后的边界是否有效
    if x2_new <= x1_new or y2_new <= y1_new:
        logging.warning(f"扩展后边界无效，使用原始边界: 原始{bbox} -> 扩展({x1_new:.2f}, {y1_new:.2f}, {x2_new:.2f}, {y2_new:.2f})")
        return bbox
    
    expansion_info = {
        'original': bbox,
        'expanded': (x1_new, y1_new, x2_new, y2_new),
        'padding': (pad_x, pad_y),
        'type': region_type,
        'scale_factor': scale_factor
    }
    
    logging.info(f"智能边界扩展: {region_type} 区域 {bbox} -> ({x1_new:.2f}, {y1_new:.2f}, {x2_new:.2f}, {y2_new:.2f}), 填充({pad_x:.2f}, {pad_y:.2f})")
    
    return (x1_new, y1_new, x2_new, y2_new)

# 智能合并重叠区域
def _merge_overlapping_expanded_rects(rects: List[tuple], region_info: Dict[int, Dict], overlap_threshold: float = 0.8) -> Tuple[List[tuple], Dict[int, Dict]]:
    """
    合并过度重叠的扩展后矩形区域，避免redaction时重复覆盖
    
    Args:
        rects: 扩展后的边界框列表
        region_info: 区域信息字典
        overlap_threshold: 重叠阈值（0.8表示80%重叠时合并）
        
    Returns:
        合并后的边界框列表和更新的区域信息
    """
    if len(rects) <= 1:
        return rects, region_info
    
    merged_rects = []
    merged_info = {}
    used_indices = set()
    
    for i, rect1 in enumerate(rects):
        if i in used_indices:
            continue
            
        current_rect = rect1
        merged_types = [region_info[i]['type']]
        merged_content = [region_info[i]['full_content']]
        indices_to_merge = [i]
        
        # 检查与其他矩形的重叠
        for j, rect2 in enumerate(rects[i+1:], i+1):
            if j in used_indices:
                continue
                
            # 计算重叠面积
            x1_max = max(current_rect[0], rect2[0])
            y1_max = max(current_rect[1], rect2[1])
            x2_min = min(current_rect[2], rect2[2])
            y2_min = min(current_rect[3], rect2[3])
            
            if x2_min > x1_max and y2_min > y1_max:
                overlap_area = (x2_min - x1_max) * (y2_min - y1_max)
                rect1_area = (current_rect[2] - current_rect[0]) * (current_rect[3] - current_rect[1])
                rect2_area = (rect2[2] - rect2[0]) * (rect2[3] - rect2[1])
                
                # 计算重叠比例（相对于较小的矩形）
                min_area = min(rect1_area, rect2_area)
                overlap_ratio = overlap_area / min_area if min_area > 0 else 0
                
                if overlap_ratio > overlap_threshold:
                    # 合并矩形
                    current_rect = (
                        min(current_rect[0], rect2[0]),
                        min(current_rect[1], rect2[1]),
                        max(current_rect[2], rect2[2]),
                        max(current_rect[3], rect2[3])
                    )
                    merged_types.append(region_info[j]['type'])
                    merged_content.append(region_info[j]['full_content'])
                    indices_to_merge.append(j)
        
        # 标记已使用的索引
        for idx in indices_to_merge:
            used_indices.add(idx)
        
        # 添加合并后的区域
        merged_index = len(merged_rects)
        merged_rects.append(current_rect)
        
        # 合并区域信息
        primary_type = max(set(merged_types), key=merged_types.count)  # 选择最常见的类型
        combined_content = ' | '.join(merged_content)  # 合并内容
        
        merged_info[merged_index] = {
            'type': primary_type,
            'full_content': combined_content,
            'merged_from': indices_to_merge
        }
        
        if len(indices_to_merge) > 1:
            logging.info(f"合并重叠区域 {indices_to_merge} -> 区域 {merged_index} ({primary_type})")
    
    return merged_rects, merged_info

# 修复复杂表格
def _detect_and_optimize_complex_tables(rects: List[tuple], region_info: Dict[int, Dict], page_bounds: tuple) -> Tuple[List[tuple], Dict[int, Dict]]:
    """
    检测并优化复杂表格区域，专门处理被VLM分割的表格区域
    优化版本：减少过度扩展，更加保守的扩展策略
    
    Args:
        rects: 边界框列表
        region_info: 区域信息字典
        page_bounds: 页面边界
        
    Returns:
        优化后的边界框列表和区域信息
    """
    if len(rects) <= 1:
        return rects, region_info
    
    # 找出所有表格区域
    table_indices = []
    for i, info in region_info.items():
        if info.get('type') == 'table' and i < len(rects):
            table_indices.append(i)
    
    if not table_indices:
        return rects, region_info
    
    optimized_rects = list(rects)
    optimized_info = dict(region_info)
    
    for table_idx in table_indices:
        table_rect = rects[table_idx]
        table_content = region_info[table_idx].get('full_content', '')
        
        # 检测复杂表格的特征
        is_complex_table = False
        complexity_reasons = []
        complexity_score = 0  # 引入复杂度评分系统
        
        # 特征1: 表格内容包含多行多列（权重：1分）
        if '|' in table_content and table_content.count('\n') > 3:
            is_complex_table = True
            complexity_reasons.append("多行多列结构")
            complexity_score += 1
        
        # 特征2: 包含嵌套数据（如金额、比例等）（权重：1分）
        if any(pattern in table_content.lower() for pattern in ['£', '$', '€', '%', 'ratio', 'total', 'debt']):
            is_complex_table = True
            complexity_reasons.append("财务数据表格")
            complexity_score += 1
        
        # 特征3: 表格密度高（权重：根据密度动态计算）
        width = table_rect[2] - table_rect[0]
        height = table_rect[3] - table_rect[1]
        content_density = len(table_content) / max(1, height)
        
        if content_density > 8:  # 提高密度阈值从5到8
            is_complex_table = True
            complexity_reasons.append(f"高密度内容(密度:{content_density:.1f})")
            # 根据密度级别给分：8-12为1分，12+为2分
            complexity_score += 1 if content_density <= 12 else 2
        
        # 特征4: 表格行数多（新增特征）
        line_count = table_content.count('\n')
        if line_count > 6:
            complexity_reasons.append(f"多行表格(行数:{line_count})")
            complexity_score += 1
        
        if is_complex_table:
            logging.info(f"检测到复杂表格区域 {table_idx}: {', '.join(complexity_reasons)}，复杂度评分: {complexity_score}")
            
            # 应用复杂表格的适度扩展策略（大幅减少激进程度）
            x1, y1, x2, y2 = table_rect
            
            # 检查是否已经被第一次扩展过（通过比较原始坐标推断）
            original_info = optimized_info.get(table_idx, {})
            has_been_expanded = 'optimized' in original_info or (x2 - x1) > width * 1.1
            
            if has_been_expanded:
                # 如果已经扩展过，使用更保守的策略
                base_expansion = 8.0  # 减少基础扩展从15到8像素
                complexity_multiplier = min(1.5, 1.0 + complexity_score * 0.2)  # 大幅减少倍数
                logging.info(f"表格已被扩展，使用保守策略，复杂度倍数: {complexity_multiplier:.2f}")
            else:
                # 如果没有扩展过，使用标准策略
                base_expansion = 12.0  # 标准扩展
                complexity_multiplier = min(2.0, 1.0 + complexity_score * 0.3)
                logging.info(f"表格未被扩展，使用标准策略，复杂度倍数: {complexity_multiplier:.2f}")
            
            # 计算扩展量，引入页面大小约束
            page_width = page_bounds[2]
            page_height = page_bounds[3]
            
            # 水平扩展：确保不漏掉表格边框，但限制最大扩展
            h_expansion_ratio = min(0.04, 0.02 + complexity_score * 0.005)  # 最大4%
            h_expansion = max(base_expansion, width * h_expansion_ratio) * complexity_multiplier
            h_expansion = min(h_expansion, 40)  # 绝对上限40像素
            
            # 垂直扩展：确保不漏掉行间距和边框
            v_expansion_ratio = min(0.06, 0.03 + complexity_score * 0.005)  # 最大6%
            v_expansion = max(base_expansion, height * v_expansion_ratio) * complexity_multiplier
            v_expansion = min(v_expansion, 25)  # 绝对上限25像素
            
            # 页面边界约束：确保扩展后不超过页面边界太多
            max_width_expansion = min(h_expansion, (page_width - width) / 4)  # 最多占用剩余宽度的1/4
            max_height_expansion = min(v_expansion, (page_height - height) / 4)  # 最多占用剩余高度的1/4
            
            h_expansion = min(h_expansion, max_width_expansion)
            v_expansion = min(v_expansion, max_height_expansion)
            
            # 计算新边界
            x1_new = max(0, x1 - h_expansion)
            y1_new = max(0, y1 - v_expansion)
            x2_new = min(page_bounds[2], x2 + h_expansion)
            y2_new = min(page_bounds[3], y2 + v_expansion)
            
            # 验证扩展是否合理（扩展后面积不应超过原来的3倍）
            original_area = width * height
            new_area = (x2_new - x1_new) * (y2_new - y1_new)
            area_ratio = new_area / original_area if original_area > 0 else 1
            
            if area_ratio > 3.0:
                # 如果扩展过大，按比例缩减
                scale_down = (3.0 / area_ratio) ** 0.5
                h_expansion *= scale_down
                v_expansion *= scale_down
                
                x1_new = max(0, x1 - h_expansion)
                y1_new = max(0, y1 - v_expansion)
                x2_new = min(page_bounds[2], x2 + h_expansion)
                y2_new = min(page_bounds[3], y2 + v_expansion)
                
                logging.info(f"扩展过大，按比例缩减: scale_down={scale_down:.2f}")
            
            optimized_rects[table_idx] = (x1_new, y1_new, x2_new, y2_new)
            
            # 更新区域信息，标记为已优化
            optimized_info[table_idx] = {
                **region_info[table_idx],
                'optimized': True,
                'optimization_type': 'complex_table',
                'complexity_reasons': complexity_reasons,
                'complexity_score': complexity_score,
                'original_bbox': table_rect,
                'expansion': (h_expansion, v_expansion),
                'area_ratio': new_area / original_area
            }
            
            logging.info(f"复杂表格区域 {table_idx} 优化: ({x1:.1f},{y1:.1f},{x2:.1f},{y2:.1f}) -> ({x1_new:.1f},{y1_new:.1f},{x2_new:.1f},{y2_new:.1f}), 扩展({h_expansion:.1f},{v_expansion:.1f}), 面积比例: {area_ratio:.2f}")
    
    return optimized_rects, optimized_info

def _detect_and_resolve_overlaps(rects: List[tuple], region_info: Dict[int, Dict], page_bounds: tuple) -> Tuple[List[tuple], Dict[int, Dict]]:
    """
    检测并解决边界框重叠问题，确保PDF文本填充不会相互覆盖
    
    Args:
        rects: 边界框列表
        region_info: 区域信息字典
        page_bounds: 页面边界
        
    Returns:
        解决重叠后的边界框列表和区域信息
    """
    if len(rects) <= 1:
        return rects, region_info
    
    resolved_rects = list(rects)
    resolved_info = dict(region_info)
    
    # 创建区域优先级映射（text类型优先级最高，避免被表格覆盖）
    priority_map = {
        'title': 5,
        'text': 4,  
        'list': 3,
        'formula': 2,
        'table': 1,
        'image': 0
    }
    
    # 按Y坐标排序，从上到下处理
    indexed_rects = [(i, rect) for i, rect in enumerate(resolved_rects)]
    indexed_rects.sort(key=lambda x: x[1][1])  # 按y1坐标排序
    
    overlaps_found = []
    
    # 检测重叠
    for i, (idx1, rect1) in enumerate(indexed_rects):
        for j, (idx2, rect2) in enumerate(indexed_rects[i+1:], i+1):
            overlap_area = _calculate_overlap_area(rect1, rect2)
            if overlap_area > 0:
                overlap_ratio1 = overlap_area / _calculate_area(rect1)
                overlap_ratio2 = overlap_area / _calculate_area(rect2)
                
                overlaps_found.append({
                    'indices': (idx1, idx2),
                    'rects': (rect1, rect2),
                    'overlap_area': overlap_area,
                    'overlap_ratios': (overlap_ratio1, overlap_ratio2),
                    'types': (region_info[idx1]['type'], region_info[idx2]['type'])
                })
                
                logging.warning(f"检测到重叠: 区域{idx1}({region_info[idx1]['type']}) 与 区域{idx2}({region_info[idx2]['type']}), 重叠面积: {overlap_area:.1f}")
    
    if not overlaps_found:
        return resolved_rects, resolved_info
    
    # 处理重叠
    for overlap in overlaps_found:
        idx1, idx2 = overlap['indices']
        rect1, rect2 = overlap['rects']
        type1, type2 = overlap['types']
        overlap_ratio1, overlap_ratio2 = overlap['overlap_ratios']
        
        # 获取优先级
        priority1 = priority_map.get(type1, 0)
        priority2 = priority_map.get(type2, 0)
        
        # 决定处理策略
        if overlap_ratio1 > 0.7 or overlap_ratio2 > 0.7:
            # 高度重叠：合并区域
            logging.info(f"高度重叠({overlap_ratio1:.1%}, {overlap_ratio2:.1%})，合并区域 {idx1} 和 {idx2}")
            merged_rect = (
                min(rect1[0], rect2[0]),
                min(rect1[1], rect2[1]), 
                max(rect1[2], rect2[2]),
                max(rect1[3], rect2[3])
            )
            
            # 选择高优先级的类型
            if priority1 >= priority2:
                primary_idx, secondary_idx = idx1, idx2
                primary_type = type1
            else:
                primary_idx, secondary_idx = idx2, idx1
                primary_type = type2
            
            resolved_rects[primary_idx] = merged_rect
            resolved_info[primary_idx] = {
                **resolved_info[primary_idx],
                'type': primary_type,
                'merged_from': [idx1, idx2],
                'full_content': f"{resolved_info[idx1]['full_content']} | {resolved_info[idx2]['full_content']}"
            }
            
            # 标记次要区域为已合并
            resolved_info[secondary_idx]['merged_into'] = primary_idx
            
        elif overlap_ratio1 > 0.3 or overlap_ratio2 > 0.3:
            # 中度重叠：调整边界
            logging.info(f"中度重叠({overlap_ratio1:.1%}, {overlap_ratio2:.1%})，调整边界")
            
            if priority1 > priority2:
                # 保护高优先级区域，调整低优先级区域
                adjusted_rect = _adjust_rect_to_avoid_overlap(rect2, rect1, 'shrink')
                resolved_rects[idx2] = adjusted_rect
                logging.info(f"调整区域 {idx2} 以避开区域 {idx1}")
            else:
                adjusted_rect = _adjust_rect_to_avoid_overlap(rect1, rect2, 'shrink')
                resolved_rects[idx1] = adjusted_rect 
                logging.info(f"调整区域 {idx1} 以避开区域 {idx2}")
                
        else:
            # 轻度重叠：轻微调整边界
            logging.info(f"轻度重叠({overlap_ratio1:.1%}, {overlap_ratio2:.1%})，轻微调整")
            
            # 使用安全间距分离
            safe_margin = 3.0
            if rect1[3] > rect2[1] and rect1[1] < rect2[3]:  # 垂直重叠
                if rect1[1] < rect2[1]:  # rect1在上
                    resolved_rects[idx1] = (rect1[0], rect1[1], rect1[2], rect2[1] - safe_margin)
                    resolved_rects[idx2] = (rect2[0], rect1[3] + safe_margin, rect2[2], rect2[3])
                else:  # rect2在上
                    resolved_rects[idx2] = (rect2[0], rect2[1], rect2[2], rect1[1] - safe_margin)
                    resolved_rects[idx1] = (rect1[0], rect2[3] + safe_margin, rect1[2], rect1[3])
    
    # 验证调整后的区域是否仍然有效
    final_rects = []
    final_info = {}
    
    for i, rect in enumerate(resolved_rects):
        if i not in resolved_info:
            continue
            
        if 'merged_into' in resolved_info[i]:
            continue  # 跳过已合并的区域
            
        # 验证区域大小
        if rect[2] > rect[0] and rect[3] > rect[1]:
            area = (rect[2] - rect[0]) * (rect[3] - rect[1])
            if area >= 50:  # 最小面积阈值
                final_rects.append(rect)
                final_info[len(final_rects) - 1] = resolved_info[i]
            else:
                logging.warning(f"区域 {i} 调整后面积过小({area:.1f})，已移除")
        else:
            logging.warning(f"区域 {i} 调整后无效({rect})，已移除")
    
    logging.info(f"重叠处理完成: {len(rects)} -> {len(final_rects)} 个区域")
    return final_rects, final_info

def _calculate_overlap_area(rect1: tuple, rect2: tuple) -> float:
    """计算两个矩形的重叠面积"""
    x1_max = max(rect1[0], rect2[0])
    y1_max = max(rect1[1], rect2[1])
    x2_min = min(rect1[2], rect2[2])
    y2_min = min(rect1[3], rect2[3])
    
    if x2_min > x1_max and y2_min > y1_max:
        return (x2_min - x1_max) * (y2_min - y1_max)
    return 0.0

def _calculate_area(rect: tuple) -> float:
    """计算矩形面积"""
    return (rect[2] - rect[0]) * (rect[3] - rect[1])

def _adjust_rect_to_avoid_overlap(rect_to_adjust: tuple, protected_rect: tuple, method: str = 'shrink') -> tuple:
    """调整矩形以避免重叠"""
    x1, y1, x2, y2 = rect_to_adjust
    px1, py1, px2, py2 = protected_rect
    
    if method == 'shrink':
        # 缩小矩形以避免重叠
        if y1 < py2 and y2 > py1:  # 垂直重叠
            if y1 < py1:  # 上方重叠
                y2 = min(y2, py1 - 2)
            else:  # 下方重叠  
                y1 = max(y1, py2 + 2)
                
        if x1 < px2 and x2 > px1:  # 水平重叠
            if x1 < px1:  # 左侧重叠
                x2 = min(x2, px1 - 2)
            else:  # 右侧重叠
                x1 = max(x1, px2 + 2)
    
    return (max(0, x1), max(0, y1), x2, y2)

# PDF 文本替换相关工具
async def _extract_region_translated_content(full_translated_content: str, region_index: int, region_type: str, page_index: int = 0) -> str:
    """从完整的翻译内容中提取特定区域的翻译内容，使用特殊分割符"""
    try:
        # 更智能的内容提取方式
        if not full_translated_content:
            logging.warning(f"页面 {page_index} 区域 {region_index}: 翻译内容为空")
            return ""
        
        # 构造对应的特殊分割符（使用新的方括号格式）
        region_start_marker = f"[[REGION_START_{page_index}_{region_index}_{region_type}]]"
        region_end_marker = f"[[REGION_END_{page_index}_{region_index}_{region_type}]]"
        
        # 添加详细的调试信息
        logging.debug(f"查找区域 {page_index}_{region_index}_{region_type} 的翻译内容")
        logging.debug(f"完整翻译内容长度: {len(full_translated_content)}")
        logging.debug(f"寻找开始分割符: {region_start_marker}")
        logging.debug(f"寻找结束分割符: {region_end_marker}")
        
        # 首先尝试使用特殊分割符提取
        start_marker_found = region_start_marker in full_translated_content
        end_marker_found = region_end_marker in full_translated_content
        
        logging.debug(f"开始分割符存在: {start_marker_found}, 结束分割符存在: {end_marker_found}")
        
        if start_marker_found and end_marker_found:
            start_pos = full_translated_content.find(region_start_marker) + len(region_start_marker)
            end_pos = full_translated_content.find(region_end_marker)
            
            logging.debug(f"找到分割符位置: start={start_pos}, end={end_pos}")
            if start_pos < end_pos:
                extracted_content = full_translated_content[start_pos:end_pos]
                logging.info(f"成功使用特殊分割符提取区域 {page_index}_{region_index} 的内容，长度: {len(extracted_content)}")
                
                # 直接返回提取的内容，因为VLM现在直接输出HTML格式
                logging.info(f"成功提取区域 {page_index}_{region_index} ({region_type}) HTML内容，长度: {len(extracted_content)}")
                return extracted_content.strip()
        
        # 如果特殊分割符方法失败，尝试模糊匹配
        logging.warning(f"未找到区域 {page_index}_{region_index} 的精确特殊分割符，尝试模糊匹配")
        
        # 尝试模糊匹配：忽略区域类型的差异（使用新的方括号格式）
        import re
        fuzzy_start_pattern = f"\[\[REGION_START_{page_index}_{region_index}_.*?\]\]"
        fuzzy_end_pattern = f"\[\[REGION_END_{page_index}_{region_index}_.*?\]\]"
        
        start_matches = re.findall(fuzzy_start_pattern, full_translated_content)
        end_matches = re.findall(fuzzy_end_pattern, full_translated_content)
        
        if start_matches and end_matches:
            actual_start_marker = start_matches[0]
            actual_end_marker = end_matches[0]
            
            logging.info(f"找到模糊匹配的分割符: {actual_start_marker} -> {actual_end_marker}")
            
            start_pos = full_translated_content.find(actual_start_marker) + len(actual_start_marker)
            end_pos = full_translated_content.find(actual_end_marker)
            
            if start_pos < end_pos:
                extracted_content = full_translated_content[start_pos:end_pos]
                logging.info(f"模糊匹配成功提取区域 {page_index}_{region_index} 的内容，长度: {len(extracted_content)}")
                return extracted_content.strip()
        
        # 如果模糊匹配也失败，尝试查找所有该页面的分割符（使用新的方括号格式）
        logging.warning(f"模糊匹配失败，查找页面 {page_index} 的所有分割符")
        
        page_markers = re.findall(f"\[\[REGION_START_{page_index}_\\d+_.*?\]\]", full_translated_content)
        if page_markers:
            logging.info(f"页面 {page_index} 找到的所有分割符: {page_markers}")
            
            # 尝试按区域索引顺序提取
            sorted_markers = []
            for marker in page_markers:
                # 提取区域索引（使用新的方括号格式）
                match = re.search(f"\[\[REGION_START_{page_index}_(\\d+)_.*?\]\]", marker)
                if match:
                    marker_region_index = int(match.group(1))
                    sorted_markers.append((marker_region_index, marker))
            
            # 按区域索引排序
            sorted_markers.sort(key=lambda x: x[0])
            
            # 查找目标区域索引
            for i, (marker_region_index, marker) in enumerate(sorted_markers):
                if marker_region_index == region_index:
                    # 找到对应的结束标记
                    end_marker_pattern = marker.replace("START", "END")
                    if end_marker_pattern in full_translated_content:
                        start_pos = full_translated_content.find(marker) + len(marker)
                        end_pos = full_translated_content.find(end_marker_pattern)
                        
                        if start_pos < end_pos:
                            extracted_content = full_translated_content[start_pos:end_pos]
                            logging.info(f"按索引顺序成功提取区域 {page_index}_{region_index} 的内容，长度: {len(extracted_content)}")
                            return extracted_content.strip()
        
        # 如果新格式失败，尝试旧的尖括号格式（兼容性支持）
        logging.warning(f"新格式分割符失败，尝试旧的尖括号格式")
        
        # 使用旧的尖括号格式尝试提取
        old_start_marker = f"<<<REGION_START_{page_index}_{region_index}_{region_type}>>>"
        old_end_marker = f"<<<REGION_END_{page_index}_{region_index}_{region_type}>>>"
        
        if old_start_marker in full_translated_content and old_end_marker in full_translated_content:
            start_pos = full_translated_content.find(old_start_marker) + len(old_start_marker)
            end_pos = full_translated_content.find(old_end_marker)
            
            if start_pos < end_pos:
                extracted_content = full_translated_content[start_pos:end_pos]
                logging.info(f"使用旧格式成功提取区域 {page_index}_{region_index} 的内容，长度: {len(extracted_content)}")
                return extracted_content.strip()
        
        # 如果所有特殊分割符方法都失败，跳过处理
        logging.warning(f"所有特殊分割符方法都失败，区域 {page_index}_{region_index} 可能不存在，跳过处理")
        return ""
            
    except Exception as e:
        logging.error(f"提取区域翻译内容时发生错误: {e}")
        import traceback
        logging.error(f"错误详情: {traceback.format_exc()}")
        return ""

async def _replace_text_in_region(page: fitz.Page, bbox: tuple, translated_content: str, region_type: str):
    """在指定区域替换文本，使用安全的分层填充方法"""
    try:
        # 创建矩形对象
        rect = fitz.Rect(bbox)
        
        # 验证矩形有效性
        if rect.is_empty or rect.is_infinite:
            logging.error(f"无效的矩形区域: {bbox}")
            return
        
        # 创建安全边界（轻微缩小以避免边缘重叠）
        safety_margin = 1.0
        safe_rect = fitz.Rect(
            rect.x0 + safety_margin,
            rect.y0 + safety_margin,
            rect.x1 - safety_margin,
            rect.y1 - safety_margin
        )
        
        # 确保安全矩形仍然有效
        if safe_rect.width < 10 or safe_rect.height < 5:
            logging.warning(f"区域过小，使用原始矩形: {bbox}")
            safe_rect = rect
        
        # 使用更精细的redaction方式
        logging.info(f"开始处理区域 {region_type}: {bbox} -> 安全区域: {safe_rect}")
        
        # Step 1: 添加redaction注释（使用安全区域）
        try:
            page.add_redact_annot(safe_rect, text="", fill=(1, 1, 1))  # 白色填充
            logging.info(f"Redaction注释添加成功: {safe_rect}")
        except Exception as e:
            logging.error(f"添加redaction注释失败: {e}")
            return
        
        # Step 2: 应用redaction（更保守的参数）
        try:
            page.apply_redactions(
                images=fitz.PDF_REDACT_IMAGE_NONE,     # 不影响图像
                graphics=fitz.PDF_REDACT_LINE_ART_NONE, # 不影响图形
                text=fitz.PDF_REDACT_TEXT_REMOVE       # 只移除文本
            )
            logging.info(f"Redaction应用成功")
        except Exception as e:
            logging.error(f"应用redaction失败: {e}")
            return
        
        # Step 3: 根据区域类型选择合适的文本插入方式
        try:
            if region_type == "table":
                await _insert_table_text_safe(page, safe_rect, translated_content)
            elif region_type == "image":
                logging.info(f"图像区域 {region_type} 不进行文本插入")
            else:
                await _insert_normal_text_safe(page, safe_rect, translated_content)
            logging.info(f"文本插入成功: {region_type}")
        except Exception as e:
            logging.error(f"文本插入失败: {e}")
            # 尝试备用方法
            try:
                await _insert_fallback_text(page, safe_rect, translated_content)
                logging.info(f"备用文本插入成功")
            except Exception as e2:
                logging.error(f"备用文本插入也失败: {e2}")
                
    except Exception as e:
        logging.error(f"替换文本时发生错误: {e}")

async def _insert_table_text_safe(page: fitz.Page, rect: fitz.Rect, translated_content: str):
    """安全地为表格区域插入翻译的HTML文本"""
    try:
        # VLM已经输出了完整的HTML格式，保留原始格式信息
        html_content = translated_content
        
        # 添加中文字体支持的CSS，同时保留VLM的原始样式
        enhanced_html = _enhance_html_with_chinese_fonts(html_content)
        
        # 使用更安全的insert_htmlbox调用，避免"No destination with id"错误
        try:
            # 方法1: 使用archive参数，可能解决destination问题
            page.insert_htmlbox(rect, enhanced_html, archive=None)
            logging.info(f"HTML表格内容插入成功（使用archive=None）")
        except Exception as e1:
            logging.warning(f"使用archive=None失败: {e1}")
            try:
                # 方法2: 简化HTML内容，移除可能导致问题的元素
                simplified_html = _simplify_html_for_pymupdf(enhanced_html)
                page.insert_htmlbox(rect, simplified_html)
                logging.info(f"HTML表格内容插入成功（使用简化HTML）")
            except Exception as e2:
                logging.warning(f"简化HTML也失败: {e2}")
                # 方法3: 回退到普通文本插入
                await _insert_normal_text_safe(page, rect, translated_content)
        
    except Exception as e:
        logging.error(f"HTML表格插入失败: {e}")
        # 回退到普通文本插入
        await _insert_normal_text_safe(page, rect, translated_content)

async def _insert_normal_text_safe(page: fitz.Page, rect: fitz.Rect, translated_content: str):
    """安全地为普通区域插入翻译的HTML文本"""
    try:
        # VLM已经输出了完整的HTML格式，保留原始格式信息
        html_content = translated_content
        
        # 添加中文字体支持的CSS，同时保留VLM的原始样式
        enhanced_html = _enhance_html_with_chinese_fonts(html_content)
        
        # 使用更安全的insert_htmlbox调用
        try:
            # 方法1: 使用archive参数
            page.insert_htmlbox(rect, enhanced_html, archive=None)
            logging.info(f"HTML普通文本插入成功（使用archive=None）")
        except Exception as e1:
            logging.warning(f"使用archive=None失败: {e1}")
            try:
                # 方法2: 简化HTML内容
                simplified_html = _simplify_html_for_pymupdf(enhanced_html)
                page.insert_htmlbox(rect, simplified_html)
                logging.info(f"HTML普通文本插入成功（使用简化HTML）")
            except Exception as e2:
                logging.warning(f"简化HTML也失败: {e2}")
                # 方法3: 回退到备用文本插入
                await _insert_fallback_text(page, rect, translated_content)
        
    except Exception as e:
        logging.error(f"HTML普通文本插入失败: {e}")
        # 最终回退
        await _insert_fallback_text(page, rect, translated_content)

def _simplify_html_for_pymupdf(html_content: str) -> str:
    """
    简化HTML内容以适配PyMuPDF的insert_htmlbox，避免"No destination with id"错误
    """
    import re
    
    # 移除可能导致问题的HTML属性和元素
    simplified = html_content
    
    # 移除id属性（这可能是导致"No destination with id"错误的原因）
    simplified = re.sub(r'\s+id="[^"]*"', '', simplified)
    simplified = re.sub(r"\s+id='[^']*'", '', simplified)
    
    # 移除可能有问题的CSS属性
    problematic_css = [
        'position:', 'transform:', 'z-index:', 'float:',
        'position-from:', 'destination:', 'anchor:'
    ]
    
    for css_prop in problematic_css:
        simplified = re.sub(f'{css_prop}[^;]*;?', '', simplified, flags=re.IGNORECASE)
    
    # 移除空的style属性
    simplified = re.sub(r'\s+style=""', '', simplified)
    simplified = re.sub(r"\s+style=''", '', simplified)
    
    # 确保HTML结构简单
    if '<table>' in simplified.lower():
        # 保持表格结构，但简化样式
        simplified = re.sub(r'<table[^>]*>', '<table style="border-collapse:collapse; width:100%; font-family:SimSun,serif;">', simplified, flags=re.IGNORECASE)
        simplified = re.sub(r'<td[^>]*>', '<td style="border:1px solid #000; padding:2px; font-family:SimSun,serif;">', simplified, flags=re.IGNORECASE)
        simplified = re.sub(r'<th[^>]*>', '<th style="border:1px solid #000; padding:2px; font-weight:bold; font-family:SimSun,serif;">', simplified, flags=re.IGNORECASE)
    else:
        # 普通文本，使用简单的div包装
        if not simplified.strip().startswith('<'):
            simplified = f'<div style="font-family:SimSun,serif; font-size:11px;">{simplified}</div>'
    
    logging.info(f"HTML简化完成: 原长度{len(html_content)} -> 简化后长度{len(simplified)}")
    return simplified

def _enhance_html_with_chinese_fonts(html_content: str) -> str:
    """
    为HTML内容添加中文字体支持，同时完全保留VLM返回的原始格式信息
    优化版本：使用PyMuPDF更好支持的字体
    """
    import re
    
    # 使用PyMuPDF更好支持的中文字体序列
    # 优先使用PyMuPDF内置的中文字体，再回退到系统字体
    chinese_fonts = "SimSun, 'Microsoft YaHei', 'SimHei', 'PingFang SC', 'Noto Sans CJK SC', serif"
    
    # 处理内联样式中的font-family
    def enhance_font_family(match):
        style_content = match.group(1)
        
        # 检查是否已经有font-family定义
        if 'font-family:' in style_content.lower():
            # 如果已有font-family，将中文字体放在最前面
            style_content = re.sub(
                r'font-family:\s*([^;]+)',
                f'font-family: {chinese_fonts}, \\1',
                style_content,
                flags=re.IGNORECASE
            )
        else:
            # 如果没有font-family，添加中文字体支持
            if style_content.strip() and not style_content.strip().endswith(';'):
                style_content += '; '
            style_content += f'font-family: {chinese_fonts};'
        
        return f'style="{style_content}"'
    
    # 增强所有带有style属性的标签
    enhanced_html = re.sub(
        r'style="([^"]*)"',
        enhance_font_family,
        html_content,
        flags=re.IGNORECASE
    )
    
    # 为没有style属性的HTML标签添加中文字体支持
    text_tags = ['p', 'h1', 'h2', 'h3', 'h4', 'h5', 'h6', 'div', 'span', 'li', 'td', 'th', 'a']
    
    for tag in text_tags:
        # 匹配没有style属性的标签
        pattern = f'<{tag}(?![^>]*style=)([^>]*)>'
        
        def add_font_style(match):
            existing_attrs = match.group(1)
            return f'<{tag}{existing_attrs} style="font-family: {chinese_fonts};">'
        
        enhanced_html = re.sub(pattern, add_font_style, enhanced_html, flags=re.IGNORECASE)
    
    # 添加一个包装div确保整体字体支持
    if not enhanced_html.strip().startswith('<div'):
        enhanced_html = f'<div style="font-family: {chinese_fonts};">{enhanced_html}</div>'
    
    logging.info(f"HTML字体增强完成，使用PyMuPDF兼容的中文字体序列")
    return enhanced_html

async def _insert_fallback_text(page: fitz.Page, rect: fitz.Rect, translated_content: str):
    """备用文本插入方法，使用基础的textbox，将HTML转换为纯文本"""
    try:
        # 从HTML内容中提取纯文本
        import re
        
        # 先处理一些常见的HTML标签转换
        content = translated_content
        content = re.sub(r'<br\s*/?>', '\n', content)  # 换行
        content = re.sub(r'<p[^>]*>', '\n', content)  # 段落开始
        content = re.sub(r'</p>', '', content)  # 段落结束
        content = re.sub(r'<li[^>]*>', '• ', content)  # 列表项
        content = re.sub(r'</li>', '\n', content)  # 列表项结束
        
        # 移除所有HTML标签
        cleaned_content = re.sub(r'<[^>]+>', '', content)
        
        # 清理多余的空行和空格
        cleaned_content = re.sub(r'\n{3,}', '\n\n', cleaned_content)
        cleaned_content = cleaned_content.strip()
        
        # 计算字体大小
        font_size = min(10, max(6, int(rect.height / 4)))
        
        # 尝试多种字体方案，确保中文显示正确
        font_attempts = [
            ("china-s", "PyMuPDF中文字体"),
            ("china-ss", "PyMuPDF简体中文字体"), 
            ("china-t", "PyMuPDF繁体中文字体"),
            ("cjk", "CJK字体"),
            ("noto-cjk", "Noto CJK字体"),
            ("helv", "默认字体")
        ]
        
        success = False
        for fontname, font_desc in font_attempts:
            try:
                page.insert_textbox(
                    rect,
                    cleaned_content,
                    fontname=fontname,
                    fontsize=font_size,
                    align=0,
                    color=(0, 0, 0)
                )
                logging.info(f"备用文本插入成功（使用{font_desc}: {fontname}）")
                success = True
                break
            except Exception as font_error:
                logging.warning(f"字体 {fontname} ({font_desc}) 插入失败: {font_error}")
                continue
        
        if not success:
            # 最后的备用方案：使用系统默认字体
            page.insert_textbox(
                rect,
                cleaned_content,
                fontsize=font_size,
                align=0,
                color=(0, 0, 0)
            )
            logging.info(f"备用文本插入成功（使用系统默认字体）")
        
    except Exception as e:
        logging.error(f"备用文本插入失败: {e}")
        # 如果连备用方案都失败，记录警告但不抛出异常
        logging.warning(f"区域 {rect} 的文本插入完全失败，跳过该区域")