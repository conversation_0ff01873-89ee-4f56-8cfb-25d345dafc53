import os
from typing import List, Literal, Tuple, Dict
import fitz
import dotenv
from langchain_openai import ChatOpenAI
import base64
import json
import asyncio
import aiofiles
from langgraph.graph import StateGraph
from langgraph.types import Send
from langgraph.types import Command
from langgraph.checkpoint.memory import MemorySaver
from .state import PageAnalysisState, PDFWorkflowState
import time
import re

# 导入项目统一的logger
import sys
from pathlib import Path
project_root = Path(__file__).parent.parent
sys.path.append(str(project_root))
from logger import logger as logging

use_langfuse_prompt = False

dotenv.load_dotenv(override=True)

if use_langfuse_prompt:
    from langfuse import Langfuse
    langfuse = Langfuse(
        public_key=os.getenv("LANGFUSE_PUBLIC_KEY"),
        secret_key=os.getenv("LANGFUSE_SECRET_KEY"),
        host=os.getenv("LANGFUSE_HOST")
    )

# 使用项目统一的loguru logger
# 日志将输出到项目的logs/info.log和logs/error.log文件中
logging.info("PDF翻译工作流模块已加载，使用项目统一logger")

# 导入相关工具
from .utils import (_check_image_size, 
                    _resize_image_if_needed, 
                    _parse_rects_traditional, 
                    MIN_IMAGE_SIZE,
                    remove_region_markers,
                    _validate_coordinate_transform,
                    _expand_bbox_intelligent,
                    _merge_overlapping_expanded_rects,
                    _detect_and_optimize_complex_tables,
                    _extract_region_translated_content,
                    _replace_text_in_region,
                    _detect_and_resolve_overlaps)

async def _vlm_layout_analysis(page_image_path: str, mime_type: str = "image/jpeg") -> List[Dict]:
    """使用VLM分析页面版面，识别不同类型的内容区域"""
    
    # 检查VLM环境变量配置
    vllm_model = os.getenv("VLLM_MODEL")
    vllm_api_key = os.getenv("VLLM_API_KEY") 
    vllm_base_url = os.getenv("VLLM_BASE_URL")
    
    if not all([vllm_model, vllm_api_key, vllm_base_url]):
        logging.error("VLM环境变量配置不完整:")
        logging.error(f"  VLLM_MODEL: {'✅' if vllm_model else '❌'} {vllm_model}")
        logging.error(f"  VLLM_API_KEY: {'✅' if vllm_api_key else '❌'} {'***' if vllm_api_key else 'None'}")
        logging.error(f"  VLLM_BASE_URL: {'✅' if vllm_base_url else '❌'} {vllm_base_url}")
        return []
    
    vllm = ChatOpenAI(model=vllm_model, 
                      api_key=vllm_api_key, 
                      base_url=vllm_base_url,
                      temperature=0.1,
                      extra_body={
        "chat_template_content_format": "raw"
    })
    
    # 直接从传入的图像文件获取实际尺寸（这是VLM将要分析的实际图像尺寸）
    try:
        from PIL import Image
        with Image.open(page_image_path) as actual_img:
            actual_width, actual_height = actual_img.size
        logging.info(f"VLM分析图像实际尺寸: {actual_width}x{actual_height}")
    except Exception as e:
        logging.error(f"无法获取图像实际尺寸: {e}")
        return []
    
    # 检查图像尺寸是否满足VLM要求
    if actual_width < MIN_IMAGE_SIZE or actual_height < MIN_IMAGE_SIZE:
        logging.warning(f"图像尺寸不满足VLM要求: {actual_width}x{actual_height}")
        # 尝试调整图像尺寸
        if not _resize_image_if_needed(page_image_path):
            logging.error(f"无法调整图像尺寸: {page_image_path}")
            return []
        # 重新获取调整后的尺寸
        with Image.open(page_image_path) as adjusted_img:
            actual_width, actual_height = adjusted_img.size
        logging.info(f"图像尺寸已调整为: {actual_width}x{actual_height}")
    
    async with aiofiles.open(page_image_path, 'rb') as image_file:
        image_data = await image_file.read()
        base64_image = base64.b64encode(image_data).decode('utf-8')
    
    # 加载vlm prompt
    try:
        if use_langfuse_prompt:
            vlm_layout_analysis_system_prompt_langfuse = langfuse.get_prompt("VLM_LAYOUT_ANALYSIS_SYSTEM_PROMPT", 
                                                                                label="ori_html_v2",
                                                                                type="chat")
            vlm_layout_analysis_system_prompt_template = vlm_layout_analysis_system_prompt_langfuse.get_langchain_prompt()[0][-1]
        else:
            logging.info("使用本地 VLM_LAYOUT_ANALYSIS_SYSTEM_PROMPT 提示词")
            from .prompt import VLM_LAYOUT_ANALYSIS_SYSTEM_PROMPT
            vlm_layout_analysis_system_prompt_template = VLM_LAYOUT_ANALYSIS_SYSTEM_PROMPT

        # 格式化提示词，插入VLM实际接收到的图像尺寸
        vlm_layout_analysis_system_prompt = vlm_layout_analysis_system_prompt_template.replace('{width}', str(actual_width))
        vlm_layout_analysis_system_prompt = vlm_layout_analysis_system_prompt.replace('{height}', str(actual_height))
        
        logging.info(f"VLM提示词已格式化，使用实际图像尺寸: {actual_width}x{actual_height}")
        logging.info(f"格式化后提示词长度: {len(vlm_layout_analysis_system_prompt)}")
        
        # 验证格式化是否成功
        if '{width}' in vlm_layout_analysis_system_prompt or '{height}' in vlm_layout_analysis_system_prompt:
            logging.warning("提示词中仍包含未替换的变量，检查提示词模板")
        else:
            logging.info("提示词变量替换成功")

    except Exception as prompt_error:
        logging.error(f"获取或格式化VLM提示词失败: {prompt_error}")
        logging.error(f"错误类型: {type(prompt_error).__name__}")
        import traceback
        logging.error(f"完整错误堆栈: {traceback.format_exc()}")
        return []

    messages = [
        {
            "role": "system",
            "content": vlm_layout_analysis_system_prompt
        },
        {
            "role": "user",
            "content": [
                {
                    "type": "image_url",
                    "image_url": {
                        "url": f"data:{mime_type};base64,{base64_image}"
                    }
                }
            ]
        }
    ]
    
    try:
        logging.info(f"开始调用VLM进行版面分析，图像尺寸: {actual_width}x{actual_height}")
        logging.info(f"VLM模型: {vllm_model}")
        logging.info(f"VLM Base URL: {vllm_base_url}")
        
        response = await vllm.ainvoke(messages)
        content = response.content
        
        logging.info(f"VLM响应成功，内容长度: {len(content)}")
        logging.info(f"VLM响应内容前500字符: {content[:500]}")
        
        if '```json' in content:
            json_start = content.find('```json') + 7
            json_end = content.find('```', json_start)
            json_str = content[json_start:json_end]
            logging.info(f"提取JSON代码块: {json_str[:200]}...")
        else:
            json_str = content
            logging.info(f"使用完整响应作为JSON: {json_str[:200]}...")
        
        try:
            parsed_json = json.loads(json_str)
            regions = parsed_json.get('regions', [])
            logging.info(f"JSON解析成功，找到 {len(regions)} 个区域")
        except json.JSONDecodeError as e:
            logging.error(f"JSON解析失败: {e}")
            logging.error(f"原始JSON字符串: {json_str}")
            logging.error(f"尝试使用json_repair修复...")
            
            try:
                from json_repair import repair_json
                repaired_json_str = repair_json(json_str)
                logging.info(f"JSON修复后: {repaired_json_str[:200]}...")
                parsed_json = json.loads(repaired_json_str)
                regions = parsed_json.get('regions', [])
                logging.info(f"JSON修复成功，找到 {len(regions)} 个区域")
            except Exception as repair_error:
                logging.error(f"JSON修复也失败: {repair_error}")
                logging.error(f"完整VLM响应内容: {content}")
                return []

        if len(regions) == 0:
            logging.error(f"VLM版面分析返回空区域列表")
            logging.error(f"完整响应内容: {content}")
            return []
        
        logging.info(f"VLM版面分析成功，返回 {len(regions)} 个区域")
        return regions
        
        
    except Exception as e:
        error_msg = str(e)
        logging.error(f"VLM调用异常: {error_msg}")
        logging.error(f"异常类型: {type(e).__name__}")
        
        if "height" in error_msg and "width" in error_msg and "must be larger than" in error_msg:
            logging.error(f"页面图片尺寸不满足Qwen-VL要求: {error_msg}")
            logging.error(f"图片路径: {page_image_path}")
            _, (w, h) = _check_image_size(page_image_path)
            logging.error(f"当前页面图片实际尺寸: {w}x{h}")
        else:
            logging.error(f"VLM版面分析失败: {e}")
            # 添加更详细的错误信息
            import traceback
            logging.error(f"完整错误堆栈: {traceback.format_exc()}")
        return []

def encode_html_tags(text: str) -> str:
    """将HTML标签编码为HTML实体，避免在vLLM翻译时被过滤"""
    html_tag_mapping = {
        '<': '&lt;',
        '>': '&gt;',
        '"': '&quot;',
        "'": '&apos;'
    }
    
    encoded_text = text
    for char, entity in html_tag_mapping.items():
        encoded_text = encoded_text.replace(char, entity)
    
    logging.info(f"HTML标签编码完成，原文长度: {len(text)}, 编码后长度: {len(encoded_text)}")
    return encoded_text

def decode_html_tags(text: str) -> str:
    """将HTML实体解码回HTML标签，支持双重编码的情况"""
    # 处理双重编码的情况（LLM将&再次编码为&amp;）
    double_encoded_mapping = {
        '&amp;lt;': '<',      # &lt; -> &amp;lt; -> <
        '&amp;gt;': '>',      # &gt; -> &amp;gt; -> >
        '&amp;quot;': '"',    # &quot; -> &amp;quot; -> "
        '&amp;apos;': "'",    # &apos; -> &amp;apos; -> '
    }
    
    # 单重编码的情况
    single_encoded_mapping = {
        '&lt;': '<',
        '&gt;': '>',
        '&quot;': '"',
        '&apos;': "'"
    }
    
    decoded_text = text
    changes_made = 0
    
    # 首先处理双重编码
    for double_entity, char in double_encoded_mapping.items():
        if double_entity in decoded_text:
            count = decoded_text.count(double_entity)
            decoded_text = decoded_text.replace(double_entity, char)
            changes_made += count
            logging.info(f"双重编码解码: {double_entity} -> {char}, 替换次数: {count}")
    
    # 然后处理单重编码
    for entity, char in single_encoded_mapping.items():
        if entity in decoded_text:
            count = decoded_text.count(entity)
            decoded_text = decoded_text.replace(entity, char)
            changes_made += count
            logging.info(f"单重编码解码: {entity} -> {char}, 替换次数: {count}")
    
    logging.info(f"HTML标签解码完成，编码前长度: {len(text)}, 解码后长度: {len(decoded_text)}, 总替换次数: {changes_made}")
    return decoded_text

async def translate_text(input_text: str, language: str) -> str:
    """翻译文本"""
    print("Running async translate_text(input_text)")

    # 加载翻译prompts
    try:
        if use_langfuse_prompt:
            translation_system_prompt_langfuse = langfuse.get_prompt("TRANSLATION_SYSTEM_PROMPT", 
                                                                    label="ori_html_v4",
                                                                    type="chat")
            translation_system_prompt = translation_system_prompt_langfuse.get_langchain_prompt()[0][-1]
        else:
            logging.info("使用本地 TRANSLATION_SYSTEM_PROMPT_WITH_HTML_ENCODING 提示词")
            from .prompt import TRANSLATION_SYSTEM_PROMPT_WITH_HTML_ENCODING
            translation_system_prompt = TRANSLATION_SYSTEM_PROMPT_WITH_HTML_ENCODING
        translation_system_prompt = translation_system_prompt.format(language=language)
        logging.info(f"翻译提示词模板获取成功，长度: {len(translation_system_prompt)}")
        
    except Exception as prompt_error:
        logging.error(f"获取翻译提示词失败: {prompt_error}")
        logging.error(f"错误类型: {type(prompt_error).__name__}")
        import traceback
        logging.error(f"完整错误堆栈: {traceback.format_exc()}")
        return ""

    # 对输入文本进行HTML标签编码，避免vLLM过滤
    encoded_input_text = encode_html_tags(input_text)
    logging.info(f"翻译前HTML编码处理完成，原长度: {len(input_text)}, 编码后长度: {len(encoded_input_text)}")
    
    messages = [{
        'role': 'system', 
        'content': translation_system_prompt
    },
    {
        'role': 'user', 
        'content': encoded_input_text
    }
    ]
    llm = ChatOpenAI(model=os.getenv("OPENAI_MODEL"), 
                    temperature=0.1,
                    api_key=os.getenv("OPENAI_API_KEY"),
                    base_url=os.getenv("OPENAI_BASE_URL"),
                    extra_body={
        "chat_template_content_format": "raw"
    })
    response = await llm.ainvoke(messages)
    logging.info(f"Translation result: {response}")
    
    # 对翻译结果进行HTML标签解码，恢复HTML格式
    decoded_content = decode_html_tags(response.content)
    logging.info(f"翻译后HTML解码处理完成，编码前长度: {len(response.content)}, 解码后长度: {len(decoded_content)}")
    
    return decoded_content

async def initialize_workflow(state) -> Command[Literal["extract_pdf_pages"]]:
    """初始化工作流，设置基本参数并创建输出目录"""
    logging.info("开始初始化PDF工作流")
    
    # 创建输出目录
    if not os.path.exists(state["output_dir"]):
        os.makedirs(state["output_dir"])
    
    # 打开PDF文件获取总页数
    pdf_document = fitz.open(state["pdf_path"])
    total_pages = len(pdf_document)
    pdf_document.close()
    
    logging.info(f"PDF文件包含 {total_pages} 页")
    
    return Command(
        goto="extract_pdf_pages",
        update={
        **state,
        "total_pages": total_pages,
        "pages_info": [],
        "page_analysis_results_translated": [],
        "all_completed_pages": [],
        "final_content": "",
        "all_rect_images": [],
        "translated_pdf_path": "",
        # 设置bbox扩展的默认配置
        "enable_intelligent_bbox_expansion": state.get("enable_intelligent_bbox_expansion", True),
        "bbox_overlap_merge_threshold": state.get("bbox_overlap_merge_threshold", 0.8),
        "enable_complex_table_optimization": state.get("enable_complex_table_optimization", True),
        "image_format": state.get("image_format", "png")  # 设置图像格式默认值
        }
    )

async def extract_pdf_pages(state) -> Command[Literal["dispatch_page_processing"]]:
    """提取PDF页面并生成基本信息，为并行处理做准备"""
    logging.info("开始提取PDF页面信息")
    
    pdf_document = fitz.open(state["pdf_path"])
    pages_info = []
    
    for page_index in range(state["total_pages"]):
        page_info = {
            "page_index": page_index,
            "page_image_path": os.path.join(state["output_dir"], f'{page_index}.png'),
        }
        pages_info.append(page_info)
    
    pdf_document.close()
    
    return Command(
        goto="dispatch_page_processing",
        update={
            "pages_info": pages_info
        }
    )

async def dispatch_page_processing(state) -> Command[Literal["process_batch_pages"]]:
    """分发页面完整处理任务（VLM分析+翻译），使用简化的批次处理"""
    pages_info = state["pages_info"]
    total_pages = len(pages_info)
    
    logging.info(f"开始分发 {total_pages} 个页面流水线处理任务（最大并发：2）")
    
    # 使用简单的页面索引列表，避免复杂的批次管理
    return Command(
        goto="process_batch_pages",
        update={
            "remaining_page_indices": list(range(total_pages)),  # 剩余待处理页面索引
            "max_concurrent": 1
        }
    )

async def process_batch_pages(state) -> Command[Literal["process_single_page_complete", "finalize_results"]]:
    """处理当前批次的页面，限制并发数量为2"""
    max_concurrent = state.get("max_concurrent", 2)
    remaining_indices = state.get("remaining_page_indices", [])
    pages_info = state["pages_info"]
    total_pages = len(pages_info)
    
    # 调试状态
    logging.info(f"process_batch_pages收到状态: remaining_page_indices={remaining_indices}, 长度={len(remaining_indices)}")
    logging.info(f"状态中的其他字段: max_concurrent={state.get('max_concurrent')}, all_completed_pages长度={len(state.get('all_completed_pages', []))}")
    
    # 检查是否还有页面需要处理
    if not remaining_indices:
        logging.info("所有页面处理完成，开始汇总结果")
        return Command(goto="finalize_results")
    
    # 取前max_concurrent个页面进行处理
    current_batch_indices = remaining_indices[:max_concurrent]
    remaining_after_batch = remaining_indices[max_concurrent:]
    
    logging.info(f"处理页面: {[i+1 for i in current_batch_indices]} / {total_pages}")
    logging.info(f"剩余页面: {len(remaining_after_batch)} 个")
    
    sends = []
    for page_idx in current_batch_indices:
        page_info = pages_info[page_idx]
        page_state = PageAnalysisState(
            page_index=page_info["page_index"],
            page_image_path=page_info["page_image_path"],
            rect_images=[],
            region_info={},
            region_bboxes=[],  
            extracted_content="",
            translated_content=""
        )
        
        # 创建Send对象，将页面处理任务发送到process_single_page_complete节点
        sends.append(Send("process_single_page_complete", {
            **state,
            "current_page": page_state
            # 不在Send中更新remaining_page_indices，避免状态累加
        }))
    
    return Command(goto=sends)

async def process_single_page_complete(state) -> Command[Literal["collect_and_continue"]]:
    """完整处理单个页面：VLM版式分析 + 内容翻译（流水线处理）"""
    main_state = {k: v for k, v in state.items() if k != "current_page"}
    page_state = state["current_page"]
    page_index = page_state["page_index"]
    
    logging.info(f'开始分析页面: {page_index}')
    
    # 打开PDF文件并获取指定页面
    pdf_document = fitz.open(main_state["pdf_path"])
    page = pdf_document.load_page(page_index)
    
    try:
        # 根据配置选择分析方法
        if main_state["use_vlm_layout"]:
            # 使用VLM智能分析，可配置缩放因子
            scale_factor = main_state.get("vlm_scale_factor", 1.0)  # 默认不缩放
            enable_bbox_expansion = main_state.get("enable_intelligent_bbox_expansion", True)  # 默认启用bbox扩展
            merge_threshold = main_state.get("bbox_overlap_merge_threshold", 0.8)  # 默认80%重叠时合并
            enable_table_optimization = main_state.get("enable_complex_table_optimization", True) # 默认启用复杂表格优化
            image_format = main_state.get("image_format", "png")  # 默认使用PNG格式
            
            rects, region_info = await _vlm_parse_rects_intelligent(
                page, main_state["output_dir"], page_index, scale_factor, enable_bbox_expansion, merge_threshold, enable_table_optimization, image_format
            )
        else:
            # 使用传统几何分析
            traditional_rects = [rect.bounds for rect in _parse_rects_traditional(page)]
            rects = traditional_rects
            region_info = {
                i: {
                    'type': 'unknown',
                    'description': f'区域 {i}',
                    'full_content': f'区域 {i} 内容'
                } for i in range(len(traditional_rects))
            }
            scale_factor = main_state.get("vlm_scale_factor", 1.0)  # 传统方法也使用相同的缩放因子
        
        # 生成矩形区域图片（使用固定的高质量缩放，与VLM分析的缩放因子独立）
        rect_images = []
        # 区域图片使用固定的2倍缩放以确保质量，避免与VLM缩放因子混淆
        rect_scale_factor = 2.0
        logging.info(f"页面 {page_index} 区域图片使用固定 {rect_scale_factor}x 缩放")
        
        # 创建images子目录
        images_dir = os.path.join(main_state["output_dir"], "images")
        os.makedirs(images_dir, exist_ok=True)
        
        for index, rect in enumerate(rects):
            fitz_rect = fitz.Rect(rect)
            if rect_scale_factor != 1.0:
                pix = page.get_pixmap(clip=fitz_rect, matrix=fitz.Matrix(rect_scale_factor, rect_scale_factor))
            else:
                pix = page.get_pixmap(clip=fitz_rect)
            name = f'{page_index}_{index}.png'
            image_path = os.path.join(images_dir, name)
            pix.save(image_path)
            
            # 添加图片到rect_images列表
            rect_images.append(f"images/{name}")
            
            # 在页面上绘制红色矩形标记
            # 重新优化线宽和字体计算：更加保守的策略
            if scale_factor <= 1.0:
                line_width = 1
                font_size = 8
            elif scale_factor <= 2.0:
                line_width = 1
                font_size = min(12, int(8 + scale_factor * 2))  # 8-12号字体
            else:
                # 对于大缩放因子，使用非常保守的增长
                line_width = min(2, max(1, int(scale_factor * 0.3)))  # 最大线宽限制为2
                font_size = min(14, max(8, int(8 + scale_factor * 1.2)))  # 字体大小限制在8-14
            
            logging.info(f"页面 {page_index} 缩放因子 {scale_factor}x，线宽: {line_width}，字体: {font_size}")
            
            # 使用优化的边框样式减少遮挡
            big_fitz_rect = fitz.Rect(fitz_rect.x0 - 1, fitz_rect.y0 - 1, fitz_rect.x1 + 1, fitz_rect.y1 + 1)
            
            # 绘制红色边框 - 简化绘制逻辑，确保可见性
            try:
                # 始终使用实线，确保红框正常显示
                page.draw_rect(big_fitz_rect, color=(1, 0, 0), width=line_width)
                logging.info(f"区域 {index} 红框绘制成功: 线宽{line_width}, 坐标{big_fitz_rect}")
            except Exception as e:
                logging.error(f"区域 {index} 红框绘制失败: {e}")
                # 尝试备用绘制方式
                try:
                    page.draw_rect(big_fitz_rect, color=(1, 0, 0), width=1)
                    logging.info(f"区域 {index} 使用备用方式绘制红框成功")
                except Exception as e2:
                    logging.error(f"区域 {index} 备用红框绘制也失败: {e2}")
            
            # 改进文字标签位置：放在左上角外侧，避免遮挡内容
            text_margin = 1
            text_x = max(2, fitz_rect.x0 + 2)  # 放在矩形内侧左上角
            text_y = max(font_size + 2, fitz_rect.y0 + font_size + 2)  # 确保文字不被截断
            
            # 计算文字背景大小（更保守的估算）
            text_width = len(name) * (font_size * 0.5)  # 更保守的宽度估算
            text_height = font_size + 2
            
            # 绘制半透明文字背景
            try:
                text_rect = fitz.Rect(text_x - text_margin, text_y - text_height, 
                                    text_x + text_width + text_margin, text_y + 2)
                page.draw_rect(text_rect, color=(1, 1, 1), fill=(1, 1, 1), width=0)
                
                # 插入文字标签
                page.insert_text((text_x, text_y), name, fontsize=font_size, color=(0.8, 0, 0))
                logging.info(f"区域 {index} 文字标签绘制成功: '{name}', 字体{font_size}, 位置({text_x:.1f}, {text_y:.1f})")
                
            except Exception as e:
                logging.error(f"区域 {index} 文字标签绘制失败: {e}")
                # 尝试简化的文字绘制
                try:
                    page.insert_text((text_x, text_y), name, fontsize=8, color=(1, 0, 0))
                    logging.info(f"区域 {index} 使用简化方式绘制文字标签")
                except Exception as e2:
                    logging.error(f"区域 {index} 简化文字绘制也失败: {e2}")
        
        # 保存标记后的页面图片（必须使用相同的坐标系）
        # 如果使用了缩放分析，则使用相同的缩放因子保存预览图以保持坐标一致性
        if scale_factor != 1.0:
            # 使用与VLM分析相同的缩放因子，确保红框位置正确
            scale_matrix = fitz.Matrix(scale_factor, scale_factor)
            page_image_with_rects = page.get_pixmap(matrix=scale_matrix)
            logging.info(f"页面 {page_index} 使用 {scale_factor}x 缩放保存预览图以匹配红框坐标")
        else:
            page_image_with_rects = page.get_pixmap()
            logging.info(f"页面 {page_index} 使用原始尺寸保存预览图")
        page_image_path = os.path.join(main_state["output_dir"], f'{page_index}.png')
        page_image_with_rects.save(page_image_path)
        
        # 立即提取内容
        all_content_parts = []
        logging.info(f"页面 {page_index} 共识别到 {len(rect_images)} 个区域")
        
        for rect_index, rect_image in enumerate(rect_images):
            region_data = region_info.get(rect_index, {})
            region_type = region_data.get('type', 'unknown')
            region_description = region_data.get('full_content', '')
            
            logging.info(f"处理页面 {page_index} 区域 {rect_index}，类型: {region_type}")
            
            # 生成特殊分割符标记（避免HTML实体编码问题）
            region_start_marker = f"[[REGION_START_{page_index}_{rect_index}_{region_type}]]"
            region_end_marker = f"[[REGION_END_{page_index}_{rect_index}_{region_type}]]"
            
            # 对于图像类型，直接生成HTML图片引用
            if region_type == "image":
                if "<img src=" in region_description:
                    # VLM已经返回了HTML格式，替换src路径并包装在容器中
                    html_image = re.sub(r'<img src="[^"]*"', f'<img src="{rect_image}"', region_description)
                    # 提取alt内容作为图像标题
                    alt_match = re.search(r'alt="([^"]*)"', html_image)
                    caption = alt_match.group(1) if alt_match else ""
                    if caption:
                        html_image = f'<div class="image-container">{html_image}<p class="image-caption" style="font-size:12px; color:#666; margin:5px 0; text-align:center;">{caption}</p></div>'
                    else:
                        html_image = f'<div class="image-container">{html_image}</div>'
                else:
                    # VLM没有返回HTML格式，手动创建图像标签
                    html_image = f'<div class="image-container"><img src="{rect_image}" alt="{region_description}" style="max-width:100%; height:auto; border:1px solid #ccc; margin:5px 0;"><p class="image-caption" style="font-size:12px; color:#666; margin:5px 0; text-align:center;">{region_description}</p></div>'
                formatted_content = f"{region_start_marker}\n\n{html_image}\n\n{region_end_marker}"
                all_content_parts.append(formatted_content)
                logging.info(f"页面 {page_index} 区域 {rect_index} 图像HTML引用: {rect_image}")
                continue
            
            # 对于其他类型，直接使用VLM布局分析的描述作为内容
            if region_description:
                formatted_content = f"{region_start_marker}\n\n{region_description}\n\n{region_end_marker}"
                all_content_parts.append(formatted_content)
                logging.info(f"页面 {page_index} 区域 {rect_index} ({region_type}) 使用VLM HTML布局分析结果")
        
        # 生成页面内容
        if all_content_parts:
            extracted_content = '\n\n'.join(all_content_parts)
        else:
            logging.warning(f"页面 {page_index} 没有识别到任何内容")
            extracted_content = ""
        
        # 清理可能的HTML代码块标记（如果VLM包装了HTML代码块）
        if '```html' in extracted_content:
            extracted_content = extracted_content.replace('```html\n', '')
            last_backticks_pos = extracted_content.rfind('```')
            if last_backticks_pos != -1:
                extracted_content = extracted_content[:last_backticks_pos] + extracted_content[last_backticks_pos + 3:]
        
        # 更新页面状态，包含提取的内容和边界框信息
        updated_page_state = PageAnalysisState(
            page_index=page_index,
            page_image_path=page_image_path,
            rect_images=rect_images,
            region_info=region_info,
            region_bboxes=rects,
            extracted_content=extracted_content,
            translated_content=""
        )
        
        # 发送VLM分析完成的进度通知
        # 使用全局回调函数避免序列化问题
        from .main import get_global_progress_callback
        progress_callback = get_global_progress_callback()
        callback_total_pages = main_state.get("callback_total_pages", main_state.get("total_pages", 1))
        
        if progress_callback:
            try:
                await progress_callback(page_index, callback_total_pages, "analysis_complete")
                logging.info(f'页面 {page_index} VLM分析进度通知已发送')
            except Exception as e:
                logging.error(f"进度回调失败 (analysis_complete): {e}")
        
        # 立即开始翻译该页面内容（流水线处理）
        language = main_state.get("language", "中文")
        logging.info(f'页面 {page_index} VLM分析完成，立即开始翻译')
        
        if extracted_content:
            translated_content = await translate_text(extracted_content, language)
            logging.info(f'页面 {page_index} 翻译完成，长度: {len(translated_content)}')
        else:
            translated_content = ""
            logging.warning(f'页面 {page_index} 无内容可翻译')
        
        # 发送翻译完成的进度通知
        if progress_callback:
            try:
                await progress_callback(page_index, callback_total_pages, "translation_complete")
                logging.info(f'页面 {page_index} 翻译进度通知已发送')
            except Exception as e:
                logging.error(f"进度回调失败 (translation_complete): {e}")
        
        # 更新页面状态，包含翻译结果
        final_page_state = PageAnalysisState(
            page_index=page_index,
            page_image_path=page_image_path,
            rect_images=rect_images,
            region_info=region_info,
            region_bboxes=rects,
            extracted_content=extracted_content,
            translated_content=translated_content
        )
        
        return Command(
            goto="collect_and_continue",
            update={
                "page_analysis_results_translated": [final_page_state]
            }
        )
        
    finally:
        pdf_document.close()

async def collect_and_continue(state) -> Command[Literal["process_batch_pages", "finalize_results"]]:
    """收集页面处理结果，并决定是否继续处理剩余页面"""
    # 收集当前批次完成的页面结果
    current_batch_pages = state.get("page_analysis_results_translated", [])
    all_completed_pages = state.get("all_completed_pages", [])
    remaining_indices = state.get("remaining_page_indices", [])
    max_concurrent = state.get("max_concurrent", 2)
    
    # 累积所有已完成的页面
    all_completed_pages.extend(current_batch_pages)
    
    # 从remaining_indices中移除已处理的页面
    # 基于实际完成的页面索引进行精确匹配，避免计数错误
    completed_page_indices = []
    for page in current_batch_pages:
        if isinstance(page, dict) and "page_index" in page:
            completed_page_indices.append(page["page_index"])
        elif hasattr(page, 'page_index'):
            completed_page_indices.append(page.page_index)
    
    # 只移除真正完成的页面索引
    updated_remaining_indices = [idx for idx in remaining_indices if idx not in completed_page_indices]
    
    actual_completed_count = len(completed_page_indices)
    expected_process_count = min(len(remaining_indices), max_concurrent)
    
    logging.info(f"本批次实际完成: {actual_completed_count} 页面，预期处理: {expected_process_count} 页面")
    logging.info(f"完成页面索引: {completed_page_indices}")
    logging.info(f"原剩余索引: {remaining_indices}")
    logging.info(f"新剩余索引: {updated_remaining_indices}")
    
    total_pages = len(state.get("pages_info", []))
    completed_count = len(all_completed_pages)
    remaining_count = len(updated_remaining_indices)
    
    logging.info(f"批次完成: {len(current_batch_pages)} 页面处理完毕")
    logging.info(f"总进度: {completed_count}/{total_pages} 页面完成，剩余: {remaining_count} 页面")
    
    # 检查是否还有页面需要处理
    if updated_remaining_indices:
        # 检测是否可能陷入循环：剩余页面数量没有减少
        if len(updated_remaining_indices) == len(remaining_indices):
            logging.warning(f"检测到潜在循环：剩余页面数量未减少 ({len(remaining_indices)} -> {len(updated_remaining_indices)})")
            logging.warning(f"当前批次完成页面数: {actual_completed_count}，预期: {expected_process_count}")
            
            # 如果没有页面完成且剩余页面数量未变，强制结束避免无限循环
            if actual_completed_count == 0:
                logging.error("强制结束：没有页面成功完成，避免无限循环")
                return Command(
                    goto="finalize_results",
                    update={
                        "page_analysis_results_translated": all_completed_pages,
                        "error_message": f"部分页面处理失败，强制结束。未完成页面: {updated_remaining_indices}"
                    }
                )
        
        # 继续处理剩余页面
        logging.info(f"继续处理剩余页面: {[i+1 for i in updated_remaining_indices[:max_concurrent]]}...")
        return Command(
            goto="process_batch_pages",
            update={
                "all_completed_pages": all_completed_pages,
                "remaining_page_indices": updated_remaining_indices,
                "page_analysis_results_translated": "DELETE"  # 清除当前批次结果
            }
        )
    else:
        # 所有页面都已完成，进入汇总阶段
        logging.info("所有页面处理完成，开始最终汇总")
        return Command(
            goto="finalize_results",
            update={
                "page_analysis_results_translated": all_completed_pages
            }
        )

async def _vlm_parse_rects_intelligent(page: fitz.Page, output_dir: str, page_index: int, scale_factor: float = 1.0, enable_bbox_expansion: bool = True, merge_threshold: float = 0.8, enable_table_optimization: bool = True, image_format: str = "png") -> Tuple[List[Tuple[float, float, float, float]], Dict[int, Dict[str, str]]]:
    """使用VLM智能分析页面布局，识别内容区域"""
    # 获取页面边界用于验证
    page_bounds = page.bound()
    logging.info(f"页面 {page_index} 边界: {page_bounds}")
    
    # 创建缩放矩阵
    scale_matrix = fitz.Matrix(scale_factor, scale_factor)
    
    # 可配置的缩放因子，默认不缩放
    if scale_factor != 1.0:
        page_image = page.get_pixmap(matrix=scale_matrix)
        logging.info(f"页面 {page_index} 使用 {scale_factor}x 缩放进行VLM分析")
    else:
        page_image = page.get_pixmap()
        logging.info(f"页面 {page_index} 使用原始尺寸进行VLM分析")
    
    # 根据配置选择图像格式
    from PIL import Image
    img = Image.frombytes("RGB", [page_image.width, page_image.height], page_image.samples)
    
    if image_format.lower() == "jpeg":
        temp_page_path = os.path.join(output_dir, f'temp_{page_index}.jpeg')
        img.save(temp_page_path, "JPEG", quality=100)  # 使用高质量JPEG
        mime_type = "image/jpeg"
        logging.info(f"页面 {page_index} 使用JPEG格式保存（质量100%）")
    else:  # 默认使用PNG
        temp_page_path = os.path.join(output_dir, f'temp_{page_index}.png')
        # 如果图像很大，使用优化的PNG保存
        if img.width * img.height > 2000000:  # 超过200万像素时优化
            img.save(temp_page_path, "PNG", optimize=True, compress_level=6)
            logging.info(f"页面 {page_index} 使用PNG优化保存（大图像）")
        else:
            img.save(temp_page_path, "PNG")
            logging.info(f"页面 {page_index} 使用PNG标准保存")
        mime_type = "image/png"
    
    try:
        regions = await _vlm_layout_analysis(temp_page_path, mime_type)
        
        rects = []
        region_info = {}
        for index, region in enumerate(regions):
            bbox = region.get('bbox', [])
            if len(bbox) == 4:
                # 如果使用了缩放，需要使用逆变换矩阵将坐标还原到原始尺寸
                if scale_factor != 1.0:
                    # 创建缩放后图像坐标系中的矩形
                    scaled_rect = fitz.Rect(bbox[0], bbox[1], bbox[2], bbox[3])
                    # 使用逆变换矩阵转换回原始页面坐标
                    inverse_matrix = ~scale_matrix  # PyMuPDF中 ~ 表示逆矩阵
                    original_rect = scaled_rect * inverse_matrix
                    x1, y1, x2, y2 = original_rect.x0, original_rect.y0, original_rect.x1, original_rect.y1
                    
                    # 详细的坐标转换验证日志
                    logging.info(f"坐标转换验证 - 区域 {index}:")
                    logging.info(f"  VLM返回(缩放后): {bbox}")
                    logging.info(f"  缩放因子: {scale_factor}")
                    logging.info(f"  转换后(原始): ({x1:.2f}, {y1:.2f}, {x2:.2f}, {y2:.2f})")
                    logging.info(f"  区域大小: {x2-x1:.2f} x {y2-y1:.2f}")
                    
                    # 坐标合理性检查和裁剪
                    is_valid, (x1, y1, x2, y2) = _validate_coordinate_transform((x1, y1, x2, y2), scale_factor, page_bounds)
                    if not is_valid:
                        logging.error(f"坐标转换验证失败！区域 {index}: ({x1:.2f}, {y1:.2f}, {x2:.2f}, {y2:.2f}) - 跳过此区域")
                        continue
                else:
                    x1, y1, x2, y2 = bbox
                    logging.info(f"无缩放 - 区域 {index} 坐标: ({x1:.2f}, {y1:.2f}, {x2:.2f}, {y2:.2f})")
                    
                    # 即使无缩放也要验证和裁剪坐标
                    is_valid, (x1, y1, x2, y2) = _validate_coordinate_transform((x1, y1, x2, y2), 1.0, page_bounds)
                    if not is_valid:
                        logging.error(f"原始坐标验证失败！区域 {index}: ({x1:.2f}, {y1:.2f}, {x2:.2f}, {y2:.2f}) - 跳过此区域")
                        continue
                
                # 获取区域类型用于智能扩展
                region_type = region.get('type', 'unknown')
                
                # 根据配置决定是否应用智能边界扩展
                if enable_bbox_expansion:
                    expanded_bbox = _expand_bbox_intelligent(
                        (x1, y1, x2, y2), 
                        region_type, 
                        scale_factor, 
                        page_bounds
                    )
                    rects.append(expanded_bbox)
                else:
                    # 不扩展，使用原始坐标
                    logging.info(f"跳过边界扩展，使用原始坐标: ({x1:.2f}, {y1:.2f}, {x2:.2f}, {y2:.2f})")
                    rects.append((x1, y1, x2, y2))
                
                region_info[index] = {
                    'type': region.get('type', 'unknown'),
                    'full_content': region.get('full_content', '')
                }
                
                logging.info(f"VLM识别区域 {index}: {region.get('type')} - {region.get('full_content', '')}")
        
        # 合并过度重叠的扩展后区域，避免redaction时重复覆盖
        if len(rects) > 1 and merge_threshold < 1.0:
            logging.info(f"页面 {page_index} 合并前共 {len(rects)} 个区域")
            merged_rects, merged_region_info = _merge_overlapping_expanded_rects(rects, region_info, merge_threshold)
            if len(merged_rects) < len(rects):
                logging.info(f"页面 {page_index} 合并后剩 {len(merged_rects)} 个区域")
                # 更新变量以便后续处理
                rects, region_info = merged_rects, merged_region_info
        
        # 检测并优化复杂表格区域（根据配置决定是否启用）
        if enable_table_optimization and any(info.get('type') == 'table' for info in region_info.values()):
            logging.info(f"页面 {page_index} 检测到表格区域，开始复杂表格优化")
            optimized_rects, optimized_region_info = _detect_and_optimize_complex_tables(rects, region_info, page_bounds)
            if optimized_rects != rects:
                logging.info(f"页面 {page_index} 表格区域已优化")
                rects, region_info = optimized_rects, optimized_region_info
        elif not enable_table_optimization:
            logging.info(f"页面 {page_index} 复杂表格优化功能已禁用")
        
        # 最终的重叠检测
        if len(rects) > 1 and merge_threshold < 1.0:
            logging.info(f"页面 {page_index} 开始重叠检测和解决")
            final_rects, final_region_info = _detect_and_resolve_overlaps(rects, region_info, page_bounds)
            if len(final_rects) != len(rects):
                logging.info(f"页面 {page_index} 重叠解决完成: {len(rects)} -> {len(final_rects)} 个区域")
                rects, region_info = final_rects, final_region_info
            else:
                logging.info(f"页面 {page_index} 未发现重叠问题")
        
        return rects, region_info
        
    except Exception as e:
        logging.error(f"VLM智能标记失败，回退到传统方法: {e}")
        traditional_rects = [rect.bounds for rect in _parse_rects_traditional(page)]
        traditional_info = {
            i: {
                'type': 'unknown',
                'description': f'区域 {i}',
                'content': f'区域 {i} 内容'
            } for i in range(len(traditional_rects))
        }
        return traditional_rects, traditional_info
        
    finally:
        if os.path.exists(temp_page_path):
            os.remove(temp_page_path)

async def async_remove_file(file_path: str) -> None:
    """异步删除文件"""
    try:
        loop = asyncio.get_event_loop()
        await loop.run_in_executor(None, os.remove, file_path)
        logging.info(f"已删除临时文件: {file_path}")
    except Exception as e:
        logging.error(f"删除文件失败 {file_path}: {e}")

# 移除了dispatch_translation和translate_page_content函数
# 翻译功能已集成到process_single_page_complete中，实现真正的流水线处理

async def finalize_results(state) -> Command[Literal["generate_translated_pdf", "__end__"]]:
    """汇总所有页面的翻译结果"""
    logging.info("开始汇总翻译结果")
    
    # 按页面索引排序
    sorted_results = sorted(state["page_analysis_results_translated"], key=lambda x: x["page_index"])
    
    # 收集所有翻译内容
    all_translated_contents = []
    all_rect_images = []
    
    for page_result in sorted_results:
        if page_result["translated_content"]:
            all_translated_contents.append(page_result["translated_content"])
        all_rect_images.extend(page_result["rect_images"])
    
    # 生成最终内容
    final_content = '\n\n'.join(all_translated_contents)
    
    # 清理特殊分割符
    cleaned_final_content = remove_region_markers(final_content)
    
    # 创建完整的HTML文档，包含CSS样式
    html_document = f"""<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>{state["file_name_without_ext"]} - 翻译结果</title>
    <style>
        body {{
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            line-height: 1.6;
            color: #333;
            max-width: 1200px;
            margin: 0 auto;
            padding: 20px;
            background-color: #f8f9fa;
        }}
        
        .container {{
            background-color: white;
            padding: 30px;
            border-radius: 10px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        }}
        
        h1, h2, h3, h4, h5, h6 {{
            color: #2c3e50;
            margin-top: 25px;
            margin-bottom: 15px;
        }}
        
        p {{
            margin-bottom: 15px;
        }}
        
        table {{
            width: 100%;
            border-collapse: collapse;
            margin: 20px 0;
            background-color: white;
            box-shadow: 0 1px 3px rgba(0,0,0,0.1);
        }}
        
        table th, table td {{
            border: 1px solid #dee2e6;
            padding: 12px;
            text-align: left;
        }}
        
        table th {{
            background-color: #f8f9fa;
            font-weight: bold;
            color: #495057;
        }}
        
        table tr:nth-child(even) {{
            background-color: #f8f9fa;
        }}
        
        .image-container {{
            margin: 20px 0;
            text-align: center;
            background-color: #f8f9fa;
            padding: 15px;
            border-radius: 8px;
            border: 1px solid #dee2e6;
        }}
        
        .image-container img {{
            max-width: 100%;
            height: auto;
            border: 1px solid #ccc;
            border-radius: 4px;
            box-shadow: 0 2px 8px rgba(0,0,0,0.1);
        }}
        
        .image-caption {{
            font-style: italic;
            color: #6c757d;
            margin-top: 10px;
            font-size: 0.9em;
        }}
        
        .image-placeholder {{
            border: 2px dashed #6c757d;
            padding: 20px;
            margin: 20px 0;
            background-color: #f8f9fa;
            text-align: center;
            color: #6c757d;
            border-radius: 8px;
        }}
        
        ul, ol {{
            margin-bottom: 15px;
            padding-left: 30px;
        }}
        
        li {{
            margin-bottom: 5px;
        }}
        
        .formula {{
            background-color: #f8f9fa;
            padding: 10px;
            border-radius: 4px;
            border-left: 4px solid #007bff;
            margin: 15px 0;
            font-family: 'Courier New', monospace;
        }}
        
        .page-break {{
            border-top: 2px solid #dee2e6;
            margin: 40px 0;
            padding-top: 20px;
        }}
        
        .header {{
            text-align: center;
            margin-bottom: 30px;
            border-bottom: 2px solid #dee2e6;
            padding-bottom: 20px;
        }}
        
        .footer {{
            text-align: center;
            margin-top: 40px;
            padding-top: 20px;
            border-top: 1px solid #dee2e6;
            color: #6c757d;
            font-size: 0.9em;
        }}
    </style>
</head>
<body>
    <div class="container">
        <div class="header">
            <h1>{state["file_name_without_ext"]} - 翻译结果</h1>
            <p style="color: #6c757d;">由PDF智能翻译助手生成</p>
        </div>
        
        <div class="content">
{cleaned_final_content}
        </div>
        
        <div class="footer">
            <p>翻译完成时间: {time.strftime('%Y-%m-%d %H:%M:%S')}</p>
        </div>
    </div>
</body>
</html>"""

    # 异步保存到文件
    output_path = os.path.join(state["output_dir"], f'{state["file_name_without_ext"]}_done_translated.html')
    async with aiofiles.open(output_path, 'w', encoding='utf-8') as f:
        await f.write(html_document)
    
    logging.info(f"翻译结果已保存到: {output_path}")
    
    # 异步清理临时文件（如果不是verbose模式）
    if not state["verbose"]:
        cleanup_tasks = []
        for page_result in sorted_results:
            page_image_path = page_result["page_image_path"]
            if os.path.exists(page_image_path):
                cleanup_tasks.append(asyncio.create_task(async_remove_file(page_image_path)))
        
        if cleanup_tasks:
            await asyncio.gather(*cleanup_tasks, return_exceptions=True)
    
    # 决定下一步：如果启用PDF翻译，则继续生成翻译PDF；否则结束
    if state.get("enable_pdf_translation", False):
        next_node = "generate_translated_pdf"
    else:
        next_node = "__end__"
    
    return Command(
        goto=next_node,
        update={
            "final_content": html_document,
            "all_rect_images": all_rect_images
        }
    )

async def generate_translated_pdf(state) -> Command[Literal["__end__"]]:
    """生成翻译后的PDF文件，直接在原PDF上替换文本"""
    logging.info("开始生成翻译后的PDF文件")
    
    try:
        # 打开原始PDF文件
        pdf_document = fitz.open(state["pdf_path"])
        
        # 按页面索引排序结果
        sorted_results = sorted(state["page_analysis_results_translated"], key=lambda x: x["page_index"])
        
        for page_result in sorted_results:
            page_index = page_result["page_index"]
            page = pdf_document[page_index]
            
            logging.info(f"处理页面 {page_index + 1}")
            
            # 获取该页面的区域信息、边界框和翻译内容
            region_info = page_result["region_info"]
            region_bboxes = page_result["region_bboxes"]
            
            # 处理每个区域
            for region_index, region_data in region_info.items():
                region_type = region_data.get('type', 'unknown')
                
                # 跳过图片类型的区域
                if region_type == "image":
                    logging.info(f"页面 {page_index + 1} 区域 {region_index}: 跳过图片区域")
                    continue
                
                # 直接使用存储的边界框信息，避免重复计算
                if region_index < len(region_bboxes):
                    bbox = region_bboxes[region_index]
                    
                    # 在PDF生成时再次验证和裁剪坐标
                    page_bounds = page.bound()
                    is_valid, bbox = _validate_coordinate_transform(bbox, 1.0, page_bounds)
                    if not is_valid:
                        logging.error(f"PDF生成时坐标验证失败！页面 {page_index + 1} 区域 {region_index}: {bbox}")
                        continue
                        
                    logging.info(f"页面 {page_index + 1} 区域 {region_index} 使用坐标: {bbox}")
                else:
                    logging.warning(f"页面 {page_index + 1} 区域 {region_index}: 边界框信息缺失")
                    continue
                
                # 获取该区域的翻译内容
                # 注意：这里使用page_index作为参数，它是从0开始的页面索引
                translated_content = await _extract_region_translated_content(
                    page_result["translated_content"], region_index, region_type, page_index
                )
                
                if not translated_content:
                    logging.warning(f"页面 {page_index + 1} 区域 {region_index}: 没有找到翻译内容")
                    # 添加更详细的调试信息
                    logging.debug(f"页面 {page_index + 1} 区域 {region_index} 翻译内容长度: {len(page_result['translated_content'])}")
                    logging.debug(f"页面 {page_index + 1} 区域 {region_index} 翻译内容前200字符: {page_result['translated_content'][:200]}")
                    continue
                
                # 替换文本
                await _replace_text_in_region(page, bbox, translated_content, region_type)
                logging.info(f"页面 {page_index + 1} 区域 {region_index} ({region_type}): 文本替换完成")
        
        # 保存翻译后的PDF，使用最大压缩设置
        translated_pdf_path = os.path.join(state["output_dir"], f'{state["file_name_without_ext"]}_translated.pdf')
        pdf_document.save(translated_pdf_path, 
                         garbage=4,              # 最高垃圾收集级别
                         deflate=True,           # 启用deflate压缩
                         deflate_images=False,    # 压缩图像流
                         deflate_fonts=False,     # 压缩字体流
                         clean=False,             # 清理内容流
                         ascii=False,            # 二进制编码
                         linear=False,           # 不线性化
                         pretty=False,           # 不美化输出
                         use_objstms=1)          # 使用对象流压缩
        
        # local test
        # pdf_document.save('./test.pdf', 
        #                  garbage=4,              # 最高垃圾收集级别
        #                  deflate=True,           # 启用deflate压缩
        #                  deflate_images=False,    # 压缩图像流
        #                  deflate_fonts=False,     # 压缩字体流
        #                  clean=False,             # 清理内容流
        #                  ascii=False,            # 二进制编码
        #                  linear=False,           # 不线性化
        #                  pretty=False,           # 不美化输出
        #                  use_objstms=1)          # 使用对象流压缩

        pdf_document.close()
        
        logging.info(f"翻译后的PDF已保存到: {translated_pdf_path}")
        
        return Command(
            goto="__end__",
            update={
                "translated_pdf_path": translated_pdf_path
            }
        )
        
    except Exception as e:
        logging.error(f"生成翻译PDF时发生错误: {e}")
        return Command(
            goto="__end__",
            update={
                "translated_pdf_path": ""
            }
        )

def save_graph_image(app, filename):
    """Save graph visualization as an image file."""
    try:
        mermaid_code = app.get_graph(xray=1).draw_mermaid()
        with open(filename, "w", encoding="utf-8") as f:
            f.write(mermaid_code)
        print(f"工作流Mermaid代码已保存到 {filename}")
        print("请使用以下方式查看图形:")
        print("1. 在线: 访问 https://mermaid.live/ 并粘贴文件内容")
        print("2. VS Code: 安装Mermaid扩展并打开.mmd文件")
        print("3. 其他Mermaid查看器或编辑器")
    except Exception as e:
        print(f"无法保存工作流图: {e}")

def create_pdf_translation_workflow():
    """创建PDF翻译工作流"""

    from langgraph.types import RetryPolicy
    
    # 创建状态图
    workflow = StateGraph(PDFWorkflowState)
    
    # 添加节点（流水线架构，限制并发）
    workflow.add_node("initialize_workflow", initialize_workflow)
    workflow.add_node("extract_pdf_pages", extract_pdf_pages)
    workflow.add_node("dispatch_page_processing", dispatch_page_processing)
    workflow.add_node("process_batch_pages", process_batch_pages)
    workflow.add_node("process_single_page_complete", process_single_page_complete, retry_policy=RetryPolicy(max_attempts=5))
    workflow.add_node("collect_and_continue", collect_and_continue, defer=True)
    workflow.add_node("finalize_results", finalize_results, defer=True)
    workflow.add_node("generate_translated_pdf", generate_translated_pdf)

    # 添加入口节点
    workflow.set_entry_point("initialize_workflow")
    
    # 编译工作流
    checkpointer = MemorySaver()
    graph = workflow.compile(checkpointer=checkpointer)
    
    save_graph_image(graph, "workflow_pipeline.mmd")
    
    return graph

if __name__ == "__main__":
    pass