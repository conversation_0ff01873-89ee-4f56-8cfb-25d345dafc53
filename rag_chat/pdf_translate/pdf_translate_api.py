import os
from uuid import uuid4
import json
import time
import filetype
import asyncio
import shutil
import tempfile
import zipfile
from pathlib import Path
from fastapi import File, UploadFile, Query, Request, APIRouter, Form
from typing import Optional
from fastapi.responses import JSONResponse

import sys
from pathlib import Path

from datetime import datetime

# 获取项目根目录的绝对路径
project_root = Path(__file__).parent.parent.parent
rag_chat_path = project_root / "rag_chat"
sys.path.append(str(rag_chat_path))

# 导入项目内部模块
from file_util import md5, upload_file
from logger import logger
from models import R
from constant import AI_ASSISTANT_HEADER, AI_ASSISTANT_WARN_URLENCODER

# 导入PDF翻译相关模块
from .main import parse_pdf_with_langgraph

# 导入RabbitMQ客户端
from rabbitmq_simple import RabbitMQClient
from simplified_stats_service import SimplifiedStatsService, save_wps_record_dm_with_stats

router = APIRouter()

# 临时目录配置
TEMP_DIR = tempfile.gettempdir()
PDF_PUT_IN_DIR = os.path.join(TEMP_DIR, "pdf_translate_put_in")
PDF_PUT_OUT_DIR = os.path.join(TEMP_DIR, "pdf_translate_put_out")
os.makedirs(PDF_PUT_IN_DIR, exist_ok=True)
os.makedirs(PDF_PUT_OUT_DIR, exist_ok=True)

# 文件上传配置 - 只支持PDF
ALLOWED_MIME_TYPES = ['application/pdf']

# RabbitMQ客户端
rabbitmq_client = RabbitMQClient()

async def sanitize_filename(filename):
    """清理文件名，移除空格和特殊字符"""
    import re
    
    if not filename or not filename.strip():
        return f"file_{str(int(time.time()))}.pdf"
    
    name, ext = os.path.splitext(filename)
    
    # 清理文件名，只保留字母、数字、下划线、连字符和点
    sanitized_name = re.sub(r'[^\w\-.]', '_', name.replace(' ', '_'))
    
    # 移除开头和结尾的特殊字符，只保留字母数字开头结尾
    sanitized_name = re.sub(r'^[^\w]+|[^\w]+$', '', sanitized_name)
    
    # 如果清理后为空，使用时间戳
    if not sanitized_name:
        sanitized_name = f"file_{str(int(time.time()))}"
    
    # 如果没有扩展名，默认为.pdf
    if not ext:
        ext = '.pdf'
    
    return sanitized_name + ext


def verify_pdf_mime_type(file_path):
    """验证PDF文件MIME类型"""
    kind = filetype.guess(file_path)
    
    if not kind:
        ext = os.path.splitext(file_path)[1].lower()
        if ext == '.pdf':
            return True, 'application/pdf'
        else:
            return False, 'unknown'
    
    if kind.mime == 'application/pdf':
        return True, kind.mime
    else:
        return False, kind.mime


async def estimate_pdf_processing_time(file_path):
    """估计PDF处理时间"""
    try:
        import fitz
        with fitz.open(file_path) as doc:
            total_pages = len(doc)
        
        # 基于页数估算处理时间（每页约15秒，包含VLM分析和翻译）
        base_time_per_page = 15
        estimated_time = total_pages * base_time_per_page + 60  # 额外60秒用于初始化和后处理
        
        return total_pages, int(estimated_time)
    
    except Exception as e:
        logger.error(f"估计PDF处理时间失败: {e}", exc_info=True)
        return 1, 120  # 默认值


async def create_translated_html_file(translated_content, original_filename, output_dir):
    """创建翻译后的HTML文件"""
    try:
        # 生成输出文件名
        name_without_ext = os.path.splitext(original_filename)[0]
        output_filename = f"{name_without_ext}_翻译.html"
        output_path = os.path.join(output_dir, output_filename)
        
        # 直接写入翻译内容（已经是HTML格式）
        with open(output_path, 'w', encoding='utf-8') as f:
            f.write(translated_content)
        
        logger.info(f"翻译后的HTML文件创建成功: {output_path}")
        return output_path, output_filename
        
    except Exception as e:
        logger.error(f"创建翻译后的HTML文件失败: {e}", exc_info=True)
        raise


async def upload_translated_file(file_path, file_name, headers):
    """上传翻译后的文件到文件服务"""
    try:
        with open(file_path, "rb") as f:
            file_bytes = f.read()
        
        file_md5 = md5(file_name + str(time.time()))
        
        upload_headers = {}
        
        # 提取必要的认证头
        for header_key in ['X-API-Key', 'x-api-key', 'X-Username', 'x-username', 
                          'Authorization', 'authorization', 'Token', 'token']:
            if header_key in headers:
                target_key = header_key.replace('x-', 'X-').replace('authorization', 'Authorization').replace('token', 'Token')
                upload_headers[target_key] = headers[header_key]
        
        logger.info(f"上传PDF翻译文件使用的headers: {upload_headers}")
        
        file_id = await upload_file(
            file_bytes, file_md5, file_name, "pdf_translations", upload_headers
        )
        
        logger.info(f"PDF翻译文件上传成功，获取到文件ID: {file_id}")
        return file_id
        
    except Exception as e:
        logger.error(f"上传PDF翻译文件失败: {e}", exc_info=True)
        raise


async def create_translation_zip(html_path, html_name, pdf_path, pdf_name, image_paths, original_pdf_path, temp_dir):
    """创建包含PDF、HTML和图片的压缩包"""
    try:
        # 获取原始文件名（不含扩展名）
        original_name = os.path.splitext(os.path.basename(original_pdf_path))[0]
        zip_filename = f"{original_name}_翻译完整包.zip"
        zip_path = os.path.join(temp_dir, zip_filename)
        
        with zipfile.ZipFile(zip_path, 'w', zipfile.ZIP_DEFLATED) as zipf:
            # 添加原始PDF文件
            if os.path.exists(original_pdf_path):
                zipf.write(original_pdf_path, os.path.basename(original_pdf_path))
                logger.info(f"已添加原始PDF文件到压缩包: {os.path.basename(original_pdf_path)}")
            
            # 添加HTML文件
            if html_path and os.path.exists(html_path):
                zipf.write(html_path, html_name)
                logger.info(f"已添加HTML文件到压缩包: {html_name}")
            
            # 添加翻译后的PDF文件
            if pdf_path and os.path.exists(pdf_path):
                zipf.write(pdf_path, pdf_name)
                logger.info(f"已添加翻译PDF文件到压缩包: {pdf_name}")
            
            # 创建images子文件夹并添加图片
            if image_paths:
                logger.info(f"开始添加 {len(image_paths)} 张图片到压缩包")
                for image_path in image_paths:
                    if os.path.exists(image_path):
                        # 在压缩包中创建images子文件夹
                        image_filename = os.path.basename(image_path)
                        zip_image_path = f"images/{image_filename}"
                        zipf.write(image_path, zip_image_path)
                        logger.info(f"已添加图片到压缩包: {zip_image_path}")
                    else:
                        logger.warning(f"图片文件不存在，跳过: {image_path}")
        
        logger.info(f"压缩包创建成功: {zip_path}")
        return zip_path, zip_filename
        
    except Exception as e:
        logger.error(f"创建压缩包失败: {e}", exc_info=True)
        raise


async def upload_translated_files(html_path, html_name, pdf_path, pdf_name, image_paths, original_pdf_path, temp_dir, headers):
    """上传翻译后的HTML、PDF文件和压缩包到文件服务"""
    upload_results = {}
    
    try:
        # 上传HTML文件
        if html_path and os.path.exists(html_path):
            logger.info(f"开始上传HTML文件: {html_name}")
            html_file_id = await upload_translated_file(html_path, html_name, headers)
            upload_results['html'] = {
                'file_id': html_file_id,
                'file_name': html_name,
                'file_type': 'html'
            }
            logger.info(f"HTML文件上传成功，文件ID: {html_file_id}")
        
        # 上传PDF文件
        if pdf_path and os.path.exists(pdf_path):
            logger.info(f"开始上传PDF文件: {pdf_name}")
            pdf_file_id = await upload_translated_file(pdf_path, pdf_name, headers)
            upload_results['pdf'] = {
                'file_id': pdf_file_id,
                'file_name': pdf_name,
                'file_type': 'pdf'
            }
            logger.info(f"PDF文件上传成功，文件ID: {pdf_file_id}")
        
        # 创建并上传压缩包
        try:
            zip_path, zip_filename = await create_translation_zip(
                html_path, html_name, pdf_path, pdf_name, 
                image_paths, original_pdf_path, temp_dir
            )
            
            logger.info(f"开始上传压缩包: {zip_filename}")
            zip_file_id = await upload_translated_file(zip_path, zip_filename, headers)
            upload_results['zip'] = {
                'file_id': zip_file_id,
                'file_name': zip_filename,
                'file_type': 'zip'
            }
            logger.info(f"压缩包上传成功，文件ID: {zip_file_id}")
            
        except Exception as e:
            logger.error(f"创建或上传压缩包失败: {e}")
            # 压缩包失败不影响其他文件的上传
        
        return upload_results
        
    except Exception as e:
        logger.error(f"上传翻译文件失败: {e}", exc_info=True)
        raise


async def process_pdf_translation_task(task_id, file_info, request_headers, user_id=''):
    """处理PDF翻译任务的主函数 - 使用RabbitMQ"""
    temp_dir = None
    start_time = time.time()
    request_start_time = datetime.now()  # 添加统计开始时间
    
    # 提取统计相关参数
    userid = request_headers.get('x-username', '') or user_id
    apikey = request_headers.get('x-api-key', '')
    uri = request_headers.get('x-uri', '/jiliang/translate/pdf')
    function_type = file_info.get('function_type')
    function_name = file_info.get('function_name')

    # 计算总步骤数：3个基础步骤 + 每页两步（分析+翻译）
    total_pages = file_info.get('total_pages', 1)
    total_step = total_pages * 2 + 3
    
    try:
        temp_dir = tempfile.mkdtemp(prefix="pdf_translate_temp_")
        
        file_path = file_info['file_path']
        target_language = file_info['target_language']
        
        # 发送任务开始通知
        await rabbitmq_client.simple_publish(
            task_id=task_id,
            message={
                "status": "processing",
                "current_step": 1,
                "total_step": total_step,  # 每页2步(分析+翻译) + 3个固定步骤
                "progress_percentage": int((1 / total_step) * 100),
                "message": "开始PDF翻译处理",
                "task_type": "pdf_translation",
                "user_id": user_id
            }
        )
        
        # 调用主翻译函数
        logger.info(f"开始调用parse_pdf_with_langgraph处理PDF: {file_path}")
        
        # 发送翻译开始通知
        await rabbitmq_client.simple_publish(
            task_id=task_id,
            message={
                "status": "processing",
                "current_step": 2,
                "total_step": total_step,
                "progress_percentage": int((2 / total_step) * 100),
                "message": f"开始逐页翻译PDF文档到{target_language}（共{file_info.get('total_pages', 1)}页）",
                "task_type": "pdf_translation",
                "user_id": user_id
            }
        )
        
        # 创建进度回调函数
        async def progress_callback(page_index: int, total_pages: int, stage: str):
            """页面级进度回调函数 - page_index从0开始，确保进度计算一致性"""
            try:
                if stage == "analysis_complete":
                    # VLM分析完成：每页的第一个步骤
                    # 步骤计算：3个基础步骤 + page_index * 2 + 1(分析步骤)
                    current_step = 3 + page_index * 2 + 1
                    # 进度计算：直接使用 current_step / total_step * 100 确保一致性
                    progress_percentage = int((current_step / total_step) * 100)
                    
                    await rabbitmq_client.simple_publish(
                        task_id=task_id,
                        message={
                            "status": "processing",
                            "current_step": current_step, 
                            "total_step": total_step,
                            "progress_percentage": progress_percentage,
                            "message": f"已完成第{page_index + 1}页VLM版式分析",
                            "task_type": "pdf_translation",
                            "user_id": user_id
                        }
                    )
                elif stage == "translation_complete":
                    # 页面翻译完成：每页的第二个步骤
                    # 步骤计算：3个基础步骤 + page_index * 2 + 2(分析+翻译步骤)
                    current_step = 3 + page_index * 2 + 2
                    # 进度计算：直接使用 current_step / total_step * 100 确保一致性
                    progress_percentage = int((current_step / total_step) * 100)
                    
                    await rabbitmq_client.simple_publish(
                        task_id=task_id,
                        message={
                            "status": "processing", 
                            "current_step": current_step,
                            "total_step": total_step,
                            "progress_percentage": progress_percentage,
                            "message": f"已完成第{page_index + 1}页翻译到{target_language}",
                            "task_type": "pdf_translation",
                            "user_id": user_id
                        }
                    )
            except Exception as e:
                logger.error(f"进度回调发送失败: {e}", exc_info=True)
                # 进度回调失败不影响主流程，只记录日志
        
        translated_content, translated_pdf_path, image_paths = await parse_pdf_with_langgraph(
            pdf_path=file_path,
            output_dir=temp_dir,
            target_language=target_language,
            progress_callback=progress_callback,
            total_pages=total_pages
        )
        
        print(translated_content)
        
        # 检查翻译内容是否为空（去除HTML标签后）
        def is_html_content_empty(html_content):
            """检查HTML内容是否为空"""
            if not html_content or not html_content.strip():
                return True
            
            import re
            
            # 尝试提取content区域的内容
            content_match = re.search(r'<div class="content">(.*?)</div>', html_content, re.DOTALL)
            if content_match:
                content_area = content_match.group(1)
            else:
                # 如果没有找到content区域，使用整个内容
                content_area = html_content
            
            # 移除HTML标签，只保留文本内容
            text_content = re.sub(r'<[^>]+>', '', content_area)
            text_content = text_content.strip()
            
            # 检查是否只剩下空白字符、换行符等
            return len(text_content) == 0 or text_content.isspace()
        
        if translated_content and not is_html_content_empty(translated_content):
            logger.info(f"PDF翻译完成，内容长度: {len(translated_content)}")
        else:
            logger.error("PDF翻译失败: 翻译内容为空")
            
            # 计算失败时的处理时间
            response_end_time = datetime.now()
            duration_ms = int((time.time() - start_time) * 1000)
            
            # 保存失败统计记录
            if function_type:
                save_wps_record_dm_with_stats(
                    f"PDF翻译: {file_info.get('file_name', '')}", '', "翻译内容为空",
                    userid, apikey, task_id, function_type, function_name,
                    request_start_time, response_end_time, duration_ms, 'ERROR',
                    'pdf_translate_model', 0.3
                )
            
            # 发送翻译失败通知
            await rabbitmq_client.simple_publish(
                task_id=task_id,
                message={
                    "status": "failed",
                    "current_step": 2,
                    "total_step": total_step,
                    "progress_percentage": int((2 / total_step) * 100),
                    "message": "PDF翻译失败: 翻译内容为空",
                    "task_type": "pdf_translation",
                    "user_id": user_id
                }
            )
            return None
        
        if translated_pdf_path:
            logger.info(f"翻译后的PDF文件路径: {translated_pdf_path}")
        
        # 发送生成文件阶段通知
        await rabbitmq_client.simple_publish(
            task_id=task_id,
            message={
                "status": "processing",
                "current_step": total_step - 1,
                "total_step": total_step,
                "progress_percentage": int(((total_step - 1) / total_step) * 100),
                "message": "PDF翻译完成，正在生成文件",
                "task_type": "pdf_translation",
                "user_id": user_id
            }
        )
        
        # 创建翻译后的HTML文件
        html_output_path, html_output_filename = await create_translated_html_file(
            translated_content, file_info['file_name'], temp_dir
        )
        
        # 准备PDF文件信息
        pdf_output_path = translated_pdf_path if translated_pdf_path and os.path.exists(translated_pdf_path) else None
        pdf_output_filename = None
        if pdf_output_path:
            # 生成PDF文件名
            name_without_ext = os.path.splitext(file_info['file_name'])[0]
            pdf_output_filename = f"{name_without_ext}_翻译.pdf"
        
        # 上传文件
        try:
            upload_results = await upload_translated_files(
                html_path=html_output_path,
                html_name=html_output_filename,
                pdf_path=pdf_output_path,
                pdf_name=pdf_output_filename,
                image_paths=image_paths,
                original_pdf_path=file_path,
                temp_dir=temp_dir,
                headers=request_headers
            )
            
            logger.info(f"PDF翻译任务完成，上传结果: {upload_results}")
            
            # 计算总处理时间
            total_time = time.time() - start_time
            response_end_time = datetime.now()
            duration_ms = int(total_time * 1000)
            
            # 保存成功统计记录
            if function_type:
                success_message = f"PDF翻译完成，目标语言: {target_language}，生成文件数: {len(upload_results)}"
                save_success = save_wps_record_dm_with_stats(
                    f"PDF翻译: {file_info.get('file_name', '')}", success_message, '',
                    userid, apikey, task_id, function_type, function_name,
                    request_start_time, response_end_time, duration_ms, 'SUCCESS',
                    'pdf_translate_model', 0.3
                )
                
                if save_success:
                    logger.info(f"PDF翻译统计记录保存成功 - Function: {function_type}, Duration: {duration_ms}ms")
                else:
                    logger.warning(f"PDF翻译统计记录保存失败 - Function: {function_type}")
            
            # 构建文件结果信息
            file_results = []
            if 'html' in upload_results:
                file_results.append(upload_results['html'])
            if 'pdf' in upload_results:
                file_results.append(upload_results['pdf'])
            if 'zip' in upload_results:
                file_results.append(upload_results['zip'])
            
            # 将文件结果转换为JSON字符串
            file_result_str = json.dumps(file_results, ensure_ascii=False)
            
            # 发送任务完成通知 - 包含所有文件信息
            await rabbitmq_client.simple_publish(
                task_id=task_id,
                message={
                    "status": "completed",
                    "current_step": total_step,
                    "total_step": total_step,
                    "progress_percentage": 100,
                    "message": f"PDF翻译完成，已生成{target_language}版本，包含{len(file_results)}个文件（含完整压缩包）",
                    "task_type": "pdf_translation",
                    "file_result": file_result_str,
                    "user_id": user_id
                }
            )
            
            return {
                'task_id': task_id,
                'files': upload_results,
                'target_language': target_language,
                'process_time': f"{total_time:.2f}秒"
            }
        
        except Exception as e:
            logger.error(f"上传PDF翻译文件失败: {str(e)}")
            
            # 计算处理时间
            total_time = time.time() - start_time
            response_end_time = datetime.now()
            duration_ms = int(total_time * 1000)
            
            # 保存失败统计记录（文件上传失败）
            if function_type:
                save_wps_record_dm_with_stats(
                    f"PDF翻译: {file_info.get('file_name', '')}", '', f"文件上传失败: {str(e)}",
                    userid, apikey, task_id, function_type, function_name,
                    request_start_time, response_end_time, duration_ms, 'ERROR',
                    'pdf_translate_model', 0.3
                )
            
            # 发送任务失败通知
            await rabbitmq_client.simple_publish(
                task_id=task_id,
                message={
                    "status": "failed",
                    "current_step": total_step,
                    "total_step": total_step,
                    "progress_percentage": int((total_step / total_step) * 100),
                    "message": f"翻译已完成，但文件上传失败: {str(e)}",
                    "task_type": "pdf_translation",
                    "user_id": user_id
                }
            )
            
            return None  # 添加return语句，避免继续执行到外层异常处理
    
    except Exception as e:
        total_time = time.time() - start_time
        response_end_time = datetime.now()
        duration_ms = int(total_time * 1000)
        
        logger.error(f"PDF翻译失败(耗时{total_time:.2f}秒): {e}", exc_info=True)
        
        # 保存失败统计记录
        if function_type:
            save_wps_record_dm_with_stats(
                f"PDF翻译: {file_info.get('file_name', '')}", '', str(e),
                userid, apikey, task_id, function_type, function_name,
                request_start_time, response_end_time, duration_ms, 'ERROR',
                'pdf_translate_model', 0.3
            )
        
        # 发送任务失败通知
        await rabbitmq_client.simple_publish(
            task_id=task_id,
            message={
                "status": "failed",
                "current_step": 1,
                "total_step": total_step if 'total_step' in locals() else 5,
                "progress_percentage": int((1 / (total_step if 'total_step' in locals() else 5)) * 100),
                "message": f"PDF翻译失败: {str(e)}",
                "task_type": "pdf_translation",
                "user_id": user_id
            }
        )
        
        return None
    
    finally:
        # 清理临时目录
        if temp_dir and os.path.exists(temp_dir):
            try:
                shutil.rmtree(temp_dir)
                logger.info(f"PDF翻译临时目录清理完成: {temp_dir}")
            except Exception as e:
                logger.error(f"清理PDF翻译临时目录失败: {e}")
        
        # 清理原始文件
        try:
            if 'file_path' in file_info and os.path.exists(file_info['file_path']):
                os.remove(file_info['file_path'])
                logger.info(f"清理原始PDF文件: {file_info['file_path']}")
        except Exception as e:
            logger.error(f"清理原始PDF文件失败: {e}")


@router.post("/jiliang/translate/pdf")
async def translate_pdf(
    request: Request,
    file: UploadFile = File(...),
    target_language: str = Form("中文", description="目标语言，支持任意语言（如：中文、英文、日文、韩文、法文、德文等）"),
    user_id: str = Form("", description="用户ID"),
    function_type: str = Form(None, description="功能类型英文代码"),
    function_name: str = Form(None, description="功能名称中文")
):
    """
    PDF翻译接口
    
    Args:
        request: FastAPI请求对象
        file: 上传的PDF文件
        target_language: 目标语言
        user_id: 用户ID
        function_type: 功能类型英文代码
        function_name: 功能名称中文
        
    Returns:
        任务ID及状态信息
    """
    
    try:
        headers = dict(request.headers)
        
        task_id = rabbitmq_client.getTaskId()
        
        # 如果提供了功能类型但没有功能名称，自动查询
        if function_type and not function_name:
            func_info = SimplifiedStatsService._get_function_info(function_type)
            if func_info:
                function_name = func_info['function_name']
                logger.info(f"自动查询功能名称: {function_type} -> {function_name}")
        
        logger.info(f"PDF翻译请求 - Function: {function_type}, FunctionName: {function_name}")
        
        response_headers = {
            "Task-Id": task_id,
            AI_ASSISTANT_HEADER: AI_ASSISTANT_WARN_URLENCODER
        }
        
        logger.info(f"PDF翻译请求: 文件名={file.filename}, 目标语言={target_language}, 任务ID={task_id}")
        
        # 清理文件名
        original_filename = file.filename
        sanitized_filename = await sanitize_filename(original_filename)
        
        if sanitized_filename != original_filename:
            logger.info(f"文件名已清理: {original_filename} -> {sanitized_filename}")
            file.filename = sanitized_filename
        
        # 保存上传文件
        file_location = os.path.join(PDF_PUT_IN_DIR, f"{task_id}_{file.filename}")
        with open(file_location, "wb") as buffer:
            shutil.copyfileobj(file.file, buffer)
        
        # 验证文件类型
        is_allowed, mime_type = verify_pdf_mime_type(file_location)
        
        if not is_allowed:
            logger.error(f"不支持的文件类型: {mime_type}, 文件: {file.filename}")
            
            os.remove(file_location)
            
            # 发送任务失败通知
            await rabbitmq_client.simple_publish(
                task_id=task_id,
                message={
                    "status": "failed",
                    "current_step": 1,
                    "total_step": 5,  # 文件验证失败，默认按1页计算: 1*2+3=5
                    "progress_percentage": int((1 / 5) * 100),
                    "message": f"不支持的文件类型: {mime_type}，仅支持PDF文件",
                    "task_type": "pdf_translation",
                    "user_id": user_id
                }
            )
            
            return JSONResponse(
                content=R.error('400', f'不支持的文件类型: {mime_type}，仅支持PDF文件').model_dump(),
                headers={AI_ASSISTANT_HEADER: AI_ASSISTANT_WARN_URLENCODER},
                status_code=400
            )
        
        # 估计处理时间
        total_pages, estimated_time = await estimate_pdf_processing_time(file_location)
        
        file_info = {
            'file_path': file_location,
            'file_name': file.filename,
            'mime_type': mime_type,
            'file_size': os.path.getsize(file_location),
            'total_pages': total_pages,
            'target_language': target_language,
            'function_type': function_type,
            'function_name': function_name
        }
        
        # 发送任务准备通知
        await rabbitmq_client.simple_publish(
            task_id=task_id,
            message={
                "status": "pending",
                "current_step": 0,
                "total_step": file_info.get('total_pages', 1),
                "message": "文件验证成功，准备开始PDF翻译",
                "task_type": "pdf_translation",
                "user_id": user_id
            }
        )
        
        # 启动异步PDF翻译任务
        asyncio.create_task(
            process_pdf_translation_task(
                task_id,
                file_info,
                headers,
                user_id
            )
        )
        
        response_data = {
            'task_id': task_id,
            'status': 'pending',
            'message': '文件验证成功，PDF翻译任务已提交',
            'target_language': target_language,
            'estimated_time': estimated_time,
            'file_name': file.filename,
            'total_pages': total_pages
        }
        
        return JSONResponse(
            content=R.ok(response_data).model_dump(),
            headers=response_headers
        )
    
    except Exception as e:
        logger.error(f"PDF翻译文件上传处理失败: {e}", exc_info=True)
        return JSONResponse(
            content=R.error('500', f'PDF翻译文件处理失败: {str(e)}').model_dump(),
            headers={AI_ASSISTANT_HEADER: AI_ASSISTANT_WARN_URLENCODER},
            status_code=500
        )


@router.get("/jiliang/translate/pdf/stats/functions")
async def get_pdf_translate_function_stats(request: Request,
                                           function_type: str = None,
                                           start_date: str = None,
                                           end_date: str = None):
    """获取PDF翻译功能调用统计"""
    try:
        from rag_chat.simplified_stats_service import StatsQueryService
        stats = StatsQueryService.get_function_stats_from_existing_tables(
            'SX_WPS_RECORDS', start_date, end_date, function_type
        )
        return R.ok({"functions": stats})
    except Exception as e:
        logger.error(f"查询PDF翻译功能统计失败: {e}")
        return R.error('500', f"查询失败: {str(e)}")


@router.get("/jiliang/translate/pdf/stats/users/{user_id}")
async def get_pdf_translate_user_stats(request: Request,
                                       user_id: str,
                                       start_date: str = None,
                                       end_date: str = None):
    """获取PDF翻译用户功能使用统计"""
    header = dict(request.headers)
    request_user = header.get('x-username', '')
    
    # 权限验证：只能查询自己的统计
    if request_user != user_id:
        return R.error('403', "权限验证失败")
    
    try:
        from rag_chat.simplified_stats_service import StatsQueryService
        stats = StatsQueryService.get_user_function_stats(
            user_id, 'SX_WPS_RECORDS', start_date, end_date
        )
        return R.ok({"user_stats": stats})
    except Exception as e:
        logger.error(f"查询PDF翻译用户统计失败: {e}")
        return R.error('500', f"查询失败: {str(e)}")