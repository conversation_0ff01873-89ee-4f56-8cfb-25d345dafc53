from typing import Tuple, List, Callable, Optional
import asyncio
import os
import time
import logging
from pathlib import Path
from .config import DEFAULT_CONFIG
from .vllm_llm_translate_graph_ori_html import create_pdf_translation_workflow
from .state import PDFWorkflowState

# 全局回调存储（避免在状态中存储函数对象）
_global_progress_callback: Optional[Callable] = None

def get_global_progress_callback() -> Optional[Callable]:
    """获取全局进度回调函数"""
    global _global_progress_callback
    return _global_progress_callback

use_langfuse = False
if use_langfuse:
    try:
        from langfuse.langchain import CallbackHandler
        from langfuse import Langfuse
        langfuse = Langfuse(
            public_key=os.getenv("LANGFUSE_PUBLIC_KEY"),
            secret_key=os.getenv("LANGFUSE_SECRET_KEY"),
            host=os.getenv("LANGFUSE_HOST")
        )
        langfuse_handler = CallbackHandler()
    except:
        langfuse_handler = None
else:
    langfuse_handler = None

async def parse_pdf_with_langgraph(
    pdf_path: str,
    output_dir: str = './',
    target_language: str = "中文",
    progress_callback = None,
    total_pages: int = 1
) -> Tuple[str, str, List[str]]:
    """
    使用LangGraph解析PDF文件为Markdown文件
    
    Args:
        pdf_path: PDF文件路径
        output_dir: 输出目录
        verbose: 是否输出详细日志
        use_vlm_layout: 是否使用VLM智能布局分析
        enable_pdf_translation: 是否启用PDF直接翻译功能
        
    Returns:
        Tuple[str, List[str]]: 包含Markdown内容和所有矩形图像路径的元组
    """
    
    # 获取文件名（不含扩展名）
    path = Path(pdf_path)
    file_name_without_ext = path.stem
    
    # 创建工作流
    workflow = create_pdf_translation_workflow()

    config = DEFAULT_CONFIG

    env_status = config.check_environment()
    vlm_available = config.is_vlm_available()
    translation_available = config.is_translation_available()

    logging.info(f"env_status: {env_status}")
    logging.info(f"vlm_available: {vlm_available}")
    logging.info(f"translation_available: {translation_available}")

    workflow_state_dict = config.to_workflow_state_dict(
                pdf_path=pdf_path,
                output_dir=output_dir,
                file_name_without_ext=file_name_without_ext,
                language=target_language
            )
    
    # 不在状态中存储函数对象以避免序列化问题
    # workflow_state_dict['progress_callback'] = progress_callback  # 已移除避免序列化问题
    workflow_state_dict['callback_total_pages'] = total_pages
    
    # 设置全局回调（避免在状态中存储函数对象）
    global _global_progress_callback
    _global_progress_callback = progress_callback
    
    initial_state = PDFWorkflowState(**workflow_state_dict)
    
    # 配置
    run_config = {"configurable": {"thread_id": f"pdf_translation_{int(time.time())}"},
                  "recursion_limit": 999999999999999999999999999999999}
    if langfuse_handler and use_langfuse:
        run_config["callbacks"] = [langfuse_handler]
    
    # 执行工作流
    try:
        logging.info("开始执行PDF翻译工作流")
        final_state = await workflow.ainvoke(initial_state, config=run_config)
        
        return final_state["final_content"], final_state.get("translated_pdf_path", ""), final_state["all_rect_images"]
        
    except Exception as e:
        logging.error(f"工作流执行失败: {e}")
        raise
    finally:
        # 清理全局回调避免内存泄漏
        _global_progress_callback = None

async def parse_with_vllm_langgraph(use_vlm_layout: bool = True, 
                                    pdf_path: str = "temp/table_test.pdf", 
                                    enable_pdf_translation: bool = False):
    """
    使用LangGraph和VLLM解析PDF文件的主函数
    
    Args:
        use_vlm_layout: 是否使用VLM智能布局分析，默认为True
        enable_pdf_translation: 是否启用PDF直接翻译功能，默认为False
    """
    
    # 设置文件路径
    path = Path(pdf_path)
    file_name_without_ext = path.stem
    directory = path.parent
    folder_path = directory / f"{file_name_without_ext}_{time.strftime('%Y%m%d_%H%M%S')}"
    
    # 创建输出目录
    try:
        folder_path.mkdir(parents=True, exist_ok=True)
        print(f"文件夹 '{folder_path}' 已成功创建。")
    except OSError as e:
        print(f"创建文件夹 '{folder_path}' 时出错: {e}")
    
    output_dir = str(folder_path)
    
    logging.info(f"VLM布局分析模式: {'启用' if use_vlm_layout else '禁用'}")
    logging.info(f"PDF直接翻译模式: {'启用' if enable_pdf_translation else '禁用'}")
    
    # 使用LangGraph异步解析PDF
    content, translated_pdf_path, image_paths = await parse_pdf_with_langgraph(
        pdf_path=pdf_path,
        output_dir=output_dir
    )
    
    print(f"✅ PDF翻译完成！")
    print(f"📄 生成内容长度: {len(content)} 字符")
    print(f"🖼️ 生成图片数量: {len(image_paths)} 张")
    if enable_pdf_translation:
        print(f"📝 PDF直接翻译功能已启用，翻译后的PDF文件将保存在输出目录中")

async def main(pdf_path: str = "temp/table_test.pdf", 
                enable_pdf_translation: bool = False):
    """主程序异步函数"""
    start_time = time.time()
    print("🚀 开始使用LangGraph执行PDF翻译任务（异步版本）")
    print("开始时间: " + str(start_time))
    
    # 检查PDF文件是否存在
    if not os.path.exists(pdf_path):
        print(f"❌ 错误：PDF文件不存在: {pdf_path}")
        print("📁 可用的PDF文件:")
        for file in os.listdir("temp/"):
            if file.endswith('.pdf'):
                print(f"   - temp/{file}")
        return
    
    print(f"📄 使用PDF文件: {pdf_path}")
    print(f"📝 PDF直接翻译: {'✅ 启用' if enable_pdf_translation else '❌ 禁用 (仅生成Markdown)'}")
    
    # 检查必要的环境变量
    vllm_model = os.getenv("VLLM_MODEL")
    vllm_api_key = os.getenv("VLLM_API_KEY") 
    vllm_base_url = os.getenv("VLLM_BASE_URL")
    openai_model = os.getenv("OPENAI_MODEL")
    openai_base_url = os.getenv("OPENAI_BASE_URL")
    openai_api_key = os.getenv("OPENAI_API_KEY")
    
    print("\n🔧 环境变量检查:")
    print(f"   VLLM_MODEL: {'✅' if vllm_model else '❌'} {vllm_model}")
    print(f"   VLLM_API_KEY: {'✅' if vllm_api_key else '❌'} {'***' if vllm_api_key else 'None'}")
    print(f"   VLLM_BASE_URL: {'✅' if vllm_base_url else '❌'} {vllm_base_url}")
    print(f"   OPENAI_MODEL: {'✅' if openai_model else '❌'} {openai_model}")
    print(f"   OPENAI_BASE_URL: {'✅' if openai_base_url else '❌'} {openai_base_url}")
    print(f"   OPENAI_API_KEY: {'✅' if openai_api_key else '❌'} {'***' if openai_api_key else 'None'}")
    
    # 如果缺少关键环境变量，使用传统模式
    if not all([vllm_model, vllm_api_key, vllm_base_url]):
        print("\n⚠️ VLM环境变量缺失，使用传统几何分析模式")
        use_intelligent_layout = False
    else:
        print("\n🎯 环境变量完整，使用VLM智能布局分析模式")
        use_intelligent_layout = True
    
    if use_intelligent_layout:
        print("🎯 启用VLM智能布局分析模式 - 使用LangGraph异步并行处理")
        await parse_with_vllm_langgraph(use_vlm_layout=True, 
                                        pdf_path=pdf_path, 
                                        enable_pdf_translation=enable_pdf_translation)
    else:
        print("📐 使用传统几何分析模式 - 使用LangGraph异步并行处理")
        await parse_with_vllm_langgraph(use_vlm_layout=False, 
                                        pdf_path=pdf_path, 
                                        enable_pdf_translation=enable_pdf_translation)

    print("✅ 任务完成！")
    end_time = time.time()
    print("结束时间: " + str(end_time))
    print("总用时: " + str(end_time - start_time) + " 秒")

if __name__ == '__main__':
    from dotenv import load_dotenv
    load_dotenv(override=True)
    pdf_path = "/Volumes/Extreme SSD/sx_ai/sx-ai-rag-chat/rag_chat/pdf_translate/Building effective agents _ Anthropic.pdf"
    enable_pdf_translation = True
    asyncio.run(main(pdf_path=pdf_path, 
                    enable_pdf_translation=enable_pdf_translation)) 