---
config:
  flowchart:
    curve: linear
---
graph TD;
	__start__([<p>__start__</p>]):::first
	initialize_workflow(initialize_workflow)
	extract_pdf_pages(extract_pdf_pages)
	dispatch_page_processing(dispatch_page_processing)
	process_batch_pages(process_batch_pages)
	process_single_page_complete(process_single_page_complete)
	collect_and_continue(collect_and_continue)
	finalize_results(finalize_results)
	generate_translated_pdf(generate_translated_pdf)
	__end__([<p>__end__</p>]):::last
	__start__ --> initialize_workflow;
	collect_and_continue -.-> finalize_results;
	collect_and_continue -.-> process_batch_pages;
	dispatch_page_processing -.-> process_batch_pages;
	extract_pdf_pages -.-> dispatch_page_processing;
	finalize_results -.-> __end__;
	finalize_results -.-> generate_translated_pdf;
	generate_translated_pdf -.-> __end__;
	generate_translated_pdf -.-> finalize_results;
	initialize_workflow -.-> extract_pdf_pages;
	process_batch_pages -.-> finalize_results;
	process_batch_pages -.-> process_single_page_complete;
	process_single_page_complete -.-> collect_and_continue;
	process_single_page_complete -.-> finalize_results;
	classDef default fill:#f2f0ff,line-height:1.2
	classDef first fill-opacity:0
	classDef last fill:#bfb6fc
