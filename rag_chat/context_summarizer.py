"""
LangMem Context Summarization Module
基于langmem的上下文总结功能，用于LangGraph ReAct Agent的长上下文管理
"""

import os
import logging
import uuid
import sys
from typing import List, Dict, Any, Optional, Tuple
from dataclasses import dataclass, asdict

from langchain_core.messages import BaseMessage, HumanMessage, AIMessage, SystemMessage, ToolMessage, RemoveMessage
from langchain_openai import ChatOpenAI
from langchain_deepseek import ChatDeepSeek
from pydantic import SecretStr

try:
    from langgraph.graph.message import REMOVE_ALL_MESSAGES
except ImportError:
    REMOVE_ALL_MESSAGES = "REMOVE_ALL_MESSAGES"

# Import typing compatibility before langmem
import typing_compat

logger = logging.getLogger(__name__)

# 尝试导入langmem（现在支持Python 3.10）
try:
    from langmem.short_term import summarize_messages, RunningSummary
    LANGMEM_AVAILABLE = True
    logger.info("langmem导入成功，上下文总结功能已启用")
except ImportError as e:
    logger.warning(f"langmem导入失败: {e}，上下文总结功能将被禁用")
    LANGMEM_AVAILABLE = False
    RunningSummary = None
except Exception as e:
    logger.warning(f"langmem初始化失败: {e}，上下文总结功能将被禁用")
    LANGMEM_AVAILABLE = False
    RunningSummary = None

@dataclass
class SummarizationConfig:
    """上下文总结配置"""
    enabled: bool = True
    max_tokens: int = 10000  # 消息列表的最大token数
    max_tokens_before_summary: int = 8000  # 触发总结的token阈值
    max_summary_tokens: int = 1000  # 总结内容的最大token数
    model: str = ""  # 用于总结的模型
    api_key: str = ""
    base_url: str = ""
    temperature: float = 0.3  # 总结时使用较低的temperature确保一致性
    model_backend: str = "deepseek"

    @classmethod
    def from_env(cls) -> 'SummarizationConfig':
        """从环境变量加载配置"""
        model_backend=os.getenv('LANGMEM_MODEL_BACKEND', 'deepseek')
        max_summary_tokens=int('5000' if model_backend == 'deepseek' else '2500')
        return cls(
            enabled=os.getenv('LANGMEM_ENABLED', 'true').lower() == 'true',
            max_tokens=int(os.getenv('LANGMEM_MAX_TOKENS', '10000')),
            max_tokens_before_summary=int(os.getenv('LANGMEM_MAX_TOKENS_BEFORE_SUMMARY', '8192')),
            max_summary_tokens=max_summary_tokens,
            model=os.getenv('LANGMEM_MODEL', ''),
            api_key=os.getenv('OPENAI_API_KEY', ''),
            base_url=os.getenv('OPENAI_BASE_URL', ''),
            temperature=float(os.getenv('LANGMEM_TEMPERATURE', '0.3')),
            model_backend=model_backend
        )

class ContextSummarizer:
    """上下文总结器"""
    
    def __init__(self, config: Optional[SummarizationConfig] = None):
        self.config = config or SummarizationConfig.from_env()
        self._summarization_model = None
        
        if not LANGMEM_AVAILABLE:
            logger.warning("langmem未安装，上下文总结功能将被禁用")
            self.config.enabled = False
        
        if self.config.enabled:
            self._initialize_model()
    
    def _initialize_model(self):
        """初始化总结模型"""
        try:
            if self.config.model_backend == "deepseek":
                self._summarization_model = ChatDeepSeek(
                    model=self.config.model,
                    api_key=SecretStr(self.config.api_key),
                    api_base=self.config.base_url,
                    temperature=self.config.temperature,
                    max_tokens=self.config.max_summary_tokens
                )
            elif self.config.model_backend == "openai":
                self._summarization_model = ChatOpenAI(
                    model=self.config.model,
                    api_key=SecretStr(self.config.api_key),
                    base_url=self.config.base_url,
                    temperature=self.config.temperature,
                    max_completion_tokens=self.config.max_summary_tokens
                )
            else:
                raise ValueError(f"不支持的模型后端: {self.config.model_backend}")
            logger.info(f"上下文总结模型初始化成功: {self.config.model}")
        except Exception as e:
            logger.error(f"初始化总结模型失败: {e}")
            self.config.enabled = False
    
    def _count_tokens(self, messages: List[BaseMessage]) -> int:
        """估算消息的token数量"""
        if not self._summarization_model:
            # 1个token约等于4个字符（英文）或1个字符（中文）
            total_chars = sum(len(msg.content) for msg in messages if hasattr(msg, 'content') and msg.content)
            return max(total_chars // 3, total_chars)  # 保守估计
        
        try:
            return self._summarization_model.get_num_tokens_from_messages(messages)
        except Exception as e:
            logger.debug(f"获取token数量失败，使用估算方法: {e}")
            # 考虑中英文混合
            total_chars = sum(len(msg.content) for msg in messages if hasattr(msg, 'content') and msg.content)
            # 中文约1字符=1token，英文约4字符=1token，取平均值
            return max(total_chars // 2, total_chars // 4)
    
    def _ensure_message_ids(self, messages: List[BaseMessage]) -> List[BaseMessage]:
        """确保消息有ID字段，如果没有则添加"""
        result = []
        for msg in messages:
            # 创建消息副本并确保有ID
            if hasattr(msg, 'id') and msg.id:
                result.append(msg)
            else:
                # 为消息添加ID
                msg_dict = msg.model_dump()
                msg_dict['id'] = str(uuid.uuid4())
                
                # 根据消息类型重新创建
                if isinstance(msg, HumanMessage):
                    new_msg = HumanMessage(**msg_dict)
                elif isinstance(msg, AIMessage):
                    new_msg = AIMessage(**msg_dict)
                elif isinstance(msg, SystemMessage):
                    new_msg = SystemMessage(**msg_dict)
                elif isinstance(msg, ToolMessage):
                    new_msg = ToolMessage(**msg_dict)
                else:
                    # 通用处理
                    new_msg = msg.__class__(**msg_dict)
                
                result.append(new_msg)
        return result
    
    def should_summarize(self, messages: List[BaseMessage]) -> bool:
        """判断是否需要进行总结"""
        if not self.config.enabled or not messages:
            return False
        
        token_count = self._count_tokens(messages)
        return token_count > self.config.max_tokens_before_summary
    
    def summarize_messages_with_state(self, 
                                    messages: List[BaseMessage], 
                                    running_summary: Optional[RunningSummary] = None) -> Tuple[List[BaseMessage], Optional[RunningSummary]]:
        """
        对消息进行总结，返回处理后的消息列表和更新的运行总结
        
        Args:
            messages: 要处理的消息列表
            running_summary: 现有的运行总结状态
            
        Returns:
            处理后的消息列表和更新的运行总结状态
        """
        if not self.config.enabled or not LANGMEM_AVAILABLE:
            return messages, running_summary
        
        if not self.should_summarize(messages):
            return messages, running_summary
        
        if not self._summarization_model:
            logger.warning("总结模型未初始化，跳过总结")
            return messages, running_summary
        
        try:
            logger.info(f"开始进行上下文总结，消息数: {len(messages)}")
            
            # 确保消息有ID字段
            messages_with_ids = self._ensure_message_ids(messages)
            
            # 使用langmem的summarize_messages函数
            summarization_result = summarize_messages(
                messages_with_ids,
                running_summary=running_summary,
                token_counter=self._count_tokens,
                model=self._summarization_model,
                max_tokens=self.config.max_tokens,
                max_tokens_before_summary=self.config.max_tokens_before_summary,
                max_summary_tokens=self.config.max_summary_tokens
            )
            
            logger.info(f"上下文总结完成，处理后消息数: {len(summarization_result.messages)}")
            
            return summarization_result.messages, summarization_result.running_summary
            
        except Exception as e:
            logger.error(f"上下文总结失败: {e}")
            return messages, running_summary
    
    def create_pre_model_hook(self) -> callable:
        """
        创建用于create_react_agent的pre_model_hook函数
        
        Returns:
            pre_model_hook函数，在模型调用前进行上下文总结并修改状态
        """
        def pre_model_hook(state: Dict[str, Any]) -> Dict[str, Any]:
            """
            pre_model_hook函数：在模型调用前处理消息，进行上下文总结并修改状态
            
            Args:
                state: LangGraph状态，包含messages等信息
                
            Returns:
                包含更新后的messages和running_summary的字典，用于修改状态
            """
            if not self.config.enabled:
                # 如果未启用，返回空字典（不修改状态）
                return {}
            
            messages = state.get("messages", [])
            if not messages:
                return {}
            
            # 从状态中获取运行总结
            running_summary = state.get("running_summary")
            
            # 检查是否需要总结（避免重复压缩）
            if not self.should_summarize(messages):
                # 不需要总结，返回空字典（不修改状态）
                return {}
            
            # 为消息添加ID（如果没有的话）
            messages_with_ids = self._ensure_message_ids(messages)
            
            # 执行总结
            processed_messages, updated_summary = self.summarize_messages_with_state(
                messages_with_ids, running_summary
            )
            
            # 检查是否实际进行了总结（processed_messages是否与原始messages不同）
            if len(processed_messages) >= len(messages):
                # 没有进行实际的压缩，返回空字典
                return {}
            
            logger.info(f"通过pre_model_hook进行上下文总结：{len(messages)} -> {len(processed_messages)} 消息")
            
            # 构建状态更新：修改状态中的消息历史
            state_update = {
                # 使用RemoveMessage移除所有现有消息，然后添加处理后的消息
                "messages": [RemoveMessage(REMOVE_ALL_MESSAGES)] + processed_messages
            }
            
            # 如果有更新的运行总结，也保存到状态中
            if updated_summary != running_summary:
                state_update["running_summary"] = updated_summary
                logger.debug("更新了运行总结状态")
            
            return state_update
        
        return pre_model_hook

    def get_config_dict(self) -> Dict[str, Any]:
        """获取配置信息"""
        return asdict(self.config)

# 全局默认总结器实例
_default_summarizer: Optional[ContextSummarizer] = None

def get_default_summarizer() -> ContextSummarizer:
    """获取默认的上下文总结器实例"""
    global _default_summarizer
    if _default_summarizer is None:
        _default_summarizer = ContextSummarizer()
    return _default_summarizer

def create_summarization_pre_model_hook(config: Optional[SummarizationConfig] = None) -> callable:
    """
    创建带有上下文总结功能的pre_model_hook函数（快捷方法）
    
    Args:
        config: 总结配置，如果不提供则使用默认配置
        
    Returns:
        pre_model_hook函数
    """
    if config:
        summarizer = ContextSummarizer(config)
    else:
        summarizer = get_default_summarizer()
    
    return summarizer.create_pre_model_hook()

# 状态扩展，为LangGraph状态添加总结相关字段
def extend_state_for_summarization(state_class):
    """
    为LangGraph状态类添加总结相关字段的装饰器
    
    Args:
        state_class: 要扩展的状态类
        
    Returns:
        扩展后的状态类
    """
    # 动态添加running_summary字段
    if not hasattr(state_class, '__annotations__'):
        state_class.__annotations__ = {}
    
    state_class.__annotations__['running_summary'] = Optional[RunningSummary]
    
    return state_class
