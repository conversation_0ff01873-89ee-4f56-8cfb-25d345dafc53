from pymongo import MongoClient, ASCENDING, DESCENDING, IndexModel
from pymongo.errors import ConnectionFailure, PyMongoError
from datetime import datetime, timedelta
from typing import Optional, Dict, List, Tuple
from bson import ObjectId


class MongoDBClient:
    """
    升级版MongoDB工具类
    功能增强：认证支持、动态集合、分页查询、时间维护、安全删除
    """

    def __init__(self, mode: str=None, host: str=None, port: int=None,
                 db_name: str=None, username: str=None, password: str=None, hosts: str=None):
        self.client = None
        self.db = None
        self.mode = mode
        self.host = host
        self.port = port
        self.db_name = db_name
        self.username = username
        self.password = password

        try:
            # 构建认证连接字符串
            # auth_str = ""
            # if username and password:
            #     auth_str = f"{username}:{password}@"

            uri = f"url: ****************************************************************************************************************************************************"
            # if self.mode == "single":
            #     uri = f"mongodb://{auth_str}{host}:{port}/{self.db_name}?authSource=admin&maxPoolSize=300&minPoolSize=30"
            # else:
            #     uri = f"mongodb://{auth_str}{hosts}/{self.db_name}?authSource=admin&maxPoolSize=300&minPoolSize=30"
            print(uri)
            self.client = MongoClient(
                uri,
                serverSelectionTimeoutMS=5000,
                authSource=db_name
            )

            # 验证连接
            # self.client.server_info()
            self.db = self.client[db_name]
        except ConnectionFailure as e:
            raise ConnectionError(f"MongoDB connection failed: {str(e)}")

    def _get_collection(self, collection_name: str):
        """获取集合对象（动态集合）"""
        return self.db[collection_name]

    def close(self):
        """关闭数据库连接"""
        if self.client:
            self.client.close()

    def create_qa_collection(self, collection_name: str):
        """创建问答记录集合（带数据验证）"""
        try:
            validator = {
                "$jsonSchema": {
                    "bsonType": "object",
                    "required": ["question", "create_time", "apikey"],
                    "properties": {
                        "question": {"bsonType": "string"},
                        "answer": {"bsonType": "string"},
                        "reasoning": {"bsonType": "string"},
                        "create_time": {"bsonType": "date"},
                        "apikey": {"bsonType": "string"}
                    }
                }
            }

            # 如果集合不存在则创建
            if collection_name not in self.db.list_collection_names():
                self.db.create_collection(
                    collection_name,
                    validator=validator
                )

            collection = self._get_collection(collection_name)

            # 创建索引
            indexes = [
                IndexModel(
                    [("apikey", ASCENDING), ("create_time", ASCENDING)],  # 使用元组列表格式
                    name="idx_apikey_time"
                )
            ]
            collection.create_indexes(indexes)

        except PyMongoError as e:
            raise RuntimeError(f"Create collection failed: {str(e)}")

    def insert_qa_record(self, collection_name: str, question: str, answer: str,
                         reasoning: str, apikey: str) -> Dict:
        """
        插入问答记录（带自动时间维护）
        :return: 包含完整数据的插入文档
        """
        collection = self._get_collection(collection_name)

        document = {
            "question": question,
            "answer": answer,
            "reasoning": reasoning,
            "create_time": datetime.utcnow(),
            "apikey": apikey
        }

        try:
            result = collection.insert_one(document)
            return {**document, "_id": result.inserted_id}
        except Exception as e:
            raise RuntimeError(f"Insert QA failed: {str(e)}")

    def paginate_qa_records(self, collection_name: str, page: int = 1,
                            per_page: int = 10, query: Dict = None) -> Tuple[List[Dict], int]:
        """
        分页查询问答记录
        :return: (当前页数据, 总记录数)
        """
        collection = self._get_collection(collection_name)
        query = query or {}

        try:
            # 计算总数
            total = collection.count_documents(query)

            # 分页查询
            skip = (page - 1) * per_page
            cursor = collection.find(query).skip(skip).limit(per_page).sort("create_time", DESCENDING)

            return list(cursor), total
        except PyMongoError as e:
            raise RuntimeError(f"Paginate query failed: {str(e)}")

    def delete_records_by_time_range(self, collection_name: str,
                                     start_time: str, end_time: str) -> int:
        """
        按时间范围删除记录
        :param start_time: UTC时间字符串，格式：YYYY-MM-DD HH:MM:SS
        :param end_time: 同上
        """
        collection = self._get_collection(collection_name)

        try:
            # 转换时间格式
            start = datetime.strptime(start_time, "%Y-%m-%d %H:%M:%S")
            end = datetime.strptime(end_time, "%Y-%m-%d %H:%M:%S")

            # 构建查询条件
            query = {
                "create_time": {
                    "$gte": start,
                    "$lte": end
                }
            }

            result = collection.delete_many(query)
            return result.deleted_count
        except ValueError as e:
            raise ValueError(f"Invalid time format: {str(e)}")
        except PyMongoError as e:
            raise RuntimeError(f"Delete operation failed: {str(e)}")

mongo_config = {
    'host': '*************',
    'port': 37017,
    'user': 'index',
    'password': 'nt85535888',
    'database': 'indexNo',
    'collection': 'sx_jiliang_qa_records'
}

# 使用示例
if __name__ == "__main__":
    client = None
    try:
        # 初始化带认证的客户端
        client = MongoDBClient(
            host = mongo_config["host"],
            port = mongo_config["port"],
            db_name=mongo_config["database"],
            username = mongo_config["user"],
            password = mongo_config["password"]
        )

        # 创建问答集合
        client.create_qa_collection(mongo_config["collection"])

        # 插入记录
        record = client.insert_qa_record(
            collection_name="qa_records",
            question="如何优化MongoDB性能？",
            answer="添加合适的索引，优化查询语句",
            reasoning="索引是查询性能的关键因素",
            apikey="sk-123456"
        )
        print("插入成功：", record)

        # 分页查询
        page_data, total = client.paginate_qa_records(
            collection_name=mongo_config["collection"],
            page=1,
            per_page=5
        )
        print(f"第一页数据（共{total}条）：", page_data)

        # 时间范围删除
        deleted_count = client.delete_records_by_time_range(
            collection_name=mongo_config["collection"],
            start_time="2023-01-01 00:00:00",
            end_time="2023-12-31 23:59:59"
        )
        print(f"删除记录数：{deleted_count}")

    except Exception as e:
        print(f"操作异常：{str(e)}")
    finally:
        if client:
            client.close()