import os
from dotenv import load_dotenv

# 加载环境变量
load_dotenv()

# OCR服务配置
OCR_CONFIG = {
  # OCR服务基础URL
  "base_url": os.getenv("OCR_BASE_URL", "https://aimp.ctg.com.cn/Others/iuocr/tuling/uocr/v2/recognize"),
  
  # 认证令牌
  "auth_token": os.getenv("OCR_AUTH_TOKEN", "ACCESSCODE 1105E98CC7B73BEACE470B7D6AD06F0A"),
  
  # 跟踪ID
  "track_id": os.getenv("OCR_TRACK_ID", "1122"),
  
  # OCR处理参数
  "params": {
    "char_option": "1",  # 单字开关（1:开启）
    "table_option": "1",  # 表格开关（1:开启）
    "element_option": "1",  # 要素开关（1:开启）
    "document_option": "word,excel",  # 文档格式化输出类型(word/excel/word,excel)
    "result_option": "all",  # 输出结果控制（all:全部输出）
  },
  
  # 临时文件设置
  "temp_dir": os.getenv("OCR_TEMP_DIR", os.path.join(os.path.dirname(os.path.abspath(__file__)), "temp")),
  
  # 输出文件设置
  "output_prefix": os.getenv("OCR_OUTPUT_PREFIX", "ocr_result"),
  
  # 重试设置
  "max_retries": int(os.getenv("OCR_MAX_RETRIES", "3")),
  "retry_delay": int(os.getenv("OCR_RETRY_DELAY", "2")),
}

# 确保临时目录存在
os.makedirs(OCR_CONFIG["temp_dir"], exist_ok=True)


def get_ocr_config():
  """获取OCR配置"""
  return OCR_CONFIG


def update_ocr_config(new_config):
  """更新OCR配置"""
  global OCR_CONFIG
  OCR_CONFIG.update(new_config)
  return OCR_CONFIG


def get_ocr_params(document_type="word"):
  """
  获取OCR参数，可以根据文档类型调整

  Args:
      document_type: 文档类型，可选值: "word", "excel", "word,excel"

  Returns:
      dict: OCR参数
  """
  params = OCR_CONFIG["params"].copy()
  params["document_option"] = document_type
  return params