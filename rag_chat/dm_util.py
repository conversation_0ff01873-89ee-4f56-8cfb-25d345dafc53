import dmPython
from typing import List, Tuple, Optional, Dict, Any


class DMDatabase:
    def __init__(self,
                 host: str,
                 port: int,
                 user: str,
                 password: str,
                 database: str,
                 auto_commit: bool = False):
        """
        达梦数据库工具类构造函数

        :param host: 数据库地址
        :param port: 端口号（默认5236）
        :param user: 用户名
        :param password: 密码
        :param database: 数据库名
        :param auto_commit: 是否自动提交事务（默认False）
        """
        self.config = {
            'host': host,
            'port': port,
            'user': user,
            'password': password,
            'database': database,
            'auto_commit': auto_commit
        }
        self.conn: Optional[dmPython.Connection] = None
        self.cursor: Optional[dmPython.Cursor] = None

    def __enter__(self):
        self.connect()
        return self

    def __exit__(self, exc_type, exc_val, exc_tb):
        self.close()

    def connect(self):
        """建立数据库连接"""
        # try:
        self.conn = dmPython.connect(
            server=self.config['host'],
            port=self.config['port'],
            user=self.config['user'],
            password=self.config['password']
        )
        self.cursor = self.conn.cursor()
        # self.conn.autocommit = self.config['auto_commit']
        # except dmPython.Error as e:
        #     raise ConnectionError(f"数据库连接失败: {str(e)}") from e

    def close(self):
        """关闭数据库连接"""
        try:
            if self.cursor:
                self.cursor.close()
            if self.conn:
                self.conn.close()
        except dmPython.Error as e:
            print(f"关闭连接时发生错误: {str(e)}")

    def execute_query(self,
                      sql: str,
                      params: Optional[Tuple] = None) -> List[Dict[str, Any]]:
        """
        执行查询语句

        :param sql: SQL语句
        :param params: 查询参数（可选）
        :return: 查询结果列表（字典形式）
        """
        if not self.conn or not self.cursor:
            raise ConnectionError("数据库未连接")

        try:
            self.cursor.execute(sql, params)
            columns = [col[0] for col in self.cursor.description]
            return [dict(zip(columns, row)) for row in self.cursor.fetchall()]
        except dmPython.Error as e:
            self.conn.rollback()
            raise RuntimeError(f"查询执行失败: {str(e)}") from e

    def execute_update(self,
                       sql: str,
                       params: Optional[Tuple] = None) -> int:
        """
        执行更新操作（INSERT/UPDATE/DELETE）

        :param sql: SQL语句
        :param params: 查询参数（可选）
        :return: 受影响的行数
        """
        if not self.conn or not self.cursor:
            raise ConnectionError("数据库未连接")

        try:
            self.cursor.execute(sql, params)
            return self.cursor.rowcount
        except dmPython.Error as e:
            self.conn.rollback()
            raise RuntimeError(f"更新操作失败: {str(e)}") from e

    def commit(self):
        """提交事务"""
        if self.conn:
            try:
                self.conn.commit()
            except dmPython.Error as e:
                raise RuntimeError(f"提交事务失败: {str(e)}") from e

    def rollback(self):
        """回滚事务"""
        if self.conn:
            try:
                self.conn.rollback()
            except dmPython.Error as e:
                raise RuntimeError(f"回滚事务失败: {str(e)}") from e


# 示例用法
if __name__ == '__main__':
    # 数据库配置（根据实际情况修改）
    db_config = {
        'host': '*************',
        'port': 5236,
        'user': 'SX_NACOS',
        'password': 'Gznt_85535888',
        'database': 'SX_JILIANG',
        'auto_commit': False
    }

    try:
        with DMDatabase(**db_config) as db:
            # 查询示例
            query_result = db.execute_query("SELECT * FROM SX_QA_RECORD", (25,))
            print(f"查询结果: {query_result}")

            # 插入示例
            insert_count = db.execute_update(
                "INSERT INTO USER (NAME, AGE) VALUES (:1, :2)",
                ('张三', 30)
            )
            print(f"插入影响行数: {insert_count}")
            db.commit()

            # 更新示例
            update_count = db.execute_update(
                "UPDATE USER SET AGE = :1 WHERE NAME = :2",
                (31, '张三')
            )
            print(f"更新影响行数: {update_count}")
            db.commit()

            # 删除示例
            delete_count = db.execute_update(
                "DELETE FROM USER WHERE NAME = :1",
                ('张三',)
            )
            print(f"删除影响行数: {delete_count}")
            db.commit()

    except Exception as e:
        print(f"数据库操作异常: {str(e)}")
        if 'db' in locals():
            db.rollback()