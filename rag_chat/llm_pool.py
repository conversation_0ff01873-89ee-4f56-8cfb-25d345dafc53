"""
LLM连接池管理器

优化LLM实例创建和管理，减少重复连接和内存占用。
"""

import os
import threading
from typing import Dict, Optional, Any
from langchain_openai import ChatOpenAI
from langchain_deepseek import ChatDeepSeek
from pydantic import SecretStr
from rag_chat.logger import logger


class LLMConnectionPool:
    """LLM连接池管理器，使用单例模式管理LLM实例"""
    
    _instance = None
    _lock = threading.Lock()
    
    def __new__(cls):
        if cls._instance is None:
            with cls._lock:
                if cls._instance is None:
                    cls._instance = super().__new__(cls)
                    cls._instance._initialized = False
        return cls._instance
    
    def __init__(self):
        if not self._initialized:
            self._llm_cache: Dict[str, Any] = {}
            self._cache_lock = threading.Lock()
            self._initialized = True
            logger.info("LLM连接池管理器初始化完成")
    
    def get_llm(self, 
                model_name: str = "",
                temperature: float = 0.6,
                extra_headers: Optional[Dict[str, Any]] = None,
                is_reasoning: bool = False,
                callbacks: Optional[list] = None) -> Any:
        """
        获取LLM实例，如果不存在则创建，存在则复用
        
        Args:
            model_name: 模型名称
            temperature: 温度参数
            extra_headers: 额外请求头
            is_reasoning: 是否为推理模型
            callbacks: 回调列表
            
        Returns:
            LLM实例
        """
        if extra_headers is None:
            extra_headers = {}
        
        # 确定最终的模型名称    
        final_model_name = model_name if model_name else str(os.getenv("OPENAI_MODEL"))
        
        # 生成缓存键 - 基于核心参数，忽略易变的callbacks和extra_headers
        cache_key = f"{final_model_name}_{temperature}_{is_reasoning}"
        
        with self._cache_lock:
            if cache_key not in self._llm_cache:
                logger.info(f"创建新的LLM实例: {cache_key}")
                
                # 创建基础LLM实例（不包含callbacks，因为callbacks是会话级别的）
                if not is_reasoning:
                    llm = ChatOpenAI(
                        model=final_model_name,
                        api_key=SecretStr(str(os.getenv("OPENAI_API_KEY"))),
                        base_url=str(os.getenv("OPENAI_BASE_URL")),
                        temperature=temperature,
                        top_p=0.9,
                        # 优化连接池参数
                        max_retries=3,
                        timeout=30,
                        # 移除default_headers，因为它们可能包含会话特定信息
                        extra_body={"enable_thinking": False},
                        stream_usage=True
                    )
                else:
                    llm = ChatDeepSeek(
                        model=final_model_name,
                        api_key=SecretStr(str(os.getenv("OPENAI_API_KEY"))),
                        api_base=str(os.getenv("OPENAI_BASE_URL")),
                        temperature=temperature,
                        top_p=0.9,
                        # 优化连接池参数
                        max_retries=3,
                        timeout=30,
                        extra_body={"enable_thinking": True},
                        stream_usage=True
                    )
                
                # 缓存基础实例
                self._llm_cache[cache_key] = llm
                logger.info(f"LLM实例已缓存: {cache_key}")
            else:
                logger.debug(f"复用现有LLM实例: {cache_key}")
        
        # 获取基础实例
        base_llm = self._llm_cache[cache_key]
        
        # 如果需要添加会话特定的配置（如callbacks和headers），创建一个临时的配置实例
        if callbacks or extra_headers:
            # 创建一个带有会话特定配置的实例副本
            configured_llm = self._create_configured_instance(base_llm, extra_headers, callbacks)
            return configured_llm
        
        return base_llm
    
    def _create_configured_instance(self, base_llm: Any, extra_headers: Dict[str, Any], callbacks: Optional[list]) -> Any:
        """创建带有会话特定配置的LLM实例"""
        try:
            # 复制基础配置
            if isinstance(base_llm, ChatOpenAI):
                configured_llm = ChatOpenAI(
                    model=base_llm.model_name,
                    api_key=base_llm.openai_api_key,
                    base_url=base_llm.openai_api_base,
                    temperature=base_llm.temperature,
                    top_p=base_llm.top_p,
                    max_retries=base_llm.max_retries,
                    timeout=base_llm.request_timeout,
                    default_headers=extra_headers,
                    extra_body=base_llm.model_kwargs.get("extra_body", {}),
                    stream_usage=True,
                    callbacks=callbacks or []
                )
            elif isinstance(base_llm, ChatDeepSeek):
                configured_llm = ChatDeepSeek(
                    model=base_llm.model_name,
                    api_key=base_llm.openai_api_key,
                    api_base=base_llm.openai_api_base,
                    temperature=base_llm.temperature,
                    top_p=base_llm.top_p,
                    max_retries=base_llm.max_retries,
                    timeout=base_llm.request_timeout,
                    default_headers=extra_headers,
                    extra_body=base_llm.model_kwargs.get("extra_body", {}),
                    stream_usage=True,
                    callbacks=callbacks or []
                )
            else:
                # 如果是其他类型的LLM，直接使用基础实例
                configured_llm = base_llm
                if callbacks:
                    configured_llm.callbacks = callbacks
            
            return configured_llm
        except Exception as e:
            logger.warning(f"创建配置实例失败，使用基础实例: {e}")
            return base_llm
    
    def get_cache_stats(self) -> Dict[str, Any]:
        """获取连接池统计信息"""
        with self._cache_lock:
            return {
                "cached_instances": len(self._llm_cache),
                "cache_keys": list(self._llm_cache.keys())
            }
    
    def clear_cache(self):
        """清空连接池缓存（谨慎使用）"""
        with self._cache_lock:
            old_count = len(self._llm_cache)
            self._llm_cache.clear()
            logger.info(f"已清空LLM连接池缓存，原有 {old_count} 个实例")


# 全局连接池实例
llm_pool = LLMConnectionPool()


def get_llm_from_pool(model_name: str = "",
                     temperature: float = 0.6,
                     extra_headers: Optional[Dict[str, Any]] = None,
                     is_reasoning: bool = False,
                     agent_name: str = "unknown",
                     callbacks: Optional[list] = None) -> Any:
    """
    便捷函数：从连接池获取LLM实例
    
    Args:
        model_name: 模型名称
        temperature: 温度参数
        extra_headers: 额外请求头
        is_reasoning: 是否为推理模型
        agent_name: Agent名称（用于日志）
        callbacks: 回调列表
        
    Returns:
        配置好的LLM实例
    """
    logger.debug(f"为Agent {agent_name} 获取LLM实例: {model_name}")
    
    return llm_pool.get_llm(
        model_name=model_name,
        temperature=temperature,
        extra_headers=extra_headers,
        is_reasoning=is_reasoning,
        callbacks=callbacks
    )


def get_pool_stats() -> Dict[str, Any]:
    """获取连接池统计信息"""
    return llm_pool.get_cache_stats()