import asyncio
import json
import os
import uuid
from datetime import datetime
from typing import Any, Callable, Optional, Union
from contextlib import asynccontextmanager
import aio_pika
from aio_pika import (
    ExchangeType,
    Message,
    RobustConnection,
    RobustChannel,
    RobustExchange,
    RobustQueue,
)
from aio_pika.abc import AbstractIncomingMessage, DeliveryMode
from loguru import logger

class RabbitMQError(Exception):
    """RabbitMQ 基础异常"""
    pass


class RabbitMQConnectionError(RabbitMQError):
    """连接异常"""
    pass


class RabbitMQTimeoutError(RabbitMQError):
    """操作超时异常"""
    pass


class RabbitMQClient:
    def __init__(
        self,
        *,
        config: dict = None,
        url: str = None,
        max_retries: int = 3,
        retry_interval: float = 1.0,
        connection_timeout: float = 10.0,
        operation_timeout: float = 30.0,
    ):
        """
        初始化 RabbitMQ 客户端

        :param config: 配置字典，包含连接参数
        :param url: RabbitMQ 连接URL (amqp://user:pass@host:port/vhost)
        :param max_retries: 最大重试次数
        :param retry_interval: 重试间隔(秒)
        :param connection_timeout: 连接超时时间(秒)
        :param operation_timeout: 操作超时时间(秒)
        """
        if not config:
            self.config = dict(os.environ)
        else:
            self.config = config

        print(self.config)

        if not url:
            self.url = f"amqp://{self.config.get('RABBITMQ_USERNAME', 'guest')}:{self.config.get('RABBITMQ_PASSWORD', 'guest')}@{self.config.get('RABBITMQ_HOST', 'localhost')}:{self.config.get('RABBITMQ_PORT', 5672)}/"
        else:
            self.url = url
        
        self.max_retries = max_retries
        self.retry_interval = retry_interval
        self.connection_timeout = connection_timeout
        self.operation_timeout = operation_timeout
        self._connection: Optional[RobustConnection] = None
        self._channel: Optional[RobustChannel] = None

    def getTaskId(self):
        """生成任务ID"""
        return str(uuid.uuid4())
    async def connect(self) -> None:
        """建立连接和通道"""
        if self._connection and not self._connection.is_closed:
            return

        try:
            self._connection = await asyncio.wait_for(
                aio_pika.connect_robust(self.url),
                timeout=self.connection_timeout,
            )
            self._channel = await self._connection.channel()
            logger.info("RabbitMQ connection established")
        except asyncio.TimeoutError as e:
            raise RabbitMQTimeoutError("Connection timeout") from e
        except Exception as e:
            raise RabbitMQConnectionError(f"Failed to connect: {str(e)}") from e

    async def close(self) -> None:
        """关闭连接"""
        if self._connection and not self._connection.is_closed:
            await self._connection.close()
            logger.info("RabbitMQ connection closed")
            self._connection = None
            self._channel = None

    async def ensure_connection(self) -> None:
        """确保连接可用"""
        if not self._connection or self._connection.is_closed:
            await self.connect()

    @asynccontextmanager
    async def get_channel(self) -> RobustChannel:
        """获取通道的上下文管理器"""
        await self.ensure_connection()
        if not self._channel or self._channel.is_closed:
            self._channel = await self._connection.channel()
        yield self._channel

    async def simple_publish(
        self,
        task_id: str, 
        message: Optional[dict],    
        *,
        durable: bool = True,
        delivery_mode: DeliveryMode = DeliveryMode.PERSISTENT,
        mandatory: bool = True,
        headers: Optional[dict] = None,
        expiration: Optional[int] = None,
        xexpires: Optional[int] = 7 * 24 * 60 * 60 * 1000) -> None:
        
        """
        简单发布消息到指定交换器

        :param task_id: 任务ID
        :param message: 消息内容
        :param exchange_type: 交换器类型
        :param durable: 是否持久化
        :param delivery_mode: 传递模式
        :param mandatory: 是否强制路由
        :param headers: 消息头
        :param expiration: 消息过期时间(秒)
        """
        try: 
            await self.ensure_connection()

            if task_id:
                message = {**message, "task_id": task_id, "update_time": datetime.now().strftime("%Y-%m-%d %H:%M:%S")}

                attempts = 0
                last_error = None
                while attempts < self.max_retries:
                    attempts += 1
                    try:
                        async with self.get_channel() as channel:
                            # aio_pika 的 queue_declare 是异步方法，需要 await
                            await channel.declare_queue(
                                name=task_id, 
                                durable=durable,
                                arguments={"x-expires": xexpires}
                            )
                            msg = Message(
                                body=json.dumps(message).encode(),
                                delivery_mode=delivery_mode,
                                headers=headers,
                                expiration=expiration,
                            )
                            await asyncio.wait_for(
                                channel.default_exchange.publish(
                                    msg,
                                    routing_key=task_id,
                                    mandatory=mandatory,
                                ),
                                timeout=self.operation_timeout,
                            )
                            return
                    except asyncio.TimeoutError as e:
                        last_error = RabbitMQTimeoutError(f"Publish operation timeout: {str(e)}")
                        logger.warning(f"Publish timeout, attempt {attempts}/{self.max_retries}")
                    except Exception as e:
                        last_error = RabbitMQError(f"Failed to publish message: {str(e)}")
                        logger.error(
                            f"Publish failed, attempt {attempts}/{self.max_retries}: {str(e)}",
                            exc_info=True,
                        )

                    if attempts < self.max_retries:
                        await asyncio.sleep(self.retry_interval)
        except Exception as e:
            logger.error(f"Failed to publish message: {str(e)}")

    async def consume(
        self,
        queue_name: str,
        callback: Callable[[AbstractIncomingMessage], Any],
        *,
        durable: bool = True,
        exclusive: bool = False,
        auto_delete: bool = False,
        prefetch_count: int = 1,
        no_ack: bool = False,
        arguments: Optional[dict] = None,
    ) -> None:
        """
        消费指定队列的消息

        :param queue_name: 队列名称
        :param callback: 消息处理回调函数
        :param durable: 是否持久化
        :param exclusive: 是否排他
        :param auto_delete: 是否自动删除
        :param prefetch_count: 预取数量
        :param no_ack: 是否自动确认
        :param arguments: 额外参数
        """
        async def process_message(message: AbstractIncomingMessage) -> None:
            try:
                await callback(message)
                if not no_ack:
                    await message.ack()
            except Exception as e:
                logger.error(f"Error processing message: {str(e)}", exc_info=True)
                if not no_ack:
                    await message.nack(requeue=False)

        attempts = 0
        last_error = None

        while attempts < self.max_retries:
            attempts += 1
            try:
                async with self.get_channel() as channel:
                    await channel.set_qos(prefetch_count=prefetch_count)
                    queue = await channel.declare_queue(
                        queue_name,
                        durable=durable,
                        exclusive=exclusive,
                        auto_delete=auto_delete,
                        arguments=arguments,
                        timeout=self.operation_timeout,
                    )
                    await queue.consume(process_message, timeout=self.operation_timeout)
                    logger.info(f"Started consuming queue: {queue_name}")
                    # 保持连接持续消费
                    while True:
                        await asyncio.sleep(1)
            except asyncio.TimeoutError as e:
                last_error = RabbitMQTimeoutError(f"Consume operation timeout: {str(e)}")
                logger.warning(f"Consume timeout, attempt {attempts}/{self.max_retries}")
            except Exception as e:
                last_error = RabbitMQError(f"Failed to consume messages: {str(e)}")
                logger.error(
                    f"Consume failed, attempt {attempts}/{self.max_retries}: {str(e)}",
                    exc_info=True,
                )

            if attempts < self.max_retries:
                await asyncio.sleep(self.retry_interval)

        raise last_error or RabbitMQError("Failed to consume messages after retries")

    async def declare_queue(
        self,
        queue_name: str,
        *,
        durable: bool = True,
        exclusive: bool = False,
        auto_delete: bool = False,
        arguments: Optional[dict] = None,
    ) -> RobustQueue:
        """
        声明队列

        :param queue_name: 队列名称
        :param durable: 是否持久化
        :param exclusive: 是否排他
        :param auto_delete: 是否自动删除
        :param arguments: 额外参数
        :return: 队列对象
        """
        attempts = 0
        last_error = None

        while attempts < self.max_retries:
            attempts += 1
            try:
                async with self.get_channel() as channel:
                    queue = await channel.declare_queue(
                        queue_name,
                        durable=durable,
                        exclusive=exclusive,
                        auto_delete=auto_delete,
                        arguments=arguments,
                        timeout=self.operation_timeout,
                    )
                    logger.debug(f"Queue declared: {queue_name}")
                    return queue
            except asyncio.TimeoutError as e:
                last_error = RabbitMQTimeoutError(f"Declare queue timeout: {str(e)}")
                logger.warning(
                    f"Declare queue timeout, attempt {attempts}/{self.max_retries}"
                )
            except Exception as e:
                last_error = RabbitMQError(f"Failed to declare queue: {str(e)}")
                logger.error(
                    f"Declare queue failed, attempt {attempts}/{self.max_retries}: {str(e)}",
                    exc_info=True,
                )

            if attempts < self.max_retries:
                await asyncio.sleep(self.retry_interval)

        raise last_error or RabbitMQError("Failed to declare queue after retries")

    async def declare_exchange(
        self,
        exchange_name: str,
        exchange_type: ExchangeType = ExchangeType.DIRECT,
        *,
        durable: bool = True,
        auto_delete: bool = False,
        arguments: Optional[dict] = None,
    ) -> RobustExchange:
        """
        声明交换器

        :param exchange_name: 交换器名称
        :param exchange_type: 交换器类型
        :param durable: 是否持久化
        :param auto_delete: 是否自动删除
        :param arguments: 额外参数
        :return: 交换器对象
        """
        attempts = 0
        last_error = None

        while attempts < self.max_retries:
            attempts += 1
            try:
                async with self.get_channel() as channel:
                    exchange = await channel.declare_exchange(
                        exchange_name,
                        type=exchange_type,
                        durable=durable,
                        auto_delete=auto_delete,
                        arguments=arguments,
                        timeout=self.operation_timeout,
                    )
                    logger.debug(f"Exchange declared: {exchange_name}")
                    return exchange
            except asyncio.TimeoutError as e:
                last_error = RabbitMQTimeoutError(f"Declare exchange timeout: {str(e)}")
                logger.warning(
                    f"Declare exchange timeout, attempt {attempts}/{self.max_retries}"
                )
            except Exception as e:
                last_error = RabbitMQError(f"Failed to declare exchange: {str(e)}")
                logger.error(
                    f"Declare exchange failed, attempt {attempts}/{self.max_retries}: {str(e)}",
                    exc_info=True,
                )

            if attempts < self.max_retries:
                await asyncio.sleep(self.retry_interval)

        raise last_error or RabbitMQError("Failed to declare exchange after retries")

    async def bind_queue(
        self,
        queue_name: str,
        exchange_name: str,
        routing_key: str = "",
        *,
        arguments: Optional[dict] = None,
    ) -> None:
        """
        绑定队列到交换器

        :param queue_name: 队列名称
        :param exchange_name: 交换器名称
        :param routing_key: 路由键
        :param arguments: 额外参数
        """
        attempts = 0
        last_error = None

        while attempts < self.max_retries:
            attempts += 1
            try:
                async with self.get_channel() as channel:
                    queue = await channel.get_queue(queue_name)
                    exchange = await channel.get_exchange(exchange_name)
                    await queue.bind(
                        exchange,
                        routing_key=routing_key,
                        arguments=arguments,
                        timeout=self.operation_timeout,
                    )
                    logger.debug(
                        f"Queue {queue_name} bound to exchange {exchange_name} with routing_key {routing_key}"
                    )
                    return
            except asyncio.TimeoutError as e:
                last_error = RabbitMQTimeoutError(f"Bind queue timeout: {str(e)}")
                logger.warning(f"Bind queue timeout, attempt {attempts}/{self.max_retries}")
            except Exception as e:
                last_error = RabbitMQError(f"Failed to bind queue: {str(e)}")
                logger.error(
                    f"Bind queue failed, attempt {attempts}/{self.max_retries}: {str(e)}",
                    exc_info=True,
                )

            if attempts < self.max_retries:
                await asyncio.sleep(self.retry_interval)

        raise last_error or RabbitMQError("Failed to bind queue after retries")

    async def __aenter__(self):
        await self.connect()
        return self

    async def __aexit__(self, exc_type, exc_val, exc_tb):
        await self.close()

async def test1():
    rabbitMQClient = RabbitMQClient()
    taskId = rabbitMQClient.getTaskId()
    logger.info("taskId: "+taskId)
    for i in range(1, 11):
        await rabbitMQClient.simple_publish(taskId, {"message": f"Hello, RabbitMQ! {i}" })

async def callback(message: AbstractIncomingMessage):
    logger.info(f"收到消息：body={message.body}, headers={message.headers}, delivery_tag={message.delivery_tag}, exchange={message.exchange}, routing_key={message.routing_key}")

async def test2():
    rabbitMQClient = RabbitMQClient()
    await rabbitMQClient.consume("6fd858b9-1286-467f-95a3-a781ae88f2a0", callback=callback)

if __name__ == "__main__":
    asyncio.run(test1())