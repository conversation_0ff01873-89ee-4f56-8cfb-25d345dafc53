"""
长期记忆管理模块
基于mem0实现跨会话的语义记忆存储和检索
与langmem短期记忆系统协同工作
"""

import os
import json
import asyncio
from typing import List, Dict, Any, Optional, Tuple
from dataclasses import dataclass, field
from datetime import datetime
import logging

try:
    import requests
    REQUESTS_AVAILABLE = True
except ImportError:
    REQUESTS_AVAILABLE = False
    requests = None

from .logger import logger

@dataclass
class MemoryConfig:
    """长期记忆配置"""
    enabled: bool = True
    base_url: str = ""  # mem0 API基础URL
    timeout: int = 30   # 请求超时时间
    # 注意：api_key 和 username 不再从配置读取，而是从请求头动态获取
    
    # 记忆存储配置
    store_threshold: float = 0.1  # 存储阈值，越低越容易存储
    search_limit: int = 5         # 搜索返回的记忆数量
    search_threshold: float = 0.8 # 搜索相关性阈值
    
    # 记忆类型配置
    chat_types: Dict[int, str] = field(default_factory=lambda: {
        0: "普通问答",
        1: "文件问答", 
        2: "知识库问答"
    })
    
    @classmethod
    def from_env(cls) -> 'MemoryConfig':
        """从环境变量创建配置（不包含API密钥和用户名）"""
        return cls(
            enabled=os.getenv("LONGTERM_MEMORY_ENABLED", "true").lower() == "true",
            base_url=os.getenv("LONGTERM_MEMORY_BASE_URL", ""),
            timeout=int(os.getenv("LONGTERM_MEMORY_TIMEOUT", "30")),
            store_threshold=float(os.getenv("LONGTERM_MEMORY_STORE_THRESHOLD", "0.1")),
            search_limit=int(os.getenv("LONGTERM_MEMORY_SEARCH_LIMIT", "5")),
            search_threshold=float(os.getenv("LONGTERM_MEMORY_SEARCH_THRESHOLD", "0.8"))
        )

@dataclass
class MemoryItem:
    """记忆项数据结构"""
    memory_id: str
    memory: str
    hash: str
    metadata: Dict[str, Any]
    score: float
    created_at: str
    updated_at: Optional[str]
    userid: str
    qa_id: str
    event: Optional[str] = None  # ADD, UPDATE
    previous_memory: Optional[Dict[str, Any]] = None

class LongTermMemoryManager:
    """长期记忆管理器"""
    
    def __init__(self, config: Optional[MemoryConfig] = None):
        self.config = config or MemoryConfig.from_env()
        self._session = None
        
        if not REQUESTS_AVAILABLE:
            logger.warning("requests库未安装，长期记忆功能将被禁用")
            self.config.enabled = False
            return
            
        if not self.config.enabled:
            logger.info("长期记忆功能已禁用")
            return
            
        if not self.config.base_url:
            logger.warning("长期记忆配置不完整（缺少base_url），功能将被禁用")
            self.config.enabled = False
            return
            
        logger.info("长期记忆管理器初始化成功")
    
    def _build_headers(self, api_key: str, username: str) -> Dict[str, str]:
        """构建请求头"""
        headers = {
            'X-API-Key': api_key or os.getenv("LONGTERM_MEMORY_API_KEY", ""),
            'X-Username': username or os.getenv("LONGTERM_MEMORY_USERNAME", ""),
            'Content-Type': 'application/json'
        }
        return headers
    
    async def store_conversation(self, 
                                userid: str,
                                qa_id: str,
                                messages: List[Dict[str, str]],
                                api_key: str,
                                username: str,
                                agent_id: str = "",
                                files: Optional[List[Dict[str, Any]]] = None,
                                chat_type: int = 0) -> List[MemoryItem]:
        """
        存储对话记忆
        
        Args:
            userid: 用户ID
            qa_id: 会话ID
            messages: 消息列表 [{"role": "user/assistant", "content": "..."}]
            api_key: API密钥（从请求头获取）
            username: 用户名（从请求头获取）
            agent_id: 智能体ID
            files: 文件列表
            chat_type: 对话类型 0普通问答，1文件问答，2知识库问答
        
        Returns:
            存储的记忆项列表
        """
        if not self.config.enabled:
            logger.debug("长期记忆功能未启用，跳过存储")
            return []
        
        try:
            url = f"{self.config.base_url}/jiliang/qa_message_memory_create"
            
            payload = {
                "userid": userid,
                "qa_id": qa_id,
                "agent_id": agent_id,
                "messages": messages,
                "files": files or [],
                "metadata": {
                    "chat_type": chat_type
                }
            }
            
            headers = self._build_headers(api_key, username)
            
            # 异步执行HTTP请求
            loop = asyncio.get_event_loop()
            response = await loop.run_in_executor(
                None, 
                lambda: requests.post(
                    url, 
                    json=payload, 
                    headers=headers, 
                    timeout=self.config.timeout
                )
            )
            
            if response.status_code == 200:
                result = response.json()
                if result.get("code") == "200":
                    memories = []
                    for item in result.get("data", []):
                        memory = MemoryItem(
                            memory_id=item.get("memory_id", ""),
                            memory=item.get("memory", ""),
                            hash=item.get("hash", ""),
                            metadata=item.get("metadata", {}),
                            score=0.0,  # 存储时不提供score
                            created_at=datetime.now().isoformat(),
                            updated_at=item.get("updated_at"),
                            userid=userid,
                            qa_id=qa_id,
                            event=item.get("event", "ADD"),
                            previous_memory=item.get("previous_memory")
                        )
                        memories.append(memory)
                    
                    logger.info(f"成功存储{len(memories)}条长期记忆 - 用户:{userid}, 会话:{qa_id}")
                    return memories
                else:
                    logger.error(f"存储长期记忆失败: {result.get('errorMsg', '未知错误')}")
                    return []
            else:
                logger.error(f"存储长期记忆HTTP请求失败: {response.status_code}")
                return []
                
        except Exception as e:
            logger.error(f"存储长期记忆异常: {e}")
            return []
    
    async def search_memories(self,
                             query: str,
                             userid: str,
                             api_key: str,
                             username: str,
                             qa_id: str = "",
                             agent_id: str = "",
                             limit: Optional[int] = None,
                             threshold: Optional[float] = None,
                             chat_type: Optional[int] = None) -> List[MemoryItem]:
        """
        搜索相关记忆
        
        Args:
            query: 搜索查询
            userid: 用户ID
            qa_id: 会话ID（可选）
            agent_id: 智能体ID（可选）
            limit: 返回结果数量限制
            threshold: 相关性阈值
            chat_type: 对话类型过滤
        
        Returns:
            相关记忆列表
        """
        if not self.config.enabled:
            logger.debug("长期记忆功能未启用，跳过搜索")
            return []
        
        try:
            url = f"{self.config.base_url}/jiliang/qa_message_memory_search"
            
            payload = {
                "query": query,
                "userid": userid,
                "qa_id": qa_id,
                "agent_id": agent_id,
                "limit": limit or self.config.search_limit,
                "threshold": threshold or self.config.search_threshold
            }
            
            # 添加过滤条件
            if chat_type is not None:
                payload["filters"] = {"chat_type": chat_type}
            
            headers = self._build_headers(api_key, username)
            
            # 异步执行HTTP请求
            loop = asyncio.get_event_loop()
            response = await loop.run_in_executor(
                None,
                lambda: requests.post(
                    url,
                    json=payload,
                    headers=headers,
                    timeout=self.config.timeout
                )
            )
            
            if response.status_code == 200:
                result = response.json()
                if result.get("code") == "200":
                    memories = []
                    for item in result.get("data", []):
                        memory = MemoryItem(
                            memory_id=item.get("memory_id", ""),
                            memory=item.get("memory", ""),
                            hash=item.get("hash", ""),
                            metadata=item.get("metadata", {}),
                            score=item.get("score", 0.0),
                            created_at=item.get("created_at", ""),
                            updated_at=item.get("updated_at"),
                            userid=item.get("user_id", userid),  # API返回user_id字段
                            qa_id=item.get("qa_id", "")
                        )
                        memories.append(memory)
                    
                    logger.info(f"搜索到{len(memories)}条相关长期记忆 - 用户:{userid}, 查询:{query[:50]}...")
                    return memories
                else:
                    logger.error(f"搜索长期记忆失败: {result.get('errorMsg', '未知错误')}")
                    return []
            else:
                logger.error(f"搜索长期记忆HTTP请求失败: {response.status_code}")
                return []
                
        except Exception as e:
            logger.error(f"搜索长期记忆异常: {e}")
            return []
    
    async def get_session_memories(self,
                                  userid: str,
                                  api_key: str,
                                  username: str,
                                  qa_id: str = "",
                                  agent_id: str = "",
                                  chat_type: Optional[int] = None) -> List[MemoryItem]:
        """
        获取会话所有记忆
        
        Args:
            userid: 用户ID
            qa_id: 会话ID（可选）
            agent_id: 智能体ID（可选）
            chat_type: 对话类型过滤
        
        Returns:
            会话记忆列表
        """
        if not self.config.enabled:
            logger.debug("长期记忆功能未启用，跳过获取")
            return []
        
        try:
            url = f"{self.config.base_url}/jiliang/qa_session_all_memories"
            
            payload = {
                "userid": userid,
                "qa_id": qa_id,
                "agent_id": agent_id
            }
            
            if chat_type is not None:
                payload["chat_type"] = chat_type
            
            headers = self._build_headers(api_key, username)
            
            # 异步执行HTTP请求
            loop = asyncio.get_event_loop()
            response = await loop.run_in_executor(
                None,
                lambda: requests.post(
                    url,
                    json=payload,
                    headers=headers,
                    timeout=self.config.timeout
                )
            )
            
            if response.status_code == 200:
                result = response.json()
                if result.get("code") == "200":
                    memories = []
                    for item in result.get("data", []):
                        memory = MemoryItem(
                            memory_id=item.get("memory_id", ""),
                            memory=item.get("memory", ""),
                            hash=item.get("hash", ""),
                            metadata=item.get("metadata", {}),
                            score=0.0,  # 获取所有记忆时不提供score
                            created_at=item.get("created_at", ""),
                            updated_at=item.get("updated_at"),
                            userid=item.get("user_id", userid),  # API返回user_id字段
                            qa_id=item.get("qa_id", "")
                        )
                        memories.append(memory)
                    
                    logger.info(f"获取到{len(memories)}条会话记忆 - 用户:{userid}, 会话:{qa_id}")
                    return memories
                else:
                    logger.error(f"获取会话记忆失败: {result.get('errorMsg', '未知错误')}")
                    return []
            else:
                logger.error(f"获取会话记忆HTTP请求失败: {response.status_code}")
                return []
                
        except Exception as e:
            logger.error(f"获取会话记忆异常: {e}")
            return []
    
    async def delete_memories(self,
                             userid: str,
                             qa_id: str,
                             api_key: str,
                             username: str,
                             agent_id: str = "",
                             chat_type: int = 0,
                             memory_ids: Optional[List[str]] = None,
                             delete_all: bool = False) -> bool:
        """
        删除记忆
        
        Args:
            userid: 用户ID
            qa_id: 会话ID
            agent_id: 智能体ID
            chat_type: 对话类型
            memory_ids: 要删除的记忆ID列表
            delete_all: 是否删除会话所有记忆
        
        Returns:
            删除是否成功
        """
        if not self.config.enabled:
            logger.debug("长期记忆功能未启用，跳过删除")
            return False
        
        try:
            url = f"{self.config.base_url}/jiliang/qa_session_memories_delete"
            
            payload = {
                "userid": userid,
                "qa_id": qa_id,
                "agent_id": agent_id,
                "chat_type": chat_type,
                "delete_all": delete_all
            }
            
            if memory_ids:
                payload["memory_ids"] = memory_ids
            
            headers = self._build_headers(api_key, username)
            
            # 异步执行HTTP请求
            loop = asyncio.get_event_loop()
            response = await loop.run_in_executor(
                None,
                lambda: requests.post(
                    url,
                    json=payload,
                    headers=headers,
                    timeout=self.config.timeout
                )
            )
            
            if response.status_code == 200:
                result = response.json()
                if result.get("code") == "200":
                    if delete_all:
                        logger.info(f"成功删除会话所有记忆 - 用户:{userid}, 会话:{qa_id}")
                    else:
                        logger.info(f"成功删除指定记忆 - 用户:{userid}, 记忆数:{len(memory_ids or [])}")
                    return True
                else:
                    logger.error(f"删除记忆失败: {result.get('errorMsg', '未知错误')}")
                    return False
            else:
                logger.error(f"删除记忆HTTP请求失败: {response.status_code}")
                return False
                
        except Exception as e:
            logger.error(f"删除记忆异常: {e}")
            return False
    
    def format_memories_for_context(self, memories: List[MemoryItem]) -> str:
        """
        格式化记忆为上下文字符串
        
        Args:
            memories: 记忆列表
        
        Returns:
            格式化的上下文字符串
        """
        if not memories:
            return ""
        
        formatted_lines = ["# 相关记忆信息"]
        
        for i, memory in enumerate(memories, 1):
            chat_type_name = self.config.chat_types.get(
                memory.metadata.get("chat_type", 0), 
                "未知类型"
            )
            
            formatted_lines.extend([
                f"## 记忆 {i}",
                f"**类型**: {chat_type_name}",
                f"**内容**: {memory.memory}",
                f"**相关性**: {memory.score:.3f}",
                f"**时间**: {memory.created_at[:19] if memory.created_at else '未知'}",
                ""
            ])
        
        return "\n".join(formatted_lines)
    
    async def close(self):
        """关闭资源"""
        if self._session:
            self._session.close()
            logger.debug("长期记忆管理器已关闭")

# 全局实例（单例模式）
_global_memory_manager: Optional[LongTermMemoryManager] = None

def get_memory_manager() -> LongTermMemoryManager:
    """获取全局长期记忆管理器实例"""
    global _global_memory_manager
    if _global_memory_manager is None:
        _global_memory_manager = LongTermMemoryManager()
    return _global_memory_manager

async def close_memory_manager():
    """关闭全局长期记忆管理器"""
    global _global_memory_manager
    if _global_memory_manager:
        await _global_memory_manager.close()
        _global_memory_manager = None