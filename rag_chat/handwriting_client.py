import json
import base64
import asyncio
import aiohttp
from typing import Dict, Any, Optional
import logging

# 配置日志
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)


class HandwritingClient:
  """手写OCR识别客户端"""
  
  def __init__(self, base_url: str = "https://aimp.ctg.com.cn",
               access_code: str = "FC1A8CCC83C31E5BF90775681F141C5E",
               api_key: str = None, username: str = None):
    """
    初始化手写OCR客户端

    Args:
        base_url: 手写OCR服务基础URL（固定配置）
        access_code: 访问码（固定配置）
        api_key: API Key（用于认证）
        username: 用户名（用于认证）
    """
    self.base_url = base_url.rstrip('/')
    self.access_code = access_code
    self.api_key = api_key
    self.username = username
    
    self.classify_url = f"{self.base_url}/Others/sxsb"
  
  async def recognize_handwriting(self, image_data: bytes,
                                  object_type: str = "general_v5",
                                  type_param: str = "st_ocrapi",
                                  languagetype: str = "CHN_ENG",
                                  line_probability: bool = False,
                                  img_direction: bool = False,
                                  handprint_type: str = None,
                                  eng_granularity: str = "word") -> Dict[str, Any]:
    """
    手写文字识别

    Args:
        image_data: 图片二进制数据
        object_type: 必填，修正为 general_v5
        type_param: 可选 {st_ocrapi: 返回行识别结果, st_ocrapi_all: 返回单字结果}
        languagetype: 语言类型，默认CHN_ENG
        line_probability: 是否返回行置信度
        img_direction: 是否开启方向判断
        handprint_type: 跳过检测，直接当作单行识别。handrecog_only
        eng_granularity: 英文粒度 {word: 按单词粒度, letter: 按字符粒度}

    Returns:
        dict: 识别结果
    """
    try:
      # 编码图片数据为base64
      image_base64 = base64.b64encode(image_data).decode('utf-8')
      
      params = [
        f"object_type={object_type}",
        f"type={type_param}",
        f"languagetype={languagetype}"
      ]
      
      if line_probability:
        params.append("line_probability=true")
      
      if img_direction:
        params.append("imgDirection=setImgDirFlag")
      
      if handprint_type:
        params.append(f"handprint_type={handprint_type}")
      
      if eng_granularity and eng_granularity != "word":
        params.append(f"eng_granularity={eng_granularity}")
      
      params.append(f"image={image_base64}")
      
      request_str = "&".join(params)
      
      payload = {
        "provider": "default",
        "data": base64.b64encode(request_str.encode('utf-8')).decode('utf-8')
      }
      
      logger.info(f"开始手写OCR识别，图片大小: {len(image_data)} bytes")
      logger.info(f"请求URL: {self.classify_url}")
      logger.info(f"使用API Key: {self.api_key[:10]}... (前10位)")
      logger.info(f"使用用户名: {self.username}")
      
      headers = {
        "Content-Type": "application/json",
        "Authorization": f"ACCESSCODE {self.access_code}",
        "X-API-Key": self.api_key,
        "X-Username": self.username,
        "User-Agent": "HandwritingOCR/1.0",
        "Accept": "application/json"
      }
      
      async with aiohttp.ClientSession() as session:
        async with session.post(
            self.classify_url,
            json=payload,
            headers=headers,
            timeout=aiohttp.ClientTimeout(total=60),
            ssl=False
        ) as response:
          
          logger.info(f"响应状态码: {response.status}")
          logger.info(f"响应头 Content-Type: {response.headers.get('Content-Type', 'Unknown')}")
          
          response_text = await response.text()
          logger.info(f"响应内容: {response_text[:500]}...")  # 只显示前500字符
          
          if response.status == 200:
            try:
              response_data = json.loads(response_text)
            except json.JSONDecodeError as json_error:
              logger.error(f"JSON解析失败: {json_error}")
              return {
                "success": False,
                "message": f"服务器返回非JSON格式数据: {response_text[:200]}",
                "status_code": response.status,
                "content_type": response.headers.get('Content-Type'),
                "raw_response": response_text
              }
            
            # 检查API响应状态
            if response_data.get('err_no') != 0:
              return {
                "success": False,
                "message": f"API错误: {response_data.get('err_msg', 'Unknown error')}",
                "error_code": response_data.get('err_no'),
                "raw_response": response_data
              }
            
            if 'result' in response_data:
              try:
                result_str = base64.b64decode(response_data['result']).decode('utf-8')
                result_json = json.loads(result_str)
                
                logger.info("手写OCR识别成功")
                return {
                  "success": True,
                  "data": result_json,
                  "message": "识别成功",
                  "calc_time_ms": response_data.get('calc_time_ms', 0),
                  "wait_time_ms": response_data.get('wait_time_ms', 0)
                }
              except Exception as decode_error:
                logger.error(f"解码结果失败: {decode_error}")
                return {
                  "success": False,
                  "message": f"解码结果失败: {str(decode_error)}",
                  "raw_response": response_data
                }
            else:
              return {
                "success": False,
                "message": "返回结果中缺少result字段",
                "raw_response": response_data
              }
          else:
            logger.error(f"手写OCR识别失败，状态码: {response.status}")
            return {
              "success": False,
              "message": f"HTTP错误: {response.status}",
              "status_code": response.status,
              "content_type": response.headers.get('Content-Type'),
              "raw_response": response_text
            }
    
    except Exception as e:
      logger.error(f"手写OCR识别异常: {e}")
      return {
        "success": False,
        "message": f"识别异常: {str(e)}",
        "error": str(e)
      }
  
  async def recognize_file(self, file_path: str, **kwargs) -> Dict[str, Any]:
    """
    识别图片文件

    Args:
        file_path: 图片文件路径
        **kwargs: 其他参数

    Returns:
        dict: 识别结果
    """
    try:
      with open(file_path, 'rb') as f:
        image_data = f.read()
      
      result = await self.recognize_handwriting(image_data, **kwargs)
      return result
    
    except Exception as e:
      logger.error(f"文件识别失败: {e}")
      return {
        "success": False,
        "message": f"文件识别失败: {str(e)}",
        "error": str(e)
      }
  
  def parse_recognition_result(self, result_data: dict) -> Dict[str, Any]:
    """
    解析识别结果，提取有用信息

    Args:
        result_data: 识别返回的原始数据

    Returns:
        dict: 解析后的结果
    """
    try:
      if not result_data.get("success"):
        return result_data
      
      ocr_result = result_data.get("data", {})

      err_no = ocr_result.get("err_no", -1)
      if err_no != 0:
        return {
          "success": False,
          "message": f"OCR错误: {ocr_result.get('err_msg', 'Unknown error')}",
          "error_code": err_no
        }

      ret_list = ocr_result.get("ret", [])
      
      lines = []
      all_text = []
      
      for line_result in ret_list:
        line_info = {
          "text": line_result.get("word", ""),
          "rect": line_result.get("rect", {}),
          "attribute": line_result.get("attribute", "")
        }
        
        if "line_probability" in line_result:
          line_info["line_probability"] = line_result["line_probability"]
        
        if "charset" in line_result:
          line_info["characters"] = []
          for char_info in line_result["charset"]:
            line_info["characters"].append({
              "char": char_info.get("word", ""),
              "rect": char_info.get("rect", {}),
              "candidates": char_info.get("candidates", [])
            })
        
        if "poly_location" in line_result:
          line_info["poly_location"] = line_result["poly_location"]
        
        lines.append(line_info)
        all_text.append(line_result.get("word", ""))
      
      image_info = ocr_result.get("image_info", {})
      
      return {
        "success": True,
        "message": "识别成功",
        "lines": lines,
        "full_text": "\n".join(all_text),
        "line_count": len(lines),
        "image_direction": image_info.get("image_dir", 0),
        "calc_time_ms": result_data.get("calc_time_ms", 0),
        "wait_time_ms": result_data.get("wait_time_ms", 0),
        "raw_result": ocr_result
      }
    
    except Exception as e:
      logger.error(f"解析识别结果失败: {e}")
      return {
        "success": False,
        "message": f"解析结果失败: {str(e)}",
        "error": str(e)
      }


# 使用示例
async def example_usage():
  """使用示例"""
  
  client = HandwritingClient(
    base_url="https://aimp.ctg.com.cn",
    access_code="FC1A8CCC83C31E5BF90775681F141C5E",
    api_key="your-api-key",
    username="your-username"
  )
  
  # 示例1：识别图片文件
  print("=== 手写OCR识别测试 ===")
  result = await client.recognize_file("handwriting_image.jpg")
  
  if result["success"]:
    print("手写识别成功:")

    parsed_result = client.parse_recognition_result(result)
    if parsed_result["success"]:
      print(f"识别文本: {parsed_result['full_text']}")
      print(f"行数: {parsed_result['line_count']}")
      print(f"处理时间: {parsed_result['calc_time_ms']}ms")
      for i, line in enumerate(parsed_result["lines"]):
        print(f"第{i + 1}行: {line['text']}")
        if 'line_probability' in line:
          prob = line['line_probability']
          print(f"  置信度 - 平均: {prob.get('average', 0):.4f}, "
                f"最小: {prob.get('min', 0):.4f}, "
                f"方差: {prob.get('variance', 0):.6f}")
    else:
      print(f"解析失败: {parsed_result['message']}")
  else:
    print(f"手写识别失败: {result['message']}")
  
  print("\n=== 二进制数据识别测试 ===")
  with open("handwriting_image.jpg", "rb") as f:
    image_data = f.read()
  
  result2 = await client.recognize_handwriting(
    image_data,
    object_type="general_v5",
    type_param="st_ocrapi_all",
    languagetype="CHN_ENG",
    line_probability=True,
    img_direction=True,
    eng_granularity="word"
  )
  
  if result2["success"]:
    print("二进制数据识别成功:")
    parsed_result2 = client.parse_recognition_result(result2)
    print(json.dumps(parsed_result2, indent=2, ensure_ascii=False))
  else:
    print(f"二进制数据识别失败: {result2['message']}")


if __name__ == "__main__":
  # 运行示例
  asyncio.run(example_usage())