"""
音频转换器
"""

import os
import tempfile
import subprocess
import logging
import struct
from typing import Tuple, Optional, List
from pathlib import Path

logger = logging.getLogger(__name__)

try:
    from pydub import AudioSegment
    from pydub.utils import which
    PYDUB_AVAILABLE = True
    logger.info("pydub library available")
except ImportError as e:
    logger.warning(f"pydub library not available: {e}")
    PYDUB_AVAILABLE = False

try:
    import numpy as np
    NUMPY_AVAILABLE = True
    logger.info("numpy library available")
except ImportError:
    NUMPY_AVAILABLE = False
    logger.warning("numpy library not available")

# SILK 解码器检测
SILK_DECODER = None
SILK_DECODER_TYPE = None

SILK_PACKAGES = [
    'pysilk',
    'silk',
    'pysilk_mod',
]

for package_name in SILK_PACKAGES:
    try:
        silk_module = __import__(package_name)
        if hasattr(silk_module, 'decode'):
            SILK_DECODER = silk_module
            SILK_DECODER_TYPE = package_name
            logger.info(f"Found SILK decoder: {package_name}")
            break
    except ImportError:
        continue

if not SILK_DECODER:
    logger.warning("No SILK Python decoder found")
    logger.info("Install suggestion: pip install pysilk-mod or pip install silk-python")


class AudioConverterOptimized:
    """优化的音频格式转换器"""
    
    def __init__(self, ffmpeg_path: str = None):
        """
        初始化音频转换器
        
        Args:
            ffmpeg_path: ffmpeg executable path (optional)
        """
        logger.info("Initializing audio converter")
        
        # 检测 ffmpeg
        self.ffmpeg_path = ffmpeg_path or which("ffmpeg") if PYDUB_AVAILABLE else None
        self.ffmpeg_available = self._check_ffmpeg()
        
        # 检测 SILK 解码能力
        self.silk_available = SILK_DECODER is not None
        
        if self.ffmpeg_path and PYDUB_AVAILABLE:
            AudioSegment.converter = self.ffmpeg_path
            AudioSegment.ffmpeg = self.ffmpeg_path
            AudioSegment.ffprobe = self.ffmpeg_path.replace('ffmpeg', 'ffprobe')
        
        # 报告状态
        self._report_status()
    
    def _check_ffmpeg(self) -> bool:
        """检查 ffmpeg 可用性"""
        if not self.ffmpeg_path:
            return False
            
        try:
            result = subprocess.run(
                [self.ffmpeg_path, "-version"],
                capture_output=True,
                text=True,
                timeout=5
            )
            
            if result.returncode == 0:
                version_line = result.stdout.split('\n')[0]
                logger.info(f"ffmpeg available: {version_line}")
                return True
            else:
                logger.warning(f"ffmpeg test failed: {result.stderr}")
                return False
                
        except Exception as e:
            logger.warning(f"ffmpeg check failed: {e}")
            return False
    
    def _report_status(self):
        """报告转换器状态"""
        logger.info(f"SILK decoder: {'Available' if self.silk_available else 'Not available'} ({SILK_DECODER_TYPE or 'N/A'})")
        logger.info(f"ffmpeg: {'Available' if self.ffmpeg_available else 'Not available'}")
        logger.info(f"pydub: {'Available' if PYDUB_AVAILABLE else 'Not available'}")
        logger.info(f"numpy: {'Available' if NUMPY_AVAILABLE else 'Not available'}")
    
    def silk_to_wav(self, silk_data: bytes, sample_rate: int = 16000) -> bytes:
        """
        将 SILK 格式转换为 WAV
        
        Args:
            silk_data: SILK 数据
            sample_rate: 目标采样率
            
        Returns:
            WAV 格式数据
        """
        if not self.silk_available:
            raise ValueError("SILK decoder not available. Install with: pip install pysilk-mod")
        
        if len(silk_data) < 10:
            raise ValueError(f"SILK data too small ({len(silk_data)} bytes), may not be a valid SILK file")
        
        input_temp_file = None
        output_temp_file = None
        
        try:
            logger.info(f"Decoding SILK using {SILK_DECODER_TYPE}: {len(silk_data)} bytes")
            
            input_temp_file = tempfile.NamedTemporaryFile(delete=False, suffix='.silk')
            input_temp_file.write(silk_data)
            input_temp_file.flush()
            input_temp_file.close()
            
            output_temp_file = tempfile.NamedTemporaryFile(delete=False, suffix='.pcm')
            output_temp_file.close()
            
            try:
                # 方法1: 使用文件路径
                if SILK_DECODER_TYPE == 'pysilk':
                    with open(input_temp_file.name, 'rb') as input_f, open(output_temp_file.name, 'wb') as output_f:
                        SILK_DECODER.decode(input_f, output_f, sample_rate)
                else:
                    SILK_DECODER.decode(input_temp_file.name, output_temp_file.name, sample_rate)
                
                with open(output_temp_file.name, 'rb') as f:
                    pcm_data = f.read()
                    
            except Exception as e1:
                logger.info(f"File object method failed: {e1}, trying alternative methods")
                
                # 方法2: 尝试不同的参数组合
                try:
                    if SILK_DECODER_TYPE == 'pysilk':

                        SILK_DECODER.decode(input_temp_file.name, output_temp_file.name, sample_rate, 1)
                    else:
                        SILK_DECODER.decode(input_temp_file.name, output_temp_file.name, sample_rate)
                    
                    # 读取输出的PCM数据
                    with open(output_temp_file.name, 'rb') as f:
                        pcm_data = f.read()
                        
                except Exception as e2:
                    # 方法3: 检查包的具体接口
                    try:
                        import inspect
                        sig = inspect.signature(SILK_DECODER.decode)
                        logger.error(f"SILK decode function signature: {sig}")
                    except:
                        logger.error("Cannot inspect SILK decode function signature")
                    
                    raise ValueError(f"All SILK decoding methods failed: file_obj={e1}, file_path={e2}")
            
            if not pcm_data:
                raise ValueError("SILK decoding produced no output data")
            
            logger.info(f"SILK decode successful: {len(pcm_data)} bytes PCM")
            
            # 转换为 WAV
            wav_data = self.pcm_to_wav(pcm_data, sample_rate)
            logger.info(f"Conversion complete: {len(wav_data)} bytes WAV")
            
            return wav_data
            
        except Exception as e:
            logger.error(f"SILK decode failed: {e}")

            logger.error(f"SILK data first 16 bytes: {silk_data[:16].hex()}")
            raise ValueError(f"SILK decode failed: {str(e)}")
            
        finally:
            # 清理临时文件
            for temp_file in [input_temp_file, output_temp_file]:
                if temp_file and hasattr(temp_file, 'name'):
                    try:
                        os.unlink(temp_file.name)
                    except:
                        pass
    
    def pcm_to_wav(self, pcm_data: bytes, sample_rate: int = 16000,
                   channels: int = 1, sample_width: int = 2) -> bytes:
        """
        将 PCM 数据转换为 WAV 格式
        
        Args:
            pcm_data: PCM 数据
            sample_rate: 采样率
            channels: 声道数
            sample_width: 采样位深（字节）
            
        Returns:
            WAV 格式数据
        """
        if PYDUB_AVAILABLE:
            return self._pcm_to_wav_pydub(pcm_data, sample_rate, channels, sample_width)
        else:
            return self._pcm_to_wav_manual(pcm_data, sample_rate, channels, sample_width)
    
    def _pcm_to_wav_pydub(self, pcm_data: bytes, sample_rate: int,
                         channels: int, sample_width: int) -> bytes:
        """使用 pydub 转换 PCM 到 WAV"""
        try:
            audio = AudioSegment(
                data=pcm_data,
                sample_width=sample_width,
                frame_rate=sample_rate,
                channels=channels
            )
            
            audio = audio.set_channels(1).set_frame_rate(sample_rate).set_sample_width(2)
            
            # 导出为字节数据
            with tempfile.NamedTemporaryFile() as temp_file:
                audio.export(temp_file.name, format="wav")
                temp_file.seek(0)
                return temp_file.read()
                
        except Exception as e:
            logger.error(f"pydub PCM conversion failed: {e}")
            raise ValueError(f"PCM conversion failed: {str(e)}")
    
    def _pcm_to_wav_manual(self, pcm_data: bytes, sample_rate: int,
                          channels: int, sample_width: int) -> bytes:
        try:
            data_size = len(pcm_data)
            file_size = 36 + data_size
            
            wav_header = struct.pack(
                '<4sI4s4sIHHIIHH4sI',
                b'RIFF',           # ChunkID
                file_size,         # ChunkSize
                b'WAVE',           # Format
                b'fmt ',           # Subchunk1ID
                16,                # Subchunk1Size (PCM)
                1,                 # AudioFormat (PCM)
                channels,          # NumChannels
                sample_rate,       # SampleRate
                sample_rate * channels * sample_width,  # ByteRate
                channels * sample_width,  # BlockAlign
                sample_width * 8,  # BitsPerSample
                b'data',           # Subchunk2ID
                data_size          # Subchunk2Size
            )
            
            return wav_header + pcm_data
            
        except Exception as e:
            logger.error(f"Manual WAV construction failed: {e}")
            raise ValueError(f"WAV construction failed: {str(e)}")
    
    def any_to_wav(self, audio_data: bytes, source_format: str,
                   target_sample_rate: int = 16000) -> bytes:
        """
        转换任意格式到 WAV
        
        Args:
            audio_data: 音频数据
            source_format: 源格式
            target_sample_rate: 目标采样率
            
        Returns:
            WAV 数据
        """
        if not PYDUB_AVAILABLE or not self.ffmpeg_available:
            raise ValueError(f"Cannot convert {source_format} format: missing pydub or ffmpeg")
        
        try:
            with tempfile.NamedTemporaryFile(suffix=f'.{source_format}') as input_file:
                input_file.write(audio_data)
                input_file.flush()
                
                audio = AudioSegment.from_file(input_file.name)
                
                # 标准化
                audio = audio.set_channels(1)
                audio = audio.set_frame_rate(target_sample_rate)
                audio = audio.set_sample_width(2)
                
                # 导出
                with tempfile.NamedTemporaryFile() as output_file:
                    audio.export(output_file.name, format="wav")
                    output_file.seek(0)
                    return output_file.read()
                    
        except Exception as e:
            logger.error(f"{source_format} conversion failed: {e}")
            raise ValueError(f"{source_format} conversion failed: {str(e)}")
    
    def convert_audio(self, audio_data: bytes, filename: str,
                     target_sample_rate: int = 16000) -> Tuple[bytes, str]:
        """
        自动选择转换方法
        
        Args:
            audio_data: 音频数据
            filename: 文件名（用于判断格式）
            target_sample_rate: 目标采样率
            
        Returns:
            (转换后数据, 转换信息)
        """
        file_ext = Path(filename).suffix.lower()
        
        try:
            if file_ext == '.silk':
                if not self.silk_available:
                    raise ValueError("SILK decoder not available, install pysilk-mod or silk-python")
                
                wav_data = self.silk_to_wav(audio_data, target_sample_rate)
                return wav_data, f"SILK to WAV (using {SILK_DECODER_TYPE})"
            
            elif file_ext == '.pcm':
                wav_data = self.pcm_to_wav(audio_data, target_sample_rate)
                return wav_data, "PCM to WAV"
            
            elif file_ext == '.wav':
                # 标准化 WAV
                wav_data = self._normalize_wav(audio_data, target_sample_rate)
                return wav_data, "WAV normalization"
            
            elif file_ext in ['.mp3', '.aac', '.m4a', '.ogg', '.flac']:
                format_name = file_ext[1:]
                wav_data = self.any_to_wav(audio_data, format_name, target_sample_rate)
                return wav_data, f"{format_name.upper()} to WAV"
            
            else:
                raise ValueError(f"Unsupported format: {file_ext}")
                
        except Exception as e:
            logger.error(f"Conversion failed ({filename}): {e}")
            raise
    
    def _normalize_wav(self, wav_data: bytes, target_sample_rate: int) -> bytes:
        """标准化 WAV 文件"""
        if not PYDUB_AVAILABLE:
            logger.warning("pydub not available, skipping WAV normalization")
            return wav_data
        
        try:
            with tempfile.NamedTemporaryFile() as temp_file:
                temp_file.write(wav_data)
                temp_file.flush()
                
                audio = AudioSegment.from_wav(temp_file.name)
                
                # 标准化
                audio = audio.set_channels(1)
                audio = audio.set_frame_rate(target_sample_rate)
                audio = audio.set_sample_width(2)
                
                with tempfile.NamedTemporaryFile() as output_file:
                    audio.export(output_file.name, format="wav")
                    output_file.seek(0)
                    return output_file.read()
                    
        except Exception as e:
            logger.warning(f"WAV normalization failed, using original data: {e}")
            return wav_data
    
    def get_supported_formats(self) -> List[str]:
        """获取支持的格式列表"""
        formats = []
        
        if self.silk_available:
            formats.append('.silk')
        
        formats.append('.pcm')  # 总是支持
        formats.append('.wav')  # 总是支持
        
        if PYDUB_AVAILABLE and self.ffmpeg_available:
            formats.extend(['.mp3', '.aac', '.m4a', '.ogg', '.flac'])
        
        return formats
    
    def get_status_report(self) -> dict:
        """获取状态报告"""
        return {
            "silk_decoder": {
                "available": self.silk_available,
                "type": SILK_DECODER_TYPE,
                "module": SILK_DECODER.__name__ if SILK_DECODER else None
            },
            "ffmpeg": {
                "available": self.ffmpeg_available,
                "path": self.ffmpeg_path
            },
            "dependencies": {
                "pydub": PYDUB_AVAILABLE,
                "numpy": NUMPY_AVAILABLE
            },
            "supported_formats": self.get_supported_formats()
        }


# 全局实例
_converter_instance = None

def get_audio_converter_optimized(ffmpeg_path: str = None) -> AudioConverterOptimized:
    """获取优化转换器实例（单例）"""
    global _converter_instance
    if _converter_instance is None:
        _converter_instance = AudioConverterOptimized(ffmpeg_path)
    return _converter_instance

def convert_to_wav_optimized(audio_data: bytes, filename: str,
                           sample_rate: int = 16000) -> Tuple[bytes, str]:
    """便捷转换函数"""
    converter = get_audio_converter_optimized()
    return converter.convert_audio(audio_data, filename, sample_rate)


if __name__ == "__main__":
    # 测试和诊断
    import argparse
    
    parser = argparse.ArgumentParser(description='Audio converter testing')
    parser.add_argument('--test', help='Test file path')
    parser.add_argument('--status', action='store_true', help='Show status')
    
    args = parser.parse_args()
    
    logging.basicConfig(level=logging.INFO)
    
    converter = AudioConverterOptimized()
    
    if args.status:
        print("Converter status:")
        status = converter.get_status_report()
        import json
        print(json.dumps(status, indent=2, ensure_ascii=False))
    
    if args.test and os.path.exists(args.test):
        try:
            with open(args.test, 'rb') as f:
                data = f.read()
            
            wav_data, info = converter.convert_audio(data, args.test)
            print(f"Conversion successful: {info}")
            print(f"Output size: {len(wav_data)} bytes")
            
            # 保存结果
            output_file = args.test.replace(Path(args.test).suffix, '_converted.wav')
            with open(output_file, 'wb') as f:
                f.write(wav_data)
            print(f"Saved: {output_file}")
            
        except Exception as e:
            print(f"Test failed: {e}")