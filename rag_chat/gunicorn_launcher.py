#!/usr/bin/env python3
"""
Gunicorn启动器 - 解决gevent MonkeyPatch警告
确保在任何模块导入之前进行monkey patching
"""

# 第一步：立即进行monkey patching，在任何其他导入之前
import gevent.monkey
gevent.monkey.patch_all(ssl=True, thread=True)

# 第二步：现在可以安全导入其他模块
import subprocess
import sys
import multiprocessing

def main():
    """启动gunicorn服务器"""
    
    # 计算最优worker数量（多智能体系统内存密集，减少worker数量）
    worker_count = min(multiprocessing.cpu_count() + 1, 4)
    
    # gunicorn生产环境配置 - 基于2025年最佳实践优化
    gunicorn_cmd = [
        sys.executable, "-m", "gunicorn",
        "rag_chat.jiliang_chat:app",
        "--workers", str(worker_count),
        "--worker-class", "gevent",          # I/O密集型使用gevent
        "--worker-connections", "1000",      # 每个worker连接数
        "--bind", "0.0.0.0:18800",
        "--timeout", "300",                  # 多智能体处理时间长
        "--keep-alive", "5",                 # 保持连接降低开销
        "--max-requests", "250",             # 防止内存泄漏
        "--max-requests-jitter", "15",       # 避免同时重启
        "--worker-tmp-dir", "/dev/shm",      # 使用共享内存
        "--log-level", "info",
        "--access-logfile", "-",             # 访问日志到stdout
        "--error-logfile", "-"               # 错误日志到stderr
    ]
    
    print(f"启动gunicorn，worker数量: {worker_count}")
    print(f"命令: {' '.join(gunicorn_cmd)}")
    
    try:
        subprocess.run(gunicorn_cmd)
    except KeyboardInterrupt:
        print("服务被用户中断")
    except Exception as e:
        print(f"启动失败: {e}")
        sys.exit(1)

if __name__ == "__main__":
    main()