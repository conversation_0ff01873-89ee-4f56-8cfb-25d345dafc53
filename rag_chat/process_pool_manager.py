"""
进程池管理器模块
"""
import concurrent.futures
import os
import time
import threading
import atexit
from logger import logger
from functools import wraps

class ProcessPoolManager:
    """进程池管理器，支持自动重建和健康检查"""
    
    def __init__(self, max_workers=None):
        self.max_workers = max_workers or os.cpu_count()
        self._executor = None
        self._lock = threading.Lock()
        self._creation_time = None
        self._task_count = 0
        self._max_tasks_per_pool = 50  # 每个进程池最大任务数
        self._max_pool_lifetime = 1800  # 进程池最大生存时间（30分钟）
        
    def _create_new_executor(self):
        """创建新的进程池执行器"""
        if self._executor:
            try:
                self._executor.shutdown(wait=False)
            except Exception as e:
                logger.warning(f"关闭旧进程池时出错: {e}")
        
        self._executor = concurrent.futures.ProcessPoolExecutor(
            max_workers=self.max_workers
        )
        self._creation_time = time.time()
        self._task_count = 0
        logger.info(f"创建新进程池，工作进程数: {self.max_workers}")
        
    def _should_recreate_pool(self):
        """判断是否需要重建进程池"""
        if not self._executor:
            return True
            
        # 检查任务数量限制
        if self._task_count >= self._max_tasks_per_pool:
            logger.info(f"进程池任务数达到限制({self._max_tasks_per_pool})，重建进程池")
            return True
            
        # 检查生存时间限制
        if time.time() - self._creation_time > self._max_pool_lifetime:
            logger.info(f"进程池生存时间超过限制({self._max_pool_lifetime}秒)，重建进程池")
            return True
            
        return False
    
    def get_executor(self):
        """获取可用的进程池执行器"""
        with self._lock:
            if self._should_recreate_pool():
                self._create_new_executor()
            
            self._task_count += 1
            return self._executor
    
    def submit_with_retry(self, fn, *args, max_retries=2, **kwargs):
        """提交任务并支持重试"""
        last_exception = None
        
        for attempt in range(max_retries + 1):
            try:
                executor = self.get_executor()
                future = executor.submit(fn, *args, **kwargs)
                return future
                
            except Exception as e:
                last_exception = e
                logger.warning(f"进程池提交任务失败(尝试 {attempt + 1}/{max_retries + 1}): {e}")
                
                if attempt < max_retries:
                    # 强制重建进程池
                    with self._lock:
                        self._create_new_executor()
                    time.sleep(0.5)  # 等待500ms
        
        raise last_exception
    
    def shutdown(self):
        """关闭进程池"""
        with self._lock:
            if self._executor:
                try:
                    self._executor.shutdown(wait=True)
                    logger.info("进程池已正常关闭")
                except Exception as e:
                    logger.error(f"关闭进程池时出错: {e}")
                finally:
                    self._executor = None

# 创建全局进程池管理器实例
process_pool_manager = ProcessPoolManager()

# 确保应用退出时关闭进程池
def shutdown_process_pool():
    """关闭全局进程池"""
    try:
        process_pool_manager.shutdown()
    except Exception as e:
        logger.error(f"关闭进程池管理器时出错: {e}")

atexit.register(shutdown_process_pool)