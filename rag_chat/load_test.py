import argparse
import asyncio
import aiohttp
import time
import uuid
import json
import statistics
from datetime import datetime
from typing import List, Tuple
import logging
from dataclasses import dataclass, asdict  # 添加asdict的导入

logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)


@dataclass
class RequestTokenStats:
    total_tokens: int = 0
    reasoning_tokens: int = 0
    text_tokens: int = 0
    request_start_time: float = 0.0    # 新增：请求开始时间
    first_token_time: float = 0.0      # 新增：首个token时间
    generation_start_time: float = 0.0
    generation_end_time: float = 0.0

    @property
    def first_token_latency(self) -> float:
        """计算从请求开始到首个token的等待时间"""
        return self.first_token_time - self.request_start_time if self.first_token_time > 0 else 0.0

    @property
    def generation_time(self) -> float:
        return self.generation_end_time - self.generation_start_time if self.generation_end_time > 0 else 0.0

    @property
    def tokens_per_second(self) -> float:
        if self.generation_time > 0:
            return self.total_tokens / self.generation_time
        return 0.0


@dataclass
@dataclass
class GlobalTokenStats:
    total_tokens: int = 0
    reasoning_tokens: int = 0
    text_tokens: int = 0
    request_stats: List[RequestTokenStats] = None
    test_start_time: float = 0.0
    test_end_time: float = 0.0

    def __post_init__(self):
        if self.request_stats is None:
            self.request_stats = []
        if self.test_start_time == 0:
            self.test_start_time = time.time()

    def add_request_stats(self, stats: RequestTokenStats):
        self.request_stats.append(stats)
        self.total_tokens += stats.total_tokens
        self.reasoning_tokens += stats.reasoning_tokens
        self.text_tokens += stats.text_tokens
        self.test_end_time = time.time()

    @property
    def total_test_time(self) -> float:
        """计算整个测试的执行时间"""
        return self.test_end_time - self.test_start_time

    @property
    def system_throughput(self) -> float:
        """计算系统整体吞吐量 (tokens/second)"""
        if self.total_test_time > 0:
            return self.total_tokens / self.total_test_time
        return 0.0

    @property
    def global_tokens_per_second(self) -> float:
        """计算全局实际token生成速率"""
        if not self.request_stats:
            return 0.0
        total_generation_time = sum(stat.generation_time for stat in self.request_stats)
        if total_generation_time > 0:
            return self.total_tokens / total_generation_time
        return 0.0

    @property
    def mean_request_tokens_per_second(self) -> float:
        """计算所有请求的平均token生成速率"""
        if not self.request_stats:
            return 0.0
        valid_rates = [stat.tokens_per_second for stat in self.request_stats if stat.tokens_per_second > 0]
        if not valid_rates:
            return 0.0
        return statistics.mean(valid_rates)


@dataclass
class RequestStats:
    user_id: int
    qa_id: str
    response_time: float
    status_code: int
    timestamp: str
    total_tokens: int
    reasoning_tokens: int
    text_tokens: int
    generation_time: float = 0.0
    first_token_latency: float = 0.0  # 新增：首个token等待时间
    error_msg: str = ""
    is_error: bool = False

class DeepSeekLoadTest:
    def __init__(self, base_url: str, concurrent_users: int, test_duration: int):
        self.base_url = base_url
        self.concurrent_users = concurrent_users
        self.test_duration = test_duration
        self.chat_endpoint = f"{base_url}/chat"
        self.results: List[RequestStats] = []
        self.global_token_stats = GlobalTokenStats()
        self.error_count = 0
        self.total_requests = 0
        self.test_questions = [
            "介绍一下三峡集团。",
            "行云登录不了怎么办。",
            "介绍一下三峡能源。",
            "如何下载wps。",
            "介绍一下三峡集团的发展。",
        ]

    def get_random_question(self) -> str:
        """随机生成一个测试问题"""
        # 可以根据需要调整问题的组合方式
        import random
        base_question = random.choice(self.test_questions)
        follow_ups = [
            "请详细说明理由。",
            "你认为最大的挑战是什么？",
            "有什么可行的解决方案？",
        ]
        if random.random() < 0.3:  # 30%的概率添加追加问题
            base_question += " " + random.choice(follow_ups)
        return base_question

    def count_tokens(self, text: str) -> int:
        """简单的token计数估算"""
        return len(text.split())

    async def process_sse_response(self, response, request_start_time: float) -> Tuple[RequestStats, bool]:
        """处理SSE响应并计算token生成速度"""
        total_tokens = 0
        reasoning_tokens = 0
        text_tokens = 0
        first_token_time = None
        generation_start = None
        generation_end = None
        error_msg = ""
        is_error = False
        request_token_stats = RequestTokenStats(request_start_time=request_start_time)

        try:
            async for line in response.content:
                if line:
                    try:
                        data = line.decode('utf-8').strip()
                        if data.startswith('data: '):
                            json_data = json.loads(data[6:])

                            if json_data['type'] == 'error':
                                is_error = True
                                error_msg = json_data['data']
                                break

                            tokens = self.count_tokens(json_data['data'])
                            current_time = time.time()

                            # 记录第一个token的时间
                            if tokens > 0:
                                if first_token_time is None:
                                    first_token_time = current_time
                                    request_token_stats.first_token_time = current_time
                                if generation_start is None:
                                    generation_start = current_time
                                    request_token_stats.generation_start_time = current_time

                            if json_data['type'] == 'reasoning':
                                reasoning_tokens += tokens
                                request_token_stats.reasoning_tokens += tokens
                            elif json_data['type'] == 'text':
                                text_tokens += tokens
                                request_token_stats.text_tokens += tokens

                            total_tokens = reasoning_tokens + text_tokens
                            request_token_stats.total_tokens = total_tokens
                            generation_end = current_time

                    except Exception as e:
                        logger.error(f"Error parsing SSE data: {e}")
                        is_error = True
                        error_msg = str(e)

        except Exception as e:
            logger.error(f"Error processing SSE response: {e}")
            is_error = True
            error_msg = str(e)

        # 更新请求统计
        if not is_error and total_tokens > 0:
            request_token_stats.generation_end_time = generation_end
            self.global_token_stats.add_request_stats(request_token_stats)

        stats = RequestStats(
            user_id=0,  # 将在simulate_user中更新
            qa_id="",  # 将在simulate_user中更新
            response_time=time.time() - request_start_time,
            status_code=response.status,
            timestamp=datetime.now().isoformat(),
            total_tokens=total_tokens,
            reasoning_tokens=reasoning_tokens,
            text_tokens=text_tokens,
            generation_time=request_token_stats.generation_time,
            first_token_latency=request_token_stats.first_token_latency,  # 新增
            error_msg=error_msg,
            is_error=is_error
        )

        return stats, is_error


    async def simulate_user(self, user_id: int):
        """模拟单个用户的会话行为"""
        async with aiohttp.ClientSession() as session:
            start_time = time.time()
            while time.time() - start_time < self.test_duration:
                qa_id = str(uuid.uuid4())
                request_data = {
                    "question": self.get_random_question(),
                    "qa_id": qa_id
                }

                self.total_requests += 1
                request_start = time.time()

                try:
                    async with session.post(
                            self.chat_endpoint,
                            json=request_data,
                            headers={"Content-Type": "application/json"}
                    ) as response:
                        if response.status != 200:
                            self.error_count += 1
                            self.results.append(
                                RequestStats(
                                    user_id=user_id,
                                    qa_id=qa_id,
                                    response_time=time.time() - request_start,
                                    status_code=response.status,
                                    timestamp=datetime.now().isoformat(),
                                    total_tokens=0,
                                    reasoning_tokens=0,
                                    text_tokens=0,
                                    error_msg=f"HTTP {response.status}",
                                    is_error=True
                                )
                            )
                            continue

                        stats, is_error = await self.process_sse_response(response, request_start)

                        if is_error:
                            self.error_count += 1

                        # 更新请求特定的字段
                        stats.user_id = user_id
                        stats.qa_id = qa_id
                        self.results.append(stats)

                except Exception as e:
                    logger.error(f"Request error: {e}")
                    self.error_count += 1
                    self.results.append(
                        RequestStats(
                            user_id=user_id,
                            qa_id=qa_id,
                            response_time=time.time() - request_start,
                            status_code=500,
                            timestamp=datetime.now().isoformat(),
                            total_tokens=0,
                            reasoning_tokens=0,
                            text_tokens=0,
                            error_msg=str(e),
                            is_error=True
                        )
                    )

                # 随机等待100-500ms模拟真实用户行为
                await asyncio.sleep(asyncio.get_running_loop().time() % 0.4 + 0.1)

    async def run_test(self):
        """运行压力测试"""
        logger.info(f"开始压力测试: {self.concurrent_users}个并发用户, 持续{self.test_duration}秒")

        tasks = [
            self.simulate_user(user_id)
            for user_id in range(self.concurrent_users)
        ]

        await asyncio.gather(*tasks)
        self.generate_report()

    def generate_report(self):
        """生成测试报告"""
        successful_requests = [r for r in self.results if not r.is_error]
        if not successful_requests:
            logger.error("所有请求都失败了")
            return

        response_times = [r.response_time for r in successful_requests]
        first_token_latencies = [r.first_token_latency for r in successful_requests]

        report = {
            "测试配置": {
                "并发用户数": self.concurrent_users,
                "测试持续时间": f"{self.test_duration}秒"
            },
            "请求统计": {
                "总请求数": self.total_requests,
                "成功请求数": len(successful_requests),
                "失败请求数": self.error_count,
                "错误率": f"{(self.error_count / self.total_requests * 100):.2f}%",
                "RPS": f"{self.total_requests / self.test_duration:.2f}",
                "HTTP状态码分布": {
                    str(status): count
                    for status, count in statistics.Counter(
                        r.status_code for r in self.results
                    ).items()
                }
            },
            "响应时间统计": {
                "平均响应时间": f"{statistics.mean(response_times):.2f}秒",
                "最大响应时间": f"{max(response_times):.2f}秒",
                "最小响应时间": f"{min(response_times):.2f}秒",
                "90%响应时间": f"{statistics.quantiles(response_times, n=10)[-1]:.2f}秒" if len(
                    response_times) >= 2 else "N/A",
                "95%响应时间": f"{statistics.quantiles(response_times, n=20)[-1]:.2f}秒" if len(
                    response_times) >= 2 else "N/A",
            },
            "首个Token等待时间统计": {  # 新增
                "平均等待时间": f"{statistics.mean(first_token_latencies):.2f}秒",
                "最大等待时间": f"{max(first_token_latencies):.2f}秒",
                "最小等待时间": f"{min(first_token_latencies):.2f}秒",
                "90%等待时间": f"{statistics.quantiles(first_token_latencies, n=10)[-1]:.2f}秒" if len(
                    first_token_latencies) >= 2 else "N/A",
                "95%等待时间": f"{statistics.quantiles(first_token_latencies, n=20)[-1]:.2f}秒" if len(
                    first_token_latencies) >= 2 else "N/A",
            },
            "Token生成统计": {
                "总生成Token数": self.global_token_stats.total_tokens,
                "Reasoning Token数": self.global_token_stats.reasoning_tokens,
                "Text Token数": self.global_token_stats.text_tokens,
                "测试总时间": f"{self.global_token_stats.total_test_time:.2f}秒",
                "全局Token生成速率": f"{self.global_token_stats.global_tokens_per_second:.2f} tokens/s",
                "平均单个请求Token生成速率": f"{self.global_token_stats.mean_request_tokens_per_second:.2f} tokens/s",
                "每请求平均Token数": f"{self.global_token_stats.total_tokens / len(successful_requests):.2f}" if successful_requests else "N/A"
            }
        }

        logger.info("测试报告:")
        for section, metrics in report.items():
            logger.info(f"\n{section}:")
            for metric, value in metrics.items():
                logger.info(f"  {metric}: {value}")

        # 保存详细结果到文件
        with open(f'load_test_results_{int(time.time())}.json', 'w', encoding='utf-8') as f:
            json.dump({
                'test_config': {
                    'concurrent_users': self.concurrent_users,
                    'test_duration': self.test_duration,
                    'base_url': self.base_url
                },
                'results': [asdict(r) for r in self.results],
                'summary': report
            }, f, indent=2, ensure_ascii=False)

def main():
    parser = argparse.ArgumentParser(description='DeepSeek接口压力测试工具')
    # parser.add_argument('--url', type=str, required=True, help='接口基础URL')
    parser.add_argument('--users', type=int, default=10, help='并发用户数')
    parser.add_argument('--duration', type=int, default=60, help='测试持续时间(秒)')

    args = parser.parse_args()

    # load_test = DeepSeekLoadTest(
    #     base_url=args.url,
    #     concurrent_users=args.users,
    #     test_duration=args.duration
    # )
    load_test = DeepSeekLoadTest(
        base_url='http://localhost:18800',
        concurrent_users=15,
        test_duration=10,
    )

    asyncio.run(load_test.run_test())


if __name__ == '__main__':
    main()
