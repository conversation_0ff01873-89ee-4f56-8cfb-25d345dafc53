import fitz
import asyncio
from concurrent.futures import <PERSON><PERSON>oolExecutor

def pdf_to_text_worker(file_path, max_size):
    text = ''
    try:
        doc = fitz.open(file_path)
        max_page = len(doc)
        for idx, page in enumerate(doc):
            now_text = page.get_text().replace(' ', '')
            if len(text) + len(now_text) > max_size:
                break
            text += '第{}页\n'.format(idx + 1)
            text += now_text
    except Exception as e:
        print(e)
        return '', 0, 0
    return text, idx + 1, max_page

async def pdf_to_text(file_path, max_size):
    loop = asyncio.get_running_loop()
    with ProcessPoolExecutor() as executor:
        text, processed_pages, total_pages = await loop.run_in_executor(
            executor, pdf_to_text_worker, file_path, max_size
        )
    return text, processed_pages, total_pages


async def main():
    text, read_page, max_page = await pdf_to_text('中华人民共和国招标投标法.pdf', 50000)
    print(f'{len(text)} 读取了 {read_page} 页，总共 {max_page} 页')

if __name__ == '__main__':
    asyncio.run(main())

