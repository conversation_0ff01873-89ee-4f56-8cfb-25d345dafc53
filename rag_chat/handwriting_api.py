import os
import json
import base64
import tempfile
import shutil
from uuid import uuid4
from typing import Optional
from fastapi import APIRouter, File, UploadFile, Form, Request, HTTPException
from pydantic import BaseModel
import logging

from handwriting_client import HandwritingClient

# 配置日志
logger = logging.getLogger(__name__)

# 创建路由
router = APIRouter()

# 临时目录配置
TEMP_DIR = tempfile.gettempdir()
HANDWRITING_TEMP_DIR = os.path.join(TEMP_DIR, "handwriting_temp")
os.makedirs(HANDWRITING_TEMP_DIR, exist_ok=True)

# 支持的图片格式
ALLOWED_IMAGE_EXTENSIONS = ['.jpg', '.jpeg', '.png', '.bmp', '.tiff', '.tif', '.gif']

# 手写OCR服务配置
HANDWRITING_CONFIG = {
  "base_url": "https://aimp.ctg.com.cn",
  "access_code": "FC1A8CCC83C31E5BF90775681F141C5E"
}


class HandwritingRequest(BaseModel):
  """手写OCR请求模型"""
  image_base64: str
  object_type: Optional[str] = "general_handwriting"
  type_param: Optional[str] = "st_ocrapi_all"
  line_probability: Optional[bool] = False
  img_direction: Optional[bool] = False
  handprint_type: Optional[str] = None
  eng_granularity: Optional[str] = "word"
  api_key: Optional[str] = None
  username: Optional[str] = None


class HandwritingResponse(BaseModel):
  """手写OCR响应模型"""
  success: bool
  message: str
  data: Optional[dict] = None
  lines: Optional[list] = None
  full_text: Optional[str] = None
  line_count: Optional[int] = None
  image_direction: Optional[int] = None
  error: Optional[str] = None


def get_auth_from_request(request: Request, api_key: str = None, username: str = None):
  """从请求中获取认证信息"""
  auth_api_key = api_key or request.headers.get('x-api-key') or request.headers.get('X-API-Key')
  auth_username = username or request.headers.get('x-username') or request.headers.get('X-Username')
  
  if not auth_api_key:
    raise HTTPException(status_code=401, detail="缺少API Key，请在headers中设置X-API-Key或在参数中传入api_key")
  
  if not auth_username:
    raise HTTPException(status_code=401, detail="缺少用户名，请在headers中设置X-Username或在参数中传入username")
  
  return auth_api_key, auth_username


@router.post("/jiliang/hand_writing")
async def handwriting_recognize(
    request: Request,
    file: UploadFile = File(None, description="图片文件"),
    image_base64: str = Form(None, description="Base64编码的图片数据"),
    object_type: str = Form("general_handwriting", description="识别类型，固定为general_handwriting"),
    type_param: str = Form("st_ocrapi_all", description="返回类型: st_ocrapi(行结果) 或 st_ocrapi_all(单字结果)"),
    line_probability: bool = Form(False, description="是否返回行置信度"),
    img_direction: bool = Form(False, description="是否开启方向判断"),
    handprint_type: str = Form(None, description="跳过检测，直接单行识别: handrecog_only"),
    eng_granularity: str = Form("word", description="英文粒度: word(单词) 或 letter(字符)"),
    api_key: str = Form(None, description="API Key"),
    username: str = Form(None, description="用户名"),
    parse_result: bool = Form(True, description="是否解析识别结果为友好格式")
):
  """
  手写文字OCR识别接口

  支持两种输入方式：
  1. 上传图片文件 (file 参数)
  2. Base64编码的图片数据 (image_base64 参数)

  """
  temp_file_path = None
  
  try:
    auth_api_key, auth_username = get_auth_from_request(request, api_key, username)
    
    # 处理图片数据
    if file and file.filename:
      # 方式1：文件上传
      file_ext = os.path.splitext(file.filename)[1].lower()
      if file_ext not in ALLOWED_IMAGE_EXTENSIONS:
        raise HTTPException(
          status_code=400,
          detail=f"不支持的图片格式: {file_ext}，支持格式: {ALLOWED_IMAGE_EXTENSIONS}"
        )
      
      # 保存临时文件
      temp_file_path = os.path.join(HANDWRITING_TEMP_DIR, f"handwriting_{uuid4()}_{file.filename}")
      with open(temp_file_path, "wb") as buffer:
        shutil.copyfileobj(file.file, buffer)
      
      with open(temp_file_path, "rb") as f:
        image_data = f.read()
      
      logger.info(f"手写OCR识别 - 文件: {file.filename}, 大小: {len(image_data)} bytes")
    
    elif image_base64:
      # 方式2：Base64数据
      try:
        image_data = base64.b64decode(image_base64)
      except Exception as e:
        raise HTTPException(status_code=400, detail=f"Base64解码失败: {str(e)}")
      
      logger.info(f"手写OCR识别 - Base64数据, 大小: {len(image_data)} bytes")
    
    else:
      raise HTTPException(status_code=400, detail="请提供图片文件或Base64编码的图片数据")
    
    # 检查数据大小（限制10MB）
    if len(image_data) > 10 * 1024 * 1024:
      raise HTTPException(
        status_code=400,
        detail=f"图片数据过大: {len(image_data)} bytes，建议小于10MB"
      )
    
    client = HandwritingClient(
      base_url=HANDWRITING_CONFIG["base_url"],
      access_code=HANDWRITING_CONFIG["access_code"],
      api_key=auth_api_key,
      username=auth_username
    )
    
    result = await client.recognize_handwriting(
      image_data=image_data,
      object_type=object_type,
      type_param=type_param,
      line_probability=line_probability,
      img_direction=img_direction,
      handprint_type=handprint_type,
      eng_granularity=eng_granularity
    )
    
    if result["success"]:
      if parse_result:
        parsed_result = client.parse_recognition_result(result)
        if parsed_result["success"]:
          return {
            "success": True,
            "message": "手写OCR识别成功",
            "lines": parsed_result.get("lines", []),
            "full_text": parsed_result.get("full_text", ""),
            "line_count": parsed_result.get("line_count", 0),
            "image_direction": parsed_result.get("image_direction", 0),
            "raw_data": result["data"] if "data" in result else None
          }
        else:
          return {
            "success": True,
            "message": "手写OCR识别成功，但结果解析失败",
            "data": result["data"],
            "parse_error": parsed_result.get("message", "解析失败")
          }
      else:
        return {
          "success": True,
          "message": "手写OCR识别成功",
          "data": result["data"]
        }
    else:
      return {
        "success": False,
        "message": result.get("message", "手写OCR识别失败"),
        "error": result.get("error"),
        "raw_response": result.get("raw_response")
      }
  
  except HTTPException:
    raise
  except Exception as e:
    logger.error(f"手写OCR识别异常: {e}", exc_info=True)
    return {
      "success": False,
      "message": f"手写OCR识别失败: {str(e)}",
      "error": str(e)
    }
  
  finally:
    # 清理临时文件
    if temp_file_path and os.path.exists(temp_file_path):
      try:
        os.remove(temp_file_path)
      except Exception as e:
        logger.error(f"清理临时文件失败: {e}")


if __name__ == "__main__":
  import uvicorn
  from fastapi import FastAPI
  
  app = FastAPI(
    title="手写OCR识别服务",
    version="1.0.0",
    description="提供手写文字OCR识别，支持中英数字识别，1w类字典"
  )
  app.include_router(router)
  
  uvicorn.run(app, host="0.0.0.0", port=8000)