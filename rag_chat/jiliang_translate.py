import os
from uuid import uuid4
import json
import ocr_processor
import time
import filetype
import asyncio
import fitz
import docx
import shutil
from datetime import datetime
from openai import AsyncOpenAI
from fastapi import File, UploadFile, BackgroundTasks, Query, Request, APIRouter, Form
from pydantic import BaseModel
from typing import Optional
import tempfile
from file_util import md5, upload_file
from logger import logger
from models import R
from ocr_config import get_ocr_config
from ocr_processor import (
  extract_image_from_page_worker,
  parse_and_create_word_document, extract_text_from_docx
)
from pdf_util import is_standard_pdf
import file_util
from file_readerV2 import process_executor, thread_executor, word_to_text_worker_V2
from simplified_stats_service import SimplifiedStatsService, save_wps_record_dm_with_stats

from redis_util import RedisClient
from constant import AI_ASSISTANT_HEADER, AI_ASSISTANT_WARN_URLENCODER
from fastapi.responses import JSONResponse

router = APIRouter()

# 翻译任务过期时间2小时
TRANSLATE_TASK_EXP_TIME = 7200

# 临时目录配置
TEMP_DIR = tempfile.gettempdir()
PUT_IN_DIR = os.path.join(TEMP_DIR, "translate_put_in")
PUT_OUT_DIR = os.path.join(TEMP_DIR, "translate_put_out")
os.makedirs(PUT_IN_DIR, exist_ok=True)
os.makedirs(PUT_OUT_DIR, exist_ok=True)

# 文件上传配置
ALLOWED_MIME_TYPES = [
  'application/pdf',  # PDF
  'application/msword',  # DOC
  'application/vnd.openxmlformats-officedocument.wordprocessingml.document',  # DOCX
  'image/jpeg',  # JPG/JPEG
  'image/png'  # PNG
]

# 并行处理配置
MAX_CONCURRENT_TASKS = os.cpu_count() or 4  # CPU核心数
MAX_CONCURRENT_PAGES = min(MAX_CONCURRENT_TASKS * 2, 16)  # 并行处理页数
CHUNK_SIZE = 2 * 1024 * 1024  # 分块上传大小: 2MB

# 进度状态常量
STATUS_PENDING = "pending"  # 等待处理
STATUS_UPLOADING = "uploading"  # 上传中
STATUS_PROCESSING = "processing"  # 处理中
STATUS_COMPLETED = "completed"  # 完成
STATUS_FAILED = "failed"  # 失败

# Redis表名
REDIS_TABLE = os.getenv('REDIS_TABLE_JILIANG')

# 翻译模型配置
TRANSLATE_MODEL = os.getenv('TRANSLATE_MODEL')
TRANSLATE_AUTH_TOKEN = os.getenv('TRANSLATE_AUTH_TOKEN')
TRANSLATE_BASE_URL = os.getenv('TRANSLATE_BASE_URL')

# Redis客户端
redis_client = RedisClient()

# 翻译客户端
translate_client = AsyncOpenAI(
  api_key='aa',
  base_url=TRANSLATE_BASE_URL,
  default_headers={"Authorization": TRANSLATE_AUTH_TOKEN}
)


class TranslateTaskInfo(BaseModel):
  """翻译任务信息"""
  task_id: str
  status: str
  progress: int
  message: str
  file_name: Optional[str] = None
  file_id: Optional[str] = None
  total_pages: Optional[int] = None
  processed_pages: Optional[int] = None
  estimated_time: Optional[int] = None  # 预计剩余时间（秒）
  created_at: Optional[datetime] = None
  updated_at: Optional[datetime] = None
  exp_time: Optional[int] = None


class ChunkUploadResponse(BaseModel):
  """分块上传响应"""
  task_id: str
  chunk_index: int
  total_chunks: int
  status: str
  message: str


class TaskStatusResponse(BaseModel):
  """任务状态响应"""
  task_id: str
  status: str
  progress: int
  message: str
  file_id: Optional[str] = None
  file_name: Optional[str] = None
  estimated_time: Optional[int] = None
  created_at: Optional[str] = None
  exp_time: Optional[int] = None


def remove_think_tags(text):
  """
  移除文本中所有<think>...</think>标签及其内容

  Args:
      text: 原始文本

  Returns:
      str: 移除标签后的文本
  """
  import re
  
  cleaned_text = re.sub(r'<think>.*?</think>', '', text, flags=re.DOTALL)
  cleaned_text = re.sub(r'<think>.*$', '', cleaned_text, flags=re.DOTALL)
  cleaned_text = cleaned_text.strip()
  cleaned_text = re.sub(r'\n\s*\n\s*\n', '\n\n', cleaned_text)
  
  logger.info(f"移除思考标签: 原始长度={len(text)}, 清理后长度={len(cleaned_text)}")
  
  return cleaned_text


async def sanitize_filename(filename):
  """
  清理文件名，移除空格和特殊字符

  Args:
      filename: 原始文件名

  Returns:
      str: 清理后的文件名
  """
  import re
  # 保留文件扩展名
  name, ext = os.path.splitext(filename)
  # 移除特殊字符，用下划线替换空格
  sanitized_name = re.sub(r'[^\w\-.]', '_', name.replace(' ', '_'))
  
  if not sanitized_name:
    sanitized_name = f"file_{str(int(time.time()))}"
  return sanitized_name + ext


def verify_mime_type(file_path):
  """
  验证文件MIME类型

  Args:
      file_path: 文件路径

  Returns:
      tuple: (是否允许, MIME类型)
  """
  kind = filetype.guess(file_path)
  
  # 如果filetype无法识别
  if not kind:
    # 通过扩展名判断
    ext = os.path.splitext(file_path)[1].lower()
    if ext == '.pdf':
      return True, 'application/pdf'
    elif ext == '.doc':
      return True, 'application/msword'
    elif ext == '.docx':
      return True, 'application/vnd.openxmlformats-officedocument.wordprocessingml.document'
    else:
      return False, 'unknown'
  
  # 检查MIME类型是否在允许列表中
  if kind.mime in ALLOWED_MIME_TYPES:
    return True, kind.mime
  else:
    return False, kind.mime


async def estimate_processing_time(file_path, mime_type):
  """
  估计文件处理时间

  Args:
      file_path: 文件路径
      mime_type: MIME类型

  Returns:
      tuple: (总页数, 预计处理时间(秒))
  """
  BASE_TIME_PER_PAGE = {
    'application/pdf': 5,
    'application/pdf-ocr': 10,
    'application/msword': 5,
    'application/vnd.openxmlformats-officedocument.wordprocessingml.document': 5,
    'image/jpeg': 10,
    'image/png': 10
  }
  
  try:
    total_pages = 1
    
    if mime_type == 'application/pdf':
      # 检查是否需要OCR
      is_standard, _ = await asyncio.get_running_loop().run_in_executor(
        process_executor,
        is_standard_pdf,
        file_path
      )
      
      # 获取PDF页数
      with fitz.open(file_path) as doc:
        total_pages = len(doc)
      
      if is_standard:
        time_per_page = BASE_TIME_PER_PAGE['application/pdf']
      else:
        time_per_page = BASE_TIME_PER_PAGE['application/pdf-ocr']
    
    elif mime_type in ['application/msword', 'application/vnd.openxmlformats-officedocument.wordprocessingml.document']:
      
      if mime_type == 'application/msword':
        # DOC文件基于文件大小的估计
        file_size = os.path.getsize(file_path)
        total_pages = max(1, int(file_size / (30 * 1024)))  # 假设每页约30KB
      else:
        
        try:
          doc = docx.Document(file_path)
          # 估算页数（一页约20段落）
          total_pages = max(1, len(doc.paragraphs) // 20)
        except:
          file_size = os.path.getsize(file_path)
          total_pages = max(1, int(file_size / (50 * 1024)))  # 假设每页约50KB
      
      time_per_page = BASE_TIME_PER_PAGE[mime_type]
    
    elif mime_type in ['image/jpeg', 'image/png']:
      # 图片文件视为单页
      total_pages = 1
      time_per_page = BASE_TIME_PER_PAGE[mime_type]
    
    else:
      total_pages = 1
      time_per_page = 30
    
    estimated_time = (total_pages / min(MAX_CONCURRENT_PAGES, total_pages)) * time_per_page
    estimated_time += 30
    
    return total_pages, int(estimated_time)
  
  except Exception as e:
    logger.error(f"估计处理时间失败: {e}", exc_info=True)
    return 1, 60


LANGUAGE_MAPPING = {
  # 英文键名
  "English": "英语",
  "French": "法语",
  "Spanish": "西班牙语",
  "Portuguese": "葡萄牙语",
  "German": "德语",
  "Italian": "意大利语",
  "Russian": "俄语",
  "Dutch": "荷兰语",
  "Polish": "波兰语",
  "Czech": "捷克语",
  "Chinese": "中文",
  "Japanese": "日语",
  "Korean": "韩语",
  "Vietnamese": "越南语",
  "Thai": "泰语",
  "Indonesian": "印尼语",
  "Malay": "马来语",
  "Lao": "老挝语",
  "Arabic": "阿拉伯语",
  "Persian": "波斯语",
  "Hebrew": "希伯来语",
  "Turkish": "土耳其语",
  "Hindi": "印地语",
  "Urdu": "乌尔都语",
  "Bengali": "孟加拉语",
  "Tamil": "泰米尔语",
  "Swahili": "斯瓦希里语",
  "Finnish": "芬兰语",
  "Norwegian": "挪威语",
  
  # 中文键名
  "英语": "英语",
  "法语": "法语",
  "西班牙语": "西班牙语",
  "葡萄牙语": "葡萄牙语",
  "德语": "德语",
  "意大利语": "意大利语",
  "俄语": "俄语",
  "荷兰语": "荷兰语",
  "波兰语": "波兰语",
  "捷克语": "捷克语",
  "中文": "中文",
  "日语": "日语",
  "韩语": "韩语",
  "越南语": "越南语",
  "泰语": "泰语",
  "印尼语": "印尼语",
  "马来语": "马来语",
  "老挝语": "老挝语",
  "阿拉伯语": "阿拉伯语",
  "波斯语": "波斯语",
  "希伯来语": "希伯来语",
  "土耳其语": "土耳其语",
  "印地语": "印地语",
  "乌尔都语": "乌尔都语",
  "孟加拉语": "孟加拉语",
  "泰米尔语": "泰米尔语",
  "斯瓦希里语": "斯瓦希里语",
  "芬兰语": "芬兰语",
  "挪威语": "挪威语"
}


def normalize_language(lang_input):
  """
  标准化语言输入，支持中英文

  Args:
      lang_input: 用户输入的语言（中文或英文）

  Returns:
      str: 标准化后的中文语言名称，如果是"auto"则返回"auto"
  """
  if not lang_input or lang_input.lower() == "auto":
    return "auto"
  
  # 直接查找映射表
  if lang_input in LANGUAGE_MAPPING:
    return LANGUAGE_MAPPING[lang_input]
  
  # 大小写不敏感匹配
  for key, value in LANGUAGE_MAPPING.items():
    if key.lower() == lang_input.lower():
      return value
  
  # 如果找不到，返回原值（保持向后兼容）
  return lang_input


async def translate_text(text, page_num, source_lang="auto", target_lang="auto", userid='', apikey='', uri='',
                         model=None, max_retries=3, function_type=None, function_name=None):
  """
  调用模型翻译文本，包含重试机制与自动语言检测
  智能识别并保留代码、专业术语等特殊字段

  Args:
      text: 待翻译文本
      page_num: 页码
      source_lang: 源语言
      target_lang: 目标语言
      userid: 用户ID
      apikey: API密钥
      uri: 请求URI
      model: 翻译模型
      max_retries: 最大重试次数
      function_type: 功能类型英文代码
      function_name: 功能名称中文
  """
  qa_id = str(uuid4())
  extra_headers = {'Qa-Id': qa_id, 'X-API-Key': apikey, 'X-Username': userid, 'X-Uri': uri}
  
  text_sample = text[:100].replace('\n', ' ').strip()
  logger.info(f"待翻译文本样本(页{page_num}): {text_sample}")
  
  # 标准化语言输入
  normalized_target_lang = normalize_language(target_lang)
  
  system_prompt = ""
  if normalized_target_lang == "auto":
    system_prompt = """你是一位精通多语言的翻译专家。请先识别以下文本的语言。
1. 如果识别为中文，请将其翻译成英文
2. 如果识别为其他任何语言(如英文、西班牙语、法语等)，请将其翻译成中文

**重要提醒：如果文本中包含多种语言混合的情况，请将所有内容都统一翻译为目标语言。不要保留原语言片段。**

请严格按照以下格式输出：
第一行：[检测到语言: X语]
第二行开始：翻译结果

这是第{page_num}页的内容。"""
  else:
    system_prompt = f"""你是一位精通多语言的专业翻译专家。请将以下文本翻译成{normalized_target_lang}。

**核心翻译原则：**

**需要翻译的内容**（翻译成{normalized_target_lang}）：
- 所有自然语言文本、描述性内容
- 标题、段落、说明文字
- 步骤说明、操作指导
- 概念解释、理论描述
- 用户界面文字和提示信息
- 表格中的描述性内容
- 注释说明（除代码注释外）

**必须保留原文的内容**（不要翻译）：
1. **代码块**：所有用```、`包围的代码，或明显的编程代码
2. **技术标识符**：
   - 编程语言关键字、函数名、类名、变量名
   - API接口名称、方法名
   - 数据库字段名、表名
   - 配置文件参数名
3. **文件系统相关**：
   - 文件名、文件路径、文件扩展名
   - 目录路径、URL地址
4. **标记语言**：
   - HTML/XML标签和属性
   - 标记语言的命名空间
5. **技术规范**：
   - 协议名称（HTTP、HTTPS、FTP等）
   - 编码格式（UTF-8、ASCII等）
   - 数据格式（JSON、XML、CSV等）
6. **专有名词**：
   - 软件名称、品牌名称、产品名称
   - 技术框架名称
   - 专业术语的英文缩写（API、SDK、IDE等）
7. **数值和单位**：
   - 数字、时间、日期格式
   - 度量单位、货币符号
8. **特殊格式**：
   - 正则表达式
   - 命令行指令
   - 键盘快捷键组合

**翻译质量要求：**
- 保持原文的格式结构（换行、缩进、符号等）
- 确保翻译自然流畅，符合{normalized_target_lang}的表达习惯
- 保持专业术语的准确性和一致性
- 对于技术文档，保持逻辑清晰、用词准确

**输出格式要求：**
第一行：[检测到语言: X语]
第二行开始：翻译结果

**重要提醒：**
- 对于混合内容，只翻译自然语言部分，技术术语和代码保持原样
- 如果不确定某个词汇是否应该翻译，倾向于保留原文（特别是技术术语）
- 确保翻译完整，不要遗漏任何需要翻译的内容

现在请翻译第{page_num}页的内容："""
  
  logger.info(
    f"翻译任务 - 页码: {page_num}, 使用模型: {TRANSLATE_MODEL}, 源语言: {source_lang}, 目标语言: {target_lang}({normalized_target_lang}), 基础URL: {TRANSLATE_BASE_URL}")
  logger.info(f"翻译任务 - 请求头: {extra_headers}")
  
  history = [{
    "role": "system",
    "content": system_prompt.format(page_num=page_num)
  }, {"role": "user", "content": text}]
  
  for retry in range(max_retries):
    try:
      logger.info(f"开始翻译第{page_num}页 - 尝试 {retry + 1}/{max_retries}")
      
      start_time = time.time()
      
      # 使用传入的model，如果未指定则使用默认值
      llm_model = model if model else TRANSLATE_MODEL
      
      response = await translate_client.chat.completions.create(
        model=llm_model,
        messages=history,
        temperature=0.3,
        extra_headers=extra_headers
      )
      
      end_time = time.time()
      duration = end_time - start_time
      
      translated_text = response.choices[0].message.content
      
      # 记录原始输出以观察语言检测结果
      logger.info(f"模型原始输出(前200字符): {translated_text[:200]}")
      
      import re
      lang_match = re.search(r'\[检测到语言:\s*(.+?)\]', translated_text)
      detected_language = lang_match.group(1) if lang_match else "未识别"
      logger.info(f"第{page_num}页文本检测到的语言: {detected_language}")
      
      # 移除语言检测行和思考标签
      translated_text = re.sub(r'\[检测到语言:.+?\]\n?', '', translated_text, 1)
      translated_text = remove_think_tags(translated_text)
      
      logger.info(
        f"第{page_num}页翻译完成 - 检测语言: {detected_language}, 目标语言: {normalized_target_lang}, 耗时: {duration:.2f}秒, 翻译前长度: {len(text)}, 翻译后长度: {len(translated_text)}")
      
      return translated_text
    
    except Exception as e:
      logger.warning(f"翻译第{page_num}页时出错(尝试 {retry + 1}/{max_retries}): {str(e)}")
      logger.error(f"详细错误信息: ", exc_info=True)
      
      if retry == max_retries - 1:
        logger.error(f"翻译第{page_num}页失败，最大重试次数已达到", exc_info=True)
        
        return f"[翻译失败: {str(e)}]"


async def process_page_with_ocr_and_translate(file_path, page_num, total_pages, temp_dir, ocr_client, track_id, params,
                                              userid, apikey, uri, progress_key, source_lang="auto", target_lang="auto",
                                              model=None):
  """
  处理单页PDF并立即翻译，实现边OCR边翻译

  Args:
      file_path: PDF文件路径
      page_num: 页码
      total_pages: 总页数
      temp_dir: 临时目录
      ocr_client: OCR客户端
      track_id: OCR跟踪ID
      params: OCR参数
      userid: 用户ID
      apikey: API密钥
      uri: 请求URI
      progress_key: Redis进度键
      source_lang: 源语言
      target_lang: 目标语言
      model: 翻译模型

  Returns:
      tuple: (翻译后的文本, 是否成功)
  """
  try:
    loop = asyncio.get_running_loop()
    
    # 更新当前页处理状态
    sub_progress = 5 + (70 * page_num) // total_pages
    await update_progress(progress_key, sub_progress, f"正在处理第{page_num + 1}/{total_pages}页")
    
    # 提取图像
    output_path = os.path.join(temp_dir, f"page_{page_num}.png")
    logger.info(f"开始提取PDF第{page_num + 1}/{total_pages}页图像")
    
    image_path = await loop.run_in_executor(
      thread_executor,
      extract_image_from_page_worker,
      file_path, page_num, output_path
    )
    
    if not image_path:
      logger.warning(f"PDF第{page_num + 1}页图像提取失败")
      return f"【第{page_num + 1}页图像提取失败】", False
    
    logger.info(f"PDF第{page_num + 1}/{total_pages}页图像提取完成: {image_path}")
    
    # 上传图像到OCR服务
    logger.info(f"开始上传PDF第{page_num + 1}页图像到OCR服务: {image_path}")
    result = await loop.run_in_executor(
      thread_executor,
      ocr_client.upload_image,
      track_id,
      image_path,
      '',
      params
    )
    logger.info(f"PDF第{page_num + 1}页图像上传OCR服务成功")
    
    page_docx_path = os.path.join(temp_dir, f"ocr_result_page_{page_num}.docx")
    
    # 解析OCR结果并创建Word文档
    logger.info(f"开始解析第{page_num + 1}页OCR结果并创建Word文档")
    parse_result = await loop.run_in_executor(
      thread_executor,
      parse_and_create_word_document,
      [result],
      page_docx_path
    )
    logger.info(f"第{page_num + 1}页OCR结果解析完成，状态码: {parse_result['code']}")
    
    if parse_result["code"] != 200:
      logger.error(f"第{page_num + 1}页OCR创建Word文档失败: {parse_result['message']}")
      return f"【第{page_num + 1}页OCR处理失败】", False
    
    # 从Word文档中提取文本
    logger.info(f"开始从第{page_num + 1}页Word文档中提取文本: {page_docx_path}")
    page_text = await loop.run_in_executor(
      thread_executor,
      extract_text_from_docx,
      page_docx_path
    )
    
    # 添加页码信息
    page_text_with_number = f"第{page_num + 1}页\n{page_text}"
    logger.info(f"第{page_num + 1}页OCR文本提取完成，长度: {len(page_text)}")
    
    # 更新进度
    sub_progress = 75 + (15 * page_num) // total_pages
    await update_progress(progress_key, sub_progress, f"正在翻译第{page_num + 1}/{total_pages}页")
    
    logger.info(f"开始翻译第{page_num + 1}页文本，源语言: {source_lang}, 目标语言: {target_lang}")
    translated_text = await translate_text(
      page_text_with_number,
      page_num + 1,
      source_lang,
      target_lang,
      userid,
      apikey,
      uri,
      model
    )
    logger.info(f"第{page_num + 1}页翻译完成，翻译后长度: {len(translated_text)}")
    
    # 更新完成进度
    sub_progress = 90 + (10 * page_num) // total_pages
    await update_progress(progress_key, sub_progress, f"第{page_num + 1}/{total_pages}页处理完成")
    
    return translated_text, True
  
  except Exception as e:
    logger.error(f"处理PDF第{page_num + 1}页时出错: {e}", exc_info=True)
    return f"【第{page_num + 1}页处理失败: {str(e)}】", False


async def update_progress(progress_key, progress, message, status=STATUS_PROCESSING, estimated_time=None,
                          additional_data=None):
  """
  更新任务进度信息

  Args:
      progress_key: Redis键
      progress: 进度百分比(0-100)
      message: 进度消息
      status: 任务状态
      estimated_time: 预计剩余时间(秒)
      additional_data: 附加数据字典
  """
  try:
    progress_data = {
      'progress': progress,
      'status': status,
      'message': message,
      'updated_at': datetime.now().strftime('%Y-%m-%d %H:%M:%S')
    }
    
    if estimated_time is not None:
      progress_data['estimated_time'] = estimated_time
    
    if additional_data and isinstance(additional_data, dict):
      progress_data.update(additional_data)
    
    # 异步更新Redis
    loop = asyncio.get_running_loop()
    await loop.run_in_executor(
      thread_executor,
      redis_client.setex,
      progress_key,
      TRANSLATE_TASK_EXP_TIME,
      json.dumps(progress_data)
    )
  except Exception as e:
    logger.error(f"更新进度失败: {e}", exc_info=True)


def extract_text_from_pdf(file_path):
  """
  从标准PDF中提取文本的独立函数

  Args:
      file_path: PDF文件路径

  Returns:
      tuple: (文本, 处理页数, 总页数)
  """
  return pdf_to_text_standard(file_path)


async def handle_pdf_translation(file_path, file_info, progress_key, request_headers):
  """
  处理PDF文件翻译

  Args:
      file_path: 文件路径
      file_info: 文件信息字典
      progress_key: Redis进度键
      request_headers: 请求头信息

  Returns:
      tuple: (翻译后的文本列表, 处理成功页数, 总页数)
  """
  userid = request_headers.get('x-username', '')
  apikey = request_headers.get('x-api-key', '')
  uri = request_headers.get('x-uri', '/jiliang/translate')
  
  source_lang = file_info.get('source_lang', 'auto')
  target_lang = file_info.get('target_lang', 'auto')
  function_type = file_info.get('function_type')
  function_name = file_info.get('function_name')
  
  await update_progress(progress_key, 5, "正在分析PDF文件类型")
  is_standard, reason = await asyncio.get_running_loop().run_in_executor(
    process_executor,
    is_standard_pdf,
    file_path
  )
  logger.info(f"PDF类型检测: {reason}")
  
  # 获取总页数
  with fitz.open(file_path) as doc:
    total_pages = len(doc)
  
  file_info['total_pages'] = total_pages
  
  if is_standard:
    
    await update_progress(progress_key, 10, f"PDF类型: {reason}，共{total_pages}页，开始提取文本",
                          additional_data={'total_pages': total_pages})
    
    text, processed_pages, _ = await asyncio.get_running_loop().run_in_executor(
      process_executor,
      extract_text_from_pdf,
      file_path
    )
    
    logger.info(f"PDF文本提取完成: 共{total_pages}页, 成功处理{processed_pages}页, 文本长度: {len(text)}")
    
    # 以页为单位进行翻译
    text_by_page = text.split('第')[1:]  # 分割出每页内容
    text_by_page = ['第' + page for page in text_by_page if page.strip()]  # 恢复"第"字
    logger.info(f"PDF分割为{len(text_by_page)}页进行翻译")
    
    translated_text_by_page = []
    
    semaphore = asyncio.Semaphore(min(MAX_CONCURRENT_PAGES, len(text_by_page)))
    
    async def translate_page_with_semaphore(page_num, page_text):
      async with semaphore:
        # 更新翻译进度
        sub_progress = 10 + (80 * page_num) // len(text_by_page)
        await update_progress(progress_key, sub_progress,
                              f"正在翻译第{page_num + 1}/{len(text_by_page)}页",
                              additional_data={'processed_pages': page_num})
        
        model = file_info.get('model')
        
        translated = await translate_text(page_text, page_num + 1, source_lang, target_lang, userid, apikey, uri, model, 3, function_type, function_name)
        
        return page_num, translated
    
    tasks = [
      translate_page_with_semaphore(i, page_text)
      for i, page_text in enumerate(text_by_page)
    ]
    
    # 等待所有翻译任务完成
    results = await asyncio.gather(*tasks, return_exceptions=True)
    
    translated_pages = [None] * len(text_by_page)
    for result in results:
      if isinstance(result, Exception):
        logger.error(f"翻译任务异常: {result}")
        continue
      
      page_num, translated_text = result
      translated_pages[page_num] = translated_text
    
    # 过滤掉None值
    translated_text_by_page = [page for page in translated_pages if page is not None]
    
    return translated_text_by_page, len(translated_text_by_page), total_pages
  else:
    
    await update_progress(progress_key, 10, f"PDF类型: {reason}，需要OCR处理，共{total_pages}页",
                          additional_data={'total_pages': total_pages})
    
    temp_dir = tempfile.mkdtemp(prefix="ocr_temp_")
    
    try:
      
      ocr_config = get_ocr_config()
      client = ocr_processor.APIClient(ocr_config["base_url"], ocr_config["auth_token"])
      track_id = ocr_config["track_id"]
      params = ocr_config["params"]
      
      translated_pages = [None] * total_pages  # 预分配结果列表
      processed_pages = 0
      
      logger.info(f"开始并行处理PDF文件的OCR和翻译: {file_path}, 总页数: {total_pages}")
      
      semaphore = asyncio.Semaphore(min(MAX_CONCURRENT_PAGES, total_pages))
      
      async def process_page_with_semaphore(page_num):
        async with semaphore:
          return await process_page_with_ocr_and_translate(
            file_path, page_num, total_pages, temp_dir, client, track_id, params,
            userid, apikey, uri, progress_key, source_lang, target_lang, file_info.get('model')
          )
      
      tasks = [process_page_with_semaphore(i) for i in range(total_pages)]
      
      results = await asyncio.gather(*tasks, return_exceptions=True)
      
      for i, result in enumerate(results):
        if isinstance(result, Exception):
          logger.error(f"第{i + 1}页处理异常: {result}")
          translated_pages[i] = f"【第{i + 1}页处理异常: {str(result)}】"
        else:
          translated_text, success = result
          translated_pages[i] = translated_text
          if success:
            processed_pages += 1
        
        # 更新进度信息
        await update_progress(
          progress_key,
          min(90, 10 + 80 * (i + 1) // total_pages),
          f"已处理 {i + 1}/{total_pages} 页",
          additional_data={'processed_pages': i + 1}
        )
      
      # 过滤掉None值
      translated_pages = [page for page in translated_pages if page is not None]
      
      logger.info(f"PDF并行OCR和翻译处理完成，成功处理页数: {processed_pages}/{total_pages}")
      
      return translated_pages, processed_pages, total_pages
    
    finally:
      # 清理临时目录
      if temp_dir and os.path.exists(temp_dir):
        try:
          shutil.rmtree(temp_dir)
          logger.info(f"OCR临时目录清理完成: {temp_dir}")
        except Exception as e:
          logger.error(f"清理OCR临时目录失败: {e}")


def word_processing_task_global(file_path):
  """全局可访问的Word处理函数，可被pickle序列化"""
  return word_to_text_worker_V2(file_path, float('inf'))


async def handle_word_translation(file_path, file_info, progress_key, request_headers):
  """
  处理Word文档翻译

  Args:
      file_path: 文件路径
      file_info: 文件信息字典
      progress_key: Redis进度键
      request_headers: 请求头信息

  Returns:
      tuple: (翻译后的文本列表, 处理成功页数, 总页数)
  """
  
  userid = request_headers.get('x-username', '')
  apikey = request_headers.get('x-api-key', '')
  uri = request_headers.get('x-uri', '/jiliang/translate')
  
  source_lang = file_info.get('source_lang', 'auto')
  target_lang = file_info.get('target_lang', 'auto')
  function_type = file_info.get('function_type')
  function_name = file_info.get('function_name')
  
  await update_progress(progress_key, 5, "正在分析Word文档")
  
  logger.info(f"处理Word文档: {file_info['file_name']}")
  
  # 使用全局函数，而不是定义本地函数
  text, page_count, total_pages = await asyncio.get_running_loop().run_in_executor(
    process_executor, word_processing_task_global, file_path)
  
  logger.info(f"Word文档解析完成: 共{total_pages}页, 文本长度: {len(text)}")
  file_info['total_pages'] = total_pages
  
  await update_progress(progress_key, 15, f"Word文档解析完成，共{total_pages}页，开始翻译",
                        additional_data={'total_pages': total_pages})
  
  # 以页为单位进行翻译
  text_by_page = text.split('第')[1:]  # 分割出每页内容
  text_by_page = ['第' + page for page in text_by_page if page.strip()]  # 恢复"第"字
  logger.info(f"Word文档分割为{len(text_by_page)}页进行翻译")
  
  translated_text_by_page = []
  
  semaphore = asyncio.Semaphore(min(MAX_CONCURRENT_PAGES, len(text_by_page)))
  
  async def translate_page_with_semaphore(page_num, page_text):
    async with semaphore:
      # 更新翻译进度
      sub_progress = 15 + (75 * page_num) // len(text_by_page)
      await update_progress(progress_key, sub_progress,
                            f"正在翻译第{page_num + 1}/{len(text_by_page)}页",
                            additional_data={'processed_pages': page_num})
      
      model = file_info.get('model')
      
      translated = await translate_text(page_text, page_num + 1, source_lang, target_lang, userid, apikey, uri, model, 3, function_type, function_name)
      
      return page_num, translated
  
  tasks = [
    translate_page_with_semaphore(i, page_text)
    for i, page_text in enumerate(text_by_page)
  ]
  
  results = await asyncio.gather(*tasks, return_exceptions=True)
  
  translated_pages = [None] * len(text_by_page)
  for result in results:
    if isinstance(result, Exception):
      logger.error(f"翻译任务异常: {result}")
      continue
    
    page_num, translated_text = result
    translated_pages[page_num] = translated_text
  
  # 过滤掉None值
  translated_text_by_page = [page for page in translated_pages if page is not None]
  
  return translated_text_by_page, len(translated_text_by_page), total_pages


def pdf_to_text_standard(file_path, max_size=float('inf')):
  """
  从标准PDF中提取文本

  Args:
      file_path: PDF文件路径
      max_size: 最大文本大小

  Returns:
      tuple: (文本, 处理页数, 总页数)
  """
  with fitz.open(file_path) as doc:
    total_pages = len(doc)
    text = ""
    processed_pages = 0
    
    for idx in range(total_pages):
      try:
        page = doc.load_page(idx)
        now_text = page.get_text().replace(' ', '')
        
        if len(text) + len(now_text) > max_size:
          break
        
        text += f'第{idx + 1}页\n{now_text}\n'
        processed_pages += 1
      except Exception as e:
        logger.error(f"处理PDF第{idx + 1}页时出错: {e}")
  
  return text, processed_pages, total_pages


async def create_translated_document(translated_text_by_page, original_file_info, temp_dir):
  """
  创建翻译后的文档

  Args:
      translated_text_by_page: 翻译后的文本列表
      original_file_info: 原始文件信息
      temp_dir: 临时目录

  Returns:
      str: 输出文件路径
  """
  
  original_name = original_file_info.get('file_name', 'document')
  original_ext = os.path.splitext(original_name)[1].lower()
  
  output_filename = os.path.splitext(original_name)[0] + "_翻译.docx"
  output_path = os.path.join(temp_dir, output_filename)
  
  doc = docx.Document()
  
  doc.add_heading(f"{os.path.splitext(original_name)[0]} - 翻译文档", level=0)
  
  for i, translated_content in enumerate(translated_text_by_page):
    
    doc.add_heading(f"第{i + 1}页", level=1)
    
    # 添加翻译内容
    paragraphs = translated_content.split('\n')
    for para in paragraphs:
      if para.strip():
        doc.add_paragraph(para)
    
    # 添加页面分隔符
    if i < len(translated_text_by_page) - 1:
      doc.add_page_break()
  
  doc.save(output_path)
  
  return output_path, output_filename


async def upload_translated_file(file_path, file_name, headers):
  """
  上传翻译后的文件到文件服务
  """
  try:
    # 读取文件内容
    with open(file_path, "rb") as f:
      file_bytes = f.read()
    
    # 计算文件MD5
    file_md5 = md5(file_name + str(time.time()))
    
    upload_headers = {}
    
    # 构建上传请求头
    if 'X-API-Key' in headers:
      upload_headers['X-API-Key'] = headers['X-API-Key']
    if 'x-api-key' in headers:
      upload_headers['X-API-Key'] = headers['x-api-key']
    if 'X-Username' in headers:
      upload_headers['X-Username'] = headers['X-Username']
    if 'x-username' in headers:
      upload_headers['X-Username'] = headers['x-username']
    
    if 'Authorization' in headers:
      upload_headers['Authorization'] = headers['Authorization']
    if 'authorization' in headers:
      upload_headers['Authorization'] = headers['authorization']
    
    if TRANSLATE_AUTH_TOKEN and 'Authorization' not in upload_headers:
      upload_headers['Authorization'] = TRANSLATE_AUTH_TOKEN
    
    if 'Token' in headers:
      upload_headers['Token'] = headers['Token']
    if 'token' in headers:
      upload_headers['Token'] = headers['token']
    
    logger.info(f"上传翻译文件: {file_name}, 大小: {len(file_bytes)} bytes")
    logger.info(f"上传headers: {upload_headers}")
    
    file_id = await file_util.upload_file(
      file_bytes, file_md5, file_name, "translations", upload_headers
    )
    
    logger.info(f"文件上传成功，获取到文件ID: {file_id}")
    
    return file_id
  
  except Exception as e:
    logger.error(f"上传翻译文件失败: {e}", exc_info=True)
    # 提供更详细的错误信息
    if '"msg"' in str(e):
      logger.error(f"可能是文件服务响应格式异常，原始错误: {e}")
      raise Exception(f"文件上传服务响应异常: {str(e)}")
    elif 'json' in str(e).lower():
      logger.error(f"JSON解析错误，可能是文件服务返回了非JSON响应")
      raise Exception(f"文件服务响应格式错误: {str(e)}")
    else:
      raise Exception(f"文件上传失败: {str(e)}")


async def handle_image_translation(file_path, file_info, progress_key, request_headers):
  """
  处理图片文件翻译

  Args:
      file_path: 文件路径
      file_info: 文件信息字典
      progress_key: Redis进度键
      request_headers: 请求头信息

  Returns:
      tuple: (翻译后的文本列表, 处理成功页数, 总页数)
  """
  
  userid = request_headers.get('x-username', '')
  apikey = request_headers.get('x-api-key', '')
  uri = request_headers.get('x-uri', '/jiliang/translate')
  
  source_lang = file_info.get('source_lang', 'auto')
  target_lang = file_info.get('target_lang', 'auto')
  function_type = file_info.get('function_type')
  function_name = file_info.get('function_name')
  
  temp_dir = tempfile.mkdtemp(prefix="image_ocr_temp_")
  
  try:
    await update_progress(progress_key, 5, "正在处理图片，准备OCR识别")
    
    # 获取OCR配置
    ocr_config = get_ocr_config()
    client = ocr_processor.APIClient(ocr_config["base_url"], ocr_config["auth_token"])
    track_id = ocr_config["track_id"]
    params = ocr_config["params"]
    
    # 设置为单页处理
    total_pages = 1
    file_info['total_pages'] = total_pages
    
    await update_progress(progress_key, 10, "开始进行图片OCR识别",
                          additional_data={'total_pages': total_pages})
    
    # 上传图像到OCR服务
    logger.info(f"开始上传图片到OCR服务: {file_path}")
    loop = asyncio.get_running_loop()
    result = await loop.run_in_executor(
      thread_executor,
      client.upload_image,
      track_id,
      file_path,
      '',
      params
    )
    logger.info("图片上传OCR服务成功")
    
    page_docx_path = os.path.join(temp_dir, "ocr_result.docx")
    
    logger.info("开始解析OCR结果并创建Word文档")
    parse_result = await loop.run_in_executor(
      thread_executor,
      parse_and_create_word_document,
      [result],
      page_docx_path
    )
    logger.info(f"OCR结果解析完成，状态码: {parse_result['code']}")
    
    if parse_result["code"] != 200:
      logger.error(f"OCR创建Word文档失败: {parse_result['message']}")
      return ["OCR处理失败"], 0, 1
    
    logger.info(f"开始从Word文档中提取文本: {page_docx_path}")
    page_text = await loop.run_in_executor(
      thread_executor,
      extract_text_from_docx,
      page_docx_path
    )
    
    # 添加页码信息
    page_text_with_number = f"第1页\n{page_text}"
    logger.info(f"OCR文本提取完成，长度: {len(page_text)}")
    
    # 更新进度
    await update_progress(progress_key, 60, "OCR识别完成，开始翻译文本")
    
    model = file_info.get('model')
    
    # 进行翻译
    logger.info("开始翻译文本")
    translated_text = await translate_text(page_text_with_number, 1, source_lang, target_lang, userid, apikey, uri,
                                           model, 3, function_type, function_name)
    logger.info(f"翻译完成，翻译后长度: {len(translated_text)}")
    
    # 更新完成进度
    await update_progress(progress_key, 90, "文本翻译完成")
    
    return [translated_text], 1, 1
  
  except Exception as e:
    logger.error(f"处理图片时出错: {e}", exc_info=True)
    return [f"【图片处理失败: {str(e)}】"], 0, 1
  
  finally:
    # 清理临时目录
    if temp_dir and os.path.exists(temp_dir):
      try:
        shutil.rmtree(temp_dir)
        logger.info(f"OCR临时目录清理完成: {temp_dir}")
      except Exception as e:
        logger.error(f"清理OCR临时目录失败: {e}")


# ********** 主要处理函数 **********
async def process_translation_task(task_id, file_info, request_headers):
  """
  处理翻译任务的主函数
  """
  temp_dir = None
  start_time = time.time()
  request_start_time = datetime.now()  # 添加统计开始时间
  progress_key = f"{REDIS_TABLE}:translate:progress:{task_id}"
  file_id = None
  
  # 提取统计相关参数
  userid = request_headers.get('x-username', '')
  apikey = request_headers.get('x-api-key', '')
  uri = request_headers.get('x-uri', '/jiliang/translate')
  function_type = file_info.get('function_type')
  function_name = file_info.get('function_name')
  model = file_info.get('model', TRANSLATE_MODEL)
  
  try:
    temp_dir = tempfile.mkdtemp(prefix="translate_temp_")
    
    file_path = file_info['file_path']
    mime_type = file_info['mime_type']
    
    # 原有的处理逻辑保持不变
    if mime_type == 'application/pdf':
      translated_text_by_page, processed_pages, total_pages = await handle_pdf_translation(
        file_path, file_info, progress_key, request_headers
      )
    elif mime_type in ['application/msword', 'application/vnd.openxmlformats-officedocument.wordprocessingml.document']:
      translated_text_by_page, processed_pages, total_pages = await handle_word_translation(
        file_path, file_info, progress_key, request_headers
      )
    elif mime_type in ['image/jpeg', 'image/png']:
      translated_text_by_page, processed_pages, total_pages = await handle_image_translation(
        file_path, file_info, progress_key, request_headers
      )
    else:
      raise ValueError(f"不支持的文件类型: {mime_type}")
    
    # 更新进度为90%
    await update_progress(progress_key, 90, "翻译完成，正在创建翻译文档",
                          additional_data={
                            'processed_pages': processed_pages,
                            'total_pages': total_pages
                          })
    
    # 创建翻译后的文档
    output_path, output_filename = await create_translated_document(
      translated_text_by_page, file_info, temp_dir
    )
    
    # 更新进度为95%
    await update_progress(progress_key, 95, "正在上传翻译文档")
    
    # 上传文件
    try:
      file_id = await upload_translated_file(output_path, output_filename, request_headers)
      
      logger.info(f"翻译任务完成，文件ID: {file_id}, 文件名: {output_filename}")
      
      # 计算总处理时间
      total_time = time.time() - start_time
      response_end_time = datetime.now()
      duration_ms = int(total_time * 1000)
      
      # 保存成功统计记录
      if function_type:
        success_message = f"翻译完成，处理页数: {processed_pages}/{total_pages}"
        save_success = save_wps_record_dm_with_stats(
          f"文件翻译: {file_info.get('file_name', '')}", success_message, '',
          userid, apikey, task_id, function_type, function_name,
          request_start_time, response_end_time, duration_ms, 'SUCCESS',
          model, 0.3
        )
        
        if save_success:
          logger.info(f"翻译统计记录保存成功 - Function: {function_type}, Duration: {duration_ms}ms")
        else:
          logger.warning(f"翻译统计记录保存失败 - Function: {function_type}")
      
      # 更新最终进度
      await update_progress(
        progress_key, 100, "翻译完成", STATUS_COMPLETED,
        additional_data={
          'file_id': file_id,
          'file_name': output_filename,
          'processed_pages': processed_pages,
          'total_pages': total_pages,
          'process_time': f"{total_time:.2f}秒",
          'exp_time': int(time.time() + TRANSLATE_TASK_EXP_TIME)
        }
      )
    
    except Exception as e:
      logger.error(f"上传翻译文件失败: {str(e)}")
      
      # 计算总处理时间
      total_time = time.time() - start_time
      response_end_time = datetime.now()
      duration_ms = int(total_time * 1000)
      
      # 保存失败统计记录（文件上传失败）
      if function_type:
        save_wps_record_dm_with_stats(
          f"文件翻译: {file_info.get('file_name', '')}", '', f"文件上传失败: {str(e)}",
          userid, apikey, task_id, function_type, function_name,
          request_start_time, response_end_time, duration_ms, 'ERROR',
          model, 0.3
        )
      
      # 文件上传失败时状态改为failed
      await update_progress(
        progress_key, 100, f"翻译已完成，但文件上传失败: {str(e)}", STATUS_FAILED,
        additional_data={
          'error': str(e),
          'file_name': output_filename,
          'processed_pages': processed_pages,
          'total_pages': total_pages,
          'process_time': f"{total_time:.2f}秒",
          'exp_time': int(time.time() + TRANSLATE_TASK_EXP_TIME)
        }
      )
    
    return {
      'task_id': task_id,
      'file_id': file_id,
      'file_name': output_filename,
      'pages': processed_pages,
      'total_pages': total_pages,
      'process_time': f"{time.time() - start_time:.2f}秒"
    }
  
  except Exception as e:
    total_time = time.time() - start_time
    response_end_time = datetime.now()
    duration_ms = int(total_time * 1000)
    
    logger.error(f"文件翻译失败(耗时{total_time:.2f}秒): {e}", exc_info=True)
    
    # 保存失败统计记录
    if function_type:
      save_wps_record_dm_with_stats(
        f"文件翻译: {file_info.get('file_name', '')}", '', str(e),
        userid, apikey, task_id, function_type, function_name,
        request_start_time, response_end_time, duration_ms, 'ERROR',
        model, 0.3
      )
    
    # 更新失败状态
    await update_progress(
      progress_key, 100, f"翻译失败: {str(e)}", STATUS_FAILED,
      additional_data={'error': str(e)}
    )
    
    return None
  
  finally:
    # 清理临时目录和原始文件的逻辑保持不变
    if temp_dir and os.path.exists(temp_dir):
      try:
        shutil.rmtree(temp_dir)
        logger.info(f"临时目录清理完成: {temp_dir}")
      except Exception as e:
        logger.error(f"清理临时目录失败: {e}")
    
    try:
      if 'file_path' in file_info and os.path.exists(file_info['file_path']):
        os.remove(file_info['file_path'])
        logger.info(f"清理原始文件: {file_info['file_path']}")
    except Exception as e:
      logger.error(f"清理原始文件失败: {e}")


@router.post("/jiliang/translate")
async def translate_file_v2(request: Request, background_tasks: BackgroundTasks, file: UploadFile = File(...),
                            model: str = Query(None, description="模型名称，不指定时使用默认模型"),
                            source_lang: str = Query("auto", description="源语言，默认为自动检测"),
                            target_lang: str = Query("auto",
                                                     description="目标语言，默认为自动(中文内容译为英文，其他语言译为中文)"),
                            function_type: str = Query(None, description="功能类型英文代码"),
                            function_name: str = Query(None, description="功能名称中文")):
  """
  异步文件翻译接口

  Args:
      request: FastAPI请求对象
      file: 上传的文件
      background_tasks: 后台任务

  Returns:
      任务ID及状态信息
  """
  start_time = time.time()
  
  try:
    
    headers = dict(request.headers)
    
    # 如果提供了功能类型但没有功能名称，自动查询
    if function_type and not function_name:
      func_info = SimplifiedStatsService._get_function_info(function_type)
      if func_info:
        function_name = func_info['function_name']
        logger.info(f"自动查询功能名称: {function_type} -> {function_name}")
    
    logger.info(f"翻译请求 - Function: {function_type}, FunctionName: {function_name}")
    
    task_id = str(uuid4())
    progress_key = f"{REDIS_TABLE}:translate:progress:{task_id}"
    
    response_headers = {
      "Task-Id": task_id,
      AI_ASSISTANT_HEADER: AI_ASSISTANT_WARN_URLENCODER
    }
    
    logger.info(f"文件翻译请求: 文件名={file.filename}, 任务ID={task_id}")
    
    # 清理文件名
    original_filename = file.filename
    sanitized_filename = await sanitize_filename(original_filename)
    
    if sanitized_filename != original_filename:
      logger.info(f"文件名已清理: {original_filename} -> {sanitized_filename}")
      file.filename = sanitized_filename
    
    # 保存上传文件
    file_location = os.path.join(PUT_IN_DIR, f"{task_id}_{file.filename}")
    with open(file_location, "wb") as buffer:
      shutil.copyfileobj(file.file, buffer)
    
    await update_progress(
      progress_key, 0, "文件已上传，正在验证", STATUS_PROCESSING,
      additional_data={
        'file_name': file.filename,
        'created_at': datetime.now().strftime('%Y-%m-%d %H:%M:%S'),
        'exp_time': int(time.time() + TRANSLATE_TASK_EXP_TIME)
      }
    )
    
    is_allowed, mime_type = verify_mime_type(file_location)
    
    if not is_allowed:
      logger.error(f"不支持的文件类型: {mime_type}, 文件: {file.filename}")
      
      os.remove(file_location)
      
      await update_progress(
        progress_key, 100, f"不支持的文件类型: {mime_type}，仅支持PDF和Word文档",
        STATUS_FAILED
      )
      
      return R.error('400', f'不支持的文件类型: {mime_type}，仅支持PDF和Word文档')
    
    # 估计处理时间
    total_pages, estimated_time = await estimate_processing_time(file_location, mime_type)
    
    # 标准化语言参数
    normalized_source_lang = normalize_language(source_lang)
    normalized_target_lang = normalize_language(target_lang)
    
    file_info = {
      'file_path': file_location,
      'file_name': file.filename,
      'mime_type': mime_type,
      'file_size': os.path.getsize(file_location),
      'total_pages': total_pages,
      'source_lang': normalized_source_lang,
      'target_lang': normalized_target_lang,
      'model': model
    }
    
    # 更新状态为处理中，包含预计时间
    await update_progress(
      progress_key, 2, "文件验证成功，开始处理翻译", STATUS_PROCESSING,
      estimated_time=estimated_time,
      additional_data={
        'total_pages': total_pages,
        'mime_type': mime_type
      }
    )
    
    # 添加后台处理任务
    background_tasks.add_task(
      process_translation_task,
      task_id,
      file_info,
      headers
    )
    
    response_data = {
      'task_id': task_id,
      'status': STATUS_PROCESSING,
      'progress': 2,
      'message': '文件验证成功，开始处理翻译',
      'estimated_time': estimated_time,
      'exp_time': int(time.time() + TRANSLATE_TASK_EXP_TIME)
    }
    
    return JSONResponse(
      content=R.ok(response_data).dict(),
      headers=response_headers
    )
  
  except Exception as e:
    logger.error(f"文件上传处理失败: {e}", exc_info=True)
    return JSONResponse(
      content=R.error('500', f'文件处理失败: {str(e)}').dict(),
      headers={AI_ASSISTANT_HEADER: AI_ASSISTANT_WARN_URLENCODER}
    )


@router.get("/jiliang/translate_progress/{task_id}")
async def get_translate_progress(request: Request, task_id: str):
  """
  查询翻译任务进度

  Args:
      request: FastAPI请求对象
      task_id: 任务ID

  Returns:
      任务进度信息
  """
  try:
    
    progress_key = f"{REDIS_TABLE}:translate:progress:{task_id}"
    progress_data = redis_client.get(progress_key)
    
    if not progress_data:
      return R.error('404', '找不到翻译任务或任务已过期')
    
    progress_info = json.loads(progress_data)
    
    result = {
      'task_id': task_id,
      'status': progress_info.get('status', STATUS_PROCESSING),
      'progress': progress_info.get('progress', 0),
      'message': progress_info.get('message', '处理中')
    }
    
    optional_fields = [
      'file_id', 'file_name', 'estimated_time', 'total_pages',
      'processed_pages', 'created_at', 'updated_at', 'exp_time',
      'process_time', 'mime_type', 'error'
    ]
    
    for field in optional_fields:
      if field in progress_info:
        result[field] = progress_info[field]
    
    # 检查是否有error字段且状态为completed，如果是则改为failed
    if progress_info.get('status') == STATUS_COMPLETED and 'error' in progress_info:
      result['status'] = STATUS_FAILED
      # 更新Redis中的状态
      progress_info['status'] = STATUS_FAILED
      redis_client.setex(progress_key, TRANSLATE_TASK_EXP_TIME, json.dumps(progress_info))
    
    # 已完成
    if result.get('status') == STATUS_COMPLETED:
      return R.ok(result)
    
    # 失败
    if result.get('status') == STATUS_FAILED:
      response = R.error('500', progress_info.get('message', '处理失败'))
      response.data = result
      return response
    
    return R.ok(result)
  
  except Exception as e:
    logger.error(f"查询翻译进度失败: {e}", exc_info=True)
    return R.error('500', f'查询翻译进度失败: {str(e)}')


@router.post("/jiliang/translate_v2")
async def translate_file_with_body_params(
    request: Request,
    background_tasks: BackgroundTasks,
    file: UploadFile = File(...),
    model: Optional[str] = Form(None),
    source_lang: str = Form("auto"),
    target_lang: str = Form("auto"),
    function_type: Optional[str] = Form(None),
    function_name: Optional[str] = Form(None)
):
  """
  使用表单参数的文件翻译接口

  Args:
      request: FastAPI请求对象
      background_tasks: 后台任务
      file: 上传的文件
      model: 模型名称
      source_lang: 源语言 (支持中英文，如: "auto", "English", "英语", "Chinese", "中文")
      target_lang: 目标语言 (支持中英文，如: "auto", "English", "英语", "Chinese", "中文")

  Returns:
      任务ID及状态信息

  Examples:
      - target_lang="English" 或 target_lang="英语" 都表示翻译成英语
      - target_lang="Chinese" 或 target_lang="中文" 都表示翻译成中文
  """
  start_time = time.time()
  
  try:
    headers = dict(request.headers)
    
    # 如果提供了功能类型但没有功能名称，自动查询
    if function_type and not function_name:
      func_info = SimplifiedStatsService._get_function_info(function_type)
      if func_info:
        function_name = func_info['function_name']
        logger.info(f"自动查询功能名称: {function_type} -> {function_name}")
    
    logger.info(f"翻译请求 - Function: {function_type}, FunctionName: {function_name}")
    
    # 标准化语言参数
    normalized_source_lang = normalize_language(source_lang)
    normalized_target_lang = normalize_language(target_lang)
    
    task_id = str(uuid4())
    progress_key = f"{REDIS_TABLE}:translate:progress:{task_id}"
    
    response_headers = {
      "Task-Id": task_id,
      AI_ASSISTANT_HEADER: AI_ASSISTANT_WARN_URLENCODER
    }
    
    logger.info(
      f"文件翻译请求v2: 文件名={file.filename}, 任务ID={task_id}, "
      f"源语言={source_lang}({normalized_source_lang}), "
      f"目标语言={target_lang}({normalized_target_lang}), 模型={model}")
    
    # 清理文件名
    original_filename = file.filename
    sanitized_filename = await sanitize_filename(original_filename)
    
    if sanitized_filename != original_filename:
      logger.info(f"文件名已清理: {original_filename} -> {sanitized_filename}")
      file.filename = sanitized_filename
    
    # 保存上传文件
    file_location = os.path.join(PUT_IN_DIR, f"{task_id}_{file.filename}")
    with open(file_location, "wb") as buffer:
      shutil.copyfileobj(file.file, buffer)
    
    await update_progress(
      progress_key, 0, "文件已上传，正在验证", STATUS_PROCESSING,
      additional_data={
        'file_name': file.filename,
        'created_at': datetime.now().strftime('%Y-%m-%d %H:%M:%S'),
        'exp_time': int(time.time() + TRANSLATE_TASK_EXP_TIME),
        'source_lang': source_lang,
        'target_lang': target_lang,
        'normalized_source_lang': normalized_source_lang,
        'normalized_target_lang': normalized_target_lang,
        'model': model
      }
    )
    
    is_allowed, mime_type = verify_mime_type(file_location)
    
    if not is_allowed:
      logger.error(f"不支持的文件类型: {mime_type}, 文件: {file.filename}")
      
      os.remove(file_location)
      
      await update_progress(
        progress_key, 100, f"不支持的文件类型: {mime_type}，仅支持PDF、Word文档和图片",
        STATUS_FAILED
      )
      
      return R.error('400', f'不支持的文件类型: {mime_type}，仅支持PDF、Word文档和图片')
    
    # 估计处理时间
    total_pages, estimated_time = await estimate_processing_time(file_location, mime_type)
    
    file_info = {
      'file_path': file_location,
      'file_name': file.filename,
      'mime_type': mime_type,
      'file_size': os.path.getsize(file_location),
      'total_pages': total_pages,
      'source_lang': normalized_source_lang,
      'target_lang': normalized_target_lang,
      'model': model,
      'function_type': function_type,
      'function_name': function_name
    }
    
    # 更新状态为处理中，包含预计时间
    await update_progress(
      progress_key, 2, "文件验证成功，开始处理翻译", STATUS_PROCESSING,
      estimated_time=estimated_time,
      additional_data={
        'total_pages': total_pages,
        'mime_type': mime_type,
        'source_lang': source_lang,
        'target_lang': target_lang,
        'normalized_source_lang': normalized_source_lang,
        'normalized_target_lang': normalized_target_lang,
        'model': model
      }
    )
    
    # 添加后台处理任务
    background_tasks.add_task(
      process_translation_task,
      task_id,
      file_info,
      headers
    )
    
    response_data = {
      'task_id': task_id,
      'status': STATUS_PROCESSING,
      'progress': 2,
      'message': '文件验证成功，开始处理翻译',
      'estimated_time': estimated_time,
      'exp_time': int(time.time() + TRANSLATE_TASK_EXP_TIME),
      'source_lang': source_lang,
      'target_lang': target_lang,
      'normalized_source_lang': normalized_source_lang,
      'normalized_target_lang': normalized_target_lang,
      'model': model
    }
    
    return JSONResponse(
      content=R.ok(response_data).dict(),
      headers=response_headers
    )
  
  except Exception as e:
    logger.error(f"文件上传处理失败: {e}", exc_info=True)
    return JSONResponse(
      content=R.error('500', f'文件处理失败: {str(e)}').dict(),
      headers={AI_ASSISTANT_HEADER: AI_ASSISTANT_WARN_URLENCODER}
    )


@router.post("/jiliang/translate_chunk")
async def upload_translate_chunk(
    request: Request,
    task_id: str = Query(..., description="任务ID"),
    chunk_index: int = Query(..., description="分块索引，从0开始"),
    total_chunks: int = Query(..., description="总分块数"),
    filename: str = Query(..., description="文件名"),
    model: str = Query(None, description="模型名称，不指定时使用默认模型"),
    source_lang: str = Query("auto", description="源语言，默认为自动检测"),
    target_lang: str = Query("auto", description="目标语言，默认为自动(中文内容译为英文，其他语言译为中文)"),
    function_type: str = Query(None, description="功能类型英文代码"),
    function_name: str = Query(None, description="功能名称中文"),
    file: UploadFile = File(..., description="文件块")
):
  """
  分块上传翻译文件（用于断点续传）

  Args:
      request: FastAPI请求对象
      task_id: 任务ID
      chunk_index: 分块索引
      total_chunks: 总分块数
      filename: 文件名
      file: 文件块

  Returns:
      上传状态
  """
  try:
    
    headers = dict(request.headers)
    
    # 文件块元数据
    chunks_key = f"{REDIS_TABLE}:translate:chunks:{task_id}"
    progress_key = f"{REDIS_TABLE}:translate:progress:{task_id}"
    
    # 检查文件名
    sanitized_filename = await sanitize_filename(filename)
    if sanitized_filename != filename:
      logger.info(f"文件名已清理: {filename} -> {sanitized_filename}")
      filename = sanitized_filename
    
    # 创建或验证分块存储目录
    chunks_dir = os.path.join(PUT_IN_DIR, f"chunks_{task_id}")
    os.makedirs(chunks_dir, exist_ok=True)
    
    # 保存当前分块
    chunk_path = os.path.join(chunks_dir, f"chunk_{chunk_index}")
    with open(chunk_path, "wb") as buffer:
      shutil.copyfileobj(file.file, buffer)
    
    chunk_info = redis_client.get(chunks_key)
    
    if not chunk_info:
      chunks_status = {
        'filename': filename,
        'total_chunks': total_chunks,
        'uploaded_chunks': [chunk_index],
        'created_at': datetime.now().strftime('%Y-%m-%d %H:%M:%S')
      }
    else:
      chunks_status = json.loads(chunk_info)
      if chunk_index not in chunks_status['uploaded_chunks']:
        chunks_status['uploaded_chunks'].append(chunk_index)
      chunks_status['updated_at'] = datetime.now().strftime('%Y-%m-%d %H:%M:%S')
    
    redis_client.setex(chunks_key, TRANSLATE_TASK_EXP_TIME, json.dumps(chunks_status))
    
    # 更新总体进度
    uploaded_count = len(chunks_status['uploaded_chunks'])
    upload_progress = min(5, int(5 * uploaded_count / total_chunks))
    
    if not redis_client.get(progress_key):
      await update_progress(
        progress_key, upload_progress, f"文件上传中: {uploaded_count}/{total_chunks}",
        STATUS_UPLOADING,
        additional_data={
          'file_name': filename,
          'created_at': datetime.now().strftime('%Y-%m-%d %H:%M:%S'),
          'exp_time': int(time.time() + TRANSLATE_TASK_EXP_TIME)
        }
      )
    else:
      # 更新上传进度
      await update_progress(
        progress_key, upload_progress, f"文件上传中: {uploaded_count}/{total_chunks}",
        STATUS_UPLOADING
      )
    
    if uploaded_count == total_chunks:
      
      await update_progress(progress_key, 5, "所有分块已上传，正在合并文件", STATUS_PROCESSING)
      
      # 合并文件
      final_file_path = os.path.join(PUT_IN_DIR, f"{task_id}_{filename}")
      
      with open(final_file_path, 'wb') as outfile:
        for i in range(total_chunks):
          chunk_file = os.path.join(chunks_dir, f"chunk_{i}")
          with open(chunk_file, 'rb') as infile:
            outfile.write(infile.read())
      
      # 验证文件类型
      is_allowed, mime_type = verify_mime_type(final_file_path)
      
      if not is_allowed:
        logger.error(f"不支持的文件类型: {mime_type}, 文件: {filename}")
        
        # 清理文件
        shutil.rmtree(chunks_dir)
        os.remove(final_file_path)
        
        # 更新失败状态
        await update_progress(
          progress_key, 100, f"不支持的文件类型: {mime_type}，仅支持PDF和Word文档",
          STATUS_FAILED
        )
        
        return R.error('400', f'不支持的文件类型: {mime_type}，仅支持PDF和Word文档')
      
      # 估计处理时间
      total_pages, estimated_time = await estimate_processing_time(final_file_path, mime_type)
      
      file_info = {
        'file_path': final_file_path,
        'file_name': filename,
        'mime_type': mime_type,
        'file_size': os.path.getsize(final_file_path),
        'total_pages': total_pages,
        'source_lang': source_lang,
        'target_lang': target_lang,
        'model': model,
        'function_type': function_type,
        'function_name': function_name
      }
      
      # 更新状态为处理中，包含预计时间
      await update_progress(
        progress_key, 6, "文件合并成功，开始处理翻译", STATUS_PROCESSING,
        estimated_time=estimated_time,
        additional_data={
          'total_pages': total_pages,
          'mime_type': mime_type,
          'source_lang': source_lang,
          'target_lang': target_lang
        }
      )
      
      asyncio.create_task(process_translation_task(task_id, file_info, headers))
      
      shutil.rmtree(chunks_dir)
      
      return R.ok({
        'task_id': task_id,
        'chunk_index': chunk_index,
        'total_chunks': total_chunks,
        'status': 'completed',
        'message': '所有分块上传完成，开始处理翻译',
        'estimated_time': estimated_time
      })
    
    return R.ok({
      'task_id': task_id,
      'chunk_index': chunk_index,
      'total_chunks': total_chunks,
      'uploaded_chunks': chunks_status['uploaded_chunks'],
      'status': 'uploading',
      'message': f'分块 {chunk_index + 1}/{total_chunks} 上传成功'
    })
  
  except Exception as e:
    logger.error(f"分块上传失败: {e}", exc_info=True)
    return R.error('500', f'分块上传失败: {str(e)}')


@router.get("/jiliang/translate_chunks/{task_id}")
async def get_translate_chunks_status(request: Request, task_id: str):
  """
  获取翻译文件分块上传状态

  Args:
      request: FastAPI请求对象
      task_id: 任务ID

  Returns:
      分块上传状态
  """
  try:
    chunks_key = f"{REDIS_TABLE}:translate:chunks:{task_id}"
    chunk_info = redis_client.get(chunks_key)
    
    if not chunk_info:
      return R.error('404', '找不到分块上传任务或任务已过期')
    
    chunks_status = json.loads(chunk_info)
    
    return R.ok({
      'task_id': task_id,
      'filename': chunks_status.get('filename', ''),
      'total_chunks': chunks_status.get('total_chunks', 0),
      'uploaded_chunks': chunks_status.get('uploaded_chunks', []),
      'missing_chunks': [
        i for i in range(chunks_status.get('total_chunks', 0))
        if i not in chunks_status.get('uploaded_chunks', [])
      ],
      'created_at': chunks_status.get('created_at', ''),
      'updated_at': chunks_status.get('updated_at', '')
    })
  
  except Exception as e:
    logger.error(f"获取分块状态失败: {e}", exc_info=True)
    return R.error('500', f'获取分块状态失败: {str(e)}')

@router.get("/jiliang/stats/functions")
async def get_translate_function_stats(request: Request,
                                       function_type: str = None,
                                       start_date: str = None,
                                       end_date: str = None):
  """获取翻译功能调用统计"""
  try:
    from simplified_stats_service import StatsQueryService
    stats = StatsQueryService.get_function_stats_from_existing_tables(
      'SX_TRANSLATE_RECORDS', start_date, end_date, function_type
    )
    return R.ok({"functions": stats})
  except Exception as e:
    logger.error(f"查询翻译功能统计失败: {e}")
    return R.error('500', f"查询失败: {str(e)}")

@router.get("/jiliang/stats/users/{user_id}")
async def get_translate_user_stats(request: Request,
                                   user_id: str,
                                   start_date: str = None,
                                   end_date: str = None):
  """获取翻译用户功能使用统计"""
  header = dict(request.headers)
  request_user = header.get('x-username', '')
  
  # 权限验证：只能查询自己的统计
  if request_user != user_id:
    return R.error('403', "权限验证失败")
  
  try:
    from simplified_stats_service import StatsQueryService
    stats = StatsQueryService.get_user_function_stats(
      user_id, 'SX_TRANSLATE_RECORDS', start_date, end_date
    )
    return R.ok({"user_stats": stats})
  except Exception as e:
    logger.error(f"查询翻译用户统计失败: {e}")
    return R.error('500', f"查询失败: {str(e)}")