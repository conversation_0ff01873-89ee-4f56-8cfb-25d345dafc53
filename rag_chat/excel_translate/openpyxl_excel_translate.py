#!/usr/bin/env python3
"""
增强版Excel翻译工具 - 支持文本框和图形对象
处理 .xls 和 .xlsx 格式，能够检测和翻译文本框、图形对象中的文本
"""

import os
import json
import xlrd
import xlwt
from pathlib import Path
from typing import Dict, List, Optional, Tuple
from dataclasses import dataclass
from dotenv import load_dotenv
import zipfile
import xml.etree.ElementTree as ET
import re

# 导入必要的库
try:
  from xlutils.copy import copy as xlutils_copy
  XLUTILS_AVAILABLE = True
except ImportError:
  XLUTILS_AVAILABLE = False

try:
  import openpyxl
  from openpyxl import load_workbook
  OPENPYXL_AVAILABLE = True
except ImportError:
  OPENPYXL_AVAILABLE = False

load_dotenv(override=True)

# LLM 支持
try:
  from langchain_openai import ChatOpenAI
  from langchain_core.messages import HumanMessage, SystemMessage
  USE_LANGCHAIN = True
except ImportError:
  USE_LANGCHAIN = False

@dataclass
class EnhancedExcelTranslatorConfig:
  """增强版Excel翻译器配置"""
  model_name: str = ""
  api_key: str = ""
  base_url: str = ""
  target_language: str = "中文"
  batch_size: int = 10
  handle_shapes: bool = True  # 是否处理图形对象
  handle_textboxes: bool = True  # 是否处理文本框
  convert_xls_to_xlsx: bool = True  # 是否自动转换xls为xlsx
  
  def __post_init__(self):
      if not self.api_key:
          self.api_key = os.getenv("OPENAI_API_KEY", "")
      if not self.base_url:
          self.base_url = os.getenv("OPENAI_BASE_URL", "")
      if not self.model_name:
          self.model_name = os.getenv("OPENAI_MODEL", "")

class EnhancedExcelTranslator:
  """增强版Excel翻译器"""
  
  def __init__(self, config: EnhancedExcelTranslatorConfig):
      self.config = config
      
      if USE_LANGCHAIN and config.api_key:
          try:
              self.llm = ChatOpenAI(
                  model=config.model_name,
                  temperature=0.1,
                  top_p=0.95
              )
          except Exception as e:
              print(f"初始化LangChain失败: {e}")
              self.llm = None
      else:
          self.llm = None
  
  def convert_xls_to_xlsx(self, xls_file: str) -> str:
      """将xls文件转换为xlsx格式"""
      if not OPENPYXL_AVAILABLE:
          print("警告：openpyxl不可用，无法转换xls到xlsx")
          return xls_file
      
      try:
          print(f"正在转换 {xls_file} 为 xlsx 格式...")
          
          # 读取xls文件
          workbook = xlrd.open_workbook(xls_file)
          
          # 创建新的xlsx文件
          xlsx_file = xls_file.rsplit('.', 1)[0] + '_temp.xlsx'
          new_workbook = openpyxl.Workbook()
          
          # 删除默认的工作表
          new_workbook.remove(new_workbook.active)
          
          # 转换每个工作表
          for sheet_name in workbook.sheet_names():
              old_sheet = workbook.sheet_by_name(sheet_name)
              new_sheet = new_workbook.create_sheet(sheet_name)
              
              # 复制数据
              for row in range(old_sheet.nrows):
                  for col in range(old_sheet.ncols):
                      try:
                          value = old_sheet.cell_value(row, col)
                          new_sheet.cell(row=row+1, column=col+1, value=value)
                      except Exception:
                          continue
          
          new_workbook.save(xlsx_file)
          print(f"转换完成: {xlsx_file}")
          return xlsx_file
          
      except Exception as e:
          print(f"转换失败: {e}")
          return xls_file
  
  def extract_textbox_content_from_xlsx(self, xlsx_file: str) -> List[Dict]:
      """从xlsx文件中提取文本框和图形对象的文本"""
      textbox_contents = []
      
      try:
          # xlsx文件实际上是一个ZIP文件
          with zipfile.ZipFile(xlsx_file, 'r') as zip_file:
              # 检查绘图文件
              drawing_files = [name for name in zip_file.namelist() 
                             if name.startswith('xl/drawings/') and name.endswith('.xml')]
              
              for drawing_file in drawing_files:
                  print(f"  分析绘图文件: {drawing_file}")
                  
                  try:
                      with zip_file.open(drawing_file) as f:
                          content = f.read().decode('utf-8')
                          
                      # 解析XML
                      root = ET.fromstring(content)
                      
                      # 定义命名空间
                      namespaces = {
                          'xdr': 'http://schemas.openxmlformats.org/drawingml/2006/spreadsheetDrawing',
                          'a': 'http://schemas.openxmlformats.org/drawingml/2006/main',
                          'r': 'http://schemas.openxmlformats.org/officeDocument/2006/relationships'
                      }
                      
                      # 查找文本框和形状
                      shapes = root.findall('.//xdr:sp', namespaces)
                      text_boxes = root.findall('.//xdr:cxnSp', namespaces)
                      
                      for shape in shapes + text_boxes:
                          # 查找文本内容
                          text_elements = shape.findall('.//a:t', namespaces)
                          
                          for text_elem in text_elements:
                              if text_elem.text and text_elem.text.strip():
                                  textbox_contents.append({
                                      "file": drawing_file,
                                      "type": "shape_text",
                                      "original_text": text_elem.text.strip(),
                                      "xml_element": text_elem
                                  })
                                  print(f"    找到图形文本: {text_elem.text.strip()[:50]}...")
                      
                  except Exception as e:
                      print(f"    处理绘图文件失败: {e}")
                      continue
              
              # 检查工作表中的文本框引用
              worksheet_files = [name for name in zip_file.namelist() 
                               if name.startswith('xl/worksheets/') and name.endswith('.xml')]
              
              for ws_file in worksheet_files:
                  try:
                      with zip_file.open(ws_file) as f:
                          content = f.read().decode('utf-8')
                      
                      # 查找可能的文本框引用
                      if 'drawing' in content or 'textbox' in content.lower():
                          print(f"  工作表 {ws_file} 包含绘图引用")
                          
                  except Exception:
                      continue
      
      except Exception as e:
          print(f"提取文本框内容失败: {e}")
      
      print(f"共找到 {len(textbox_contents)} 个文本框/图形对象")
      return textbox_contents
  
  def analyze_excel_enhanced(self, file_path: str) -> Dict:
      """增强版Excel分析，包括文本框和图形对象"""
      print(f"正在分析Excel文件: {file_path}")
      
      # 检查文件格式
      file_ext = Path(file_path).suffix.lower()
      is_xlsx = file_ext == '.xlsx'
      
      # 如果是xls文件且需要处理图形对象，先转换为xlsx
      working_file = file_path
      temp_file_created = False
      
      if not is_xlsx and self.config.convert_xls_to_xlsx and (self.config.handle_shapes or self.config.handle_textboxes):
          working_file = self.convert_xls_to_xlsx(file_path)
          temp_file_created = (working_file != file_path)
          is_xlsx = True
      
      try:
          # 使用原有方法分析单元格内容
          analysis_result = self.analyze_excel_basic(working_file)
          
          # 如果是xlsx格式且需要处理图形对象，提取文本框内容
          textbox_contents = []
          if is_xlsx and (self.config.handle_shapes or self.config.handle_textboxes):
              textbox_contents = self.extract_textbox_content_from_xlsx(working_file)
          
          # 合并结果
          analysis_result["textbox_contents"] = textbox_contents
          analysis_result["temp_file"] = working_file if temp_file_created else None
          
          return analysis_result
          
      except Exception as e:
          print(f"增强分析失败: {e}")
          return {"error": str(e)}
      
      finally:
          # 清理临时文件
          if temp_file_created and working_file != file_path:
              try:
                  os.remove(working_file)
                  print(f"清理临时文件: {working_file}")
              except:
                  pass
  
  def analyze_excel_basic(self, file_path: str) -> Dict:
      """基础Excel分析（原有方法）"""
      try:
          workbook = xlrd.open_workbook(file_path)
          sheet_names = workbook.sheet_names()

          text_cells = []
          sheet_names_to_translate = []

          # 收集需要翻译的sheet名称
          for sheet_name in sheet_names:
              if any(c.isalpha() for c in sheet_name) and not sheet_name.replace(' ', '').replace('-', '').replace('_', '').isdigit():
                  sheet_names_to_translate.append({
                      "original_name": sheet_name,
                      "index": sheet_names.index(sheet_name)
                  })

          # 遍历所有工作表
          for sheet_name in sheet_names:
              sheet = workbook.sheet_by_name(sheet_name)
              print(f"  分析工作表: {sheet_name} ({sheet.nrows}行 x {sheet.ncols}列)")

              # 遍历所有单元格
              for row in range(sheet.nrows):
                  for col in range(sheet.ncols):
                      try:
                          value = sheet.cell_value(row, col)
                          if isinstance(value, str) and len(value.strip()) > 0:
                              clean_value = value.strip()
                              if (not clean_value.replace('.', '').replace(',', '').replace('-', '').replace('%', '').isdigit()
                                  and not clean_value.startswith('=')):
                                  text_cells.append({
                                      "sheet_name": sheet_name,
                                      "row": row,
                                      "col": col,
                                      "original_text": clean_value,
                                      "cell_ref": f"{sheet_name}!{chr(65 + col)}{row + 1}"
                                  })
                      except Exception:
                          continue

          print(f"找到 {len(text_cells)} 个需要翻译的文本单元格")
          print(f"找到 {len(sheet_names_to_translate)} 个需要翻译的工作表名称")

          return {
              "sheet_names": sheet_names,
              "text_cells": text_cells,
              "sheet_names_to_translate": sheet_names_to_translate
          }

      except Exception as e:
          print(f"分析Excel文件失败: {e}")
          return {"error": str(e)}
  
  def translate_texts_with_llm(self, texts: List[str]) -> List[str]:
      """使用LLM翻译文本"""
      if not self.llm or not texts:
          print("  警告：没有可用的翻译服务，返回原文")
          return texts

      try:
          texts_json = json.dumps(texts, ensure_ascii=False, indent=2)

          system_prompt = f"""你是专业的财务文档翻译专家。将英文准确翻译成{self.config.target_language}。

严格要求：
1. 只输出一个JSON数组，不要任何其他内容、解释或说明
2. 数组中每个元素是一个翻译后的字符串
3. 保持专业术语准确性
4. 保持原文格式（括号、标点、数字等）
5. 翻译顺序与输入完全一致

常用术语：
THERMAL POWER PLANT→火力发电厂, FINANCIAL MODEL→财务模型, PROJECT→项目, ANALYSIS→分析
COST→成本, INVESTMENT→投资, REVENUE→收入, CASH FLOW→现金流
The model has not been audited→本模型未经审计
No representation, warranty or undertaking→不做任何陈述、保证或承诺

重要：只输出JSON数组，不要包含任何其他文字！"""

          user_prompt = f"""翻译以下内容到{self.config.target_language}，只输出JSON数组：
{texts_json}"""

          messages = [
              SystemMessage(content=system_prompt),
              HumanMessage(content=user_prompt)
          ]

          response = self.llm.invoke(messages)
          response_content = str(response.content).strip()

          # 清理响应内容
          if response_content.startswith('```json'):
              response_content = response_content[7:]
          if response_content.startswith('```'):
              response_content = response_content[3:]
          if response_content.endswith('```'):
              response_content = response_content[:-3]
          response_content = response_content.strip()

          # 查找JSON数组
          start_idx = response_content.find('[')
          end_idx = response_content.rfind(']')

          if start_idx != -1 and end_idx != -1 and end_idx > start_idx:
              json_content = response_content[start_idx:end_idx+1]
              
              try:
                  translated_texts = json.loads(json_content)
                  if isinstance(translated_texts, list) and len(translated_texts) == len(texts):
                      return translated_texts
              except json.JSONDecodeError:
                  pass
          
          print("翻译响应解析失败，返回原文")
          return texts

      except Exception as e:
          print(f"LLM翻译失败: {e}")
          return texts
  
  def translate_textbox_contents(self, textbox_contents: List[Dict]) -> List[Dict]:
      """翻译文本框内容"""
      if not textbox_contents:
          return []
      
      print(f"开始翻译 {len(textbox_contents)} 个文本框/图形对象...")
      
      texts_to_translate = [item["original_text"] for item in textbox_contents]
      
      if self.config.api_key and self.llm:
          translated_texts = self.translate_texts_with_llm(texts_to_translate)
      else:
          print("  未配置API密钥，跳过翻译")
          translated_texts = texts_to_translate
      
      # 组合结果
      translated_contents = []
      for i, item in enumerate(textbox_contents):
          translated_item = {
              **item,
              "translated_text": translated_texts[i] if i < len(translated_texts) else item["original_text"]
          }
          translated_contents.append(translated_item)
      
      print(f"文本框翻译完成")
      return translated_contents
  
  def translate_batch(self, text_cells: List[Dict]) -> List[Dict]:
      """批量翻译单元格文本"""
      if not text_cells:
          return []

      print(f"开始翻译 {len(text_cells)} 个文本单元格...")

      translated_cells = []
      batch_size = self.config.batch_size

      for i in range(0, len(text_cells), batch_size):
          batch = text_cells[i:i + batch_size]
          print(f"  翻译批次 {i//batch_size + 1}/{(len(text_cells) + batch_size - 1)//batch_size}")

          texts_to_translate = [cell["original_text"] for cell in batch]

          if self.config.api_key and self.llm:
              translated_texts = self.translate_texts_with_llm(texts_to_translate)
          else:
              print("    未配置API密钥，跳过翻译")
              translated_texts = texts_to_translate

          for j, cell in enumerate(batch):
              translated_cell = {
                  **cell,
                  "translated_text": translated_texts[j] if j < len(translated_texts) else cell["original_text"]
              }
              translated_cells.append(translated_cell)

      print(f"单元格翻译完成")
      return translated_cells
  
  def create_translation_report(self, analysis_result: Dict, translated_cells: List[Dict], 
                              translated_textboxes: List[Dict]) -> str:
      """创建翻译报告"""
      report = []
      report.append("=== Excel翻译报告 ===\n")
      
      # 基本信息
      report.append(f"工作表数量: {len(analysis_result.get('sheet_names', []))}")
      report.append(f"翻译的单元格数量: {len(translated_cells)}")
      report.append(f"翻译的文本框/图形对象数量: {len(translated_textboxes)}")
      report.append("")
      
      # 文本框翻译详情
      if translated_textboxes:
          report.append("=== 文本框/图形对象翻译详情 ===")
          for i, item in enumerate(translated_textboxes[:10], 1):  # 只显示前10个
              report.append(f"{i}. 原文: {item['original_text'][:50]}...")
              report.append(f"   译文: {item['translated_text'][:50]}...")
              report.append("")
          
          if len(translated_textboxes) > 10:
              report.append(f"... 还有 {len(translated_textboxes) - 10} 个项目\n")
      
      # 单元格翻译详情（部分）
      if translated_cells:
          report.append("=== 单元格翻译详情（部分） ===")
          for i, cell in enumerate(translated_cells[:5], 1):  # 只显示前5个
              report.append(f"{i}. {cell['cell_ref']}: {cell['original_text']} -> {cell['translated_text']}")
          
          if len(translated_cells) > 5:
              report.append(f"... 还有 {len(translated_cells) - 5} 个单元格\n")
      
      return "\n".join(report)
  
  def translate_excel(self, input_file: str, output_file: str) -> Dict:
      """翻译Excel文件的主入口"""
      print(f"开始增强版Excel翻译...")
      print(f"输入文件: {input_file}")
      print(f"输出文件: {output_file}")
      print(f"目标语言: {self.config.target_language}")

      # 1. 增强分析Excel文件
      analysis_result = self.analyze_excel_enhanced(input_file)
      if "error" in analysis_result:
          return {"success": False, "error": analysis_result["error"]}

      # 2. 翻译工作表名称
      translated_sheet_names = []
      if analysis_result.get("sheet_names_to_translate"):
          sheet_names = [item["original_name"] for item in analysis_result["sheet_names_to_translate"]]
          if self.config.api_key and self.llm:
              translated_names = self.translate_texts_with_llm(sheet_names)
              for i, item in enumerate(analysis_result["sheet_names_to_translate"]):
                  translated_sheet_names.append({
                      **item,
                      "translated_name": translated_names[i] if i < len(translated_names) else item["original_name"]
                  })

      # 3. 翻译单元格文本
      translated_cells = self.translate_batch(analysis_result.get("text_cells", []))

      # 4. 翻译文本框内容
      translated_textboxes = self.translate_textbox_contents(analysis_result.get("textbox_contents", []))

      # 5. 创建翻译报告
      report = self.create_translation_report(analysis_result, translated_cells, translated_textboxes)
      print("\n" + report)

      # 6. 保存结果（目前只能保存单元格翻译）
      print("\n注意：当前版本只能保存单元格的翻译结果。")
      print("文本框/图形对象的翻译需要手动处理，或使用支持COM接口的高级工具。")
      
      success = self.save_translated_excel_basic(input_file, output_file, translated_cells, translated_sheet_names)

      return {
          "success": success,
          "sheet_count": len(analysis_result.get("sheet_names", [])),
          "translated_cell_count": len(translated_cells),
          "translated_textbox_count": len(translated_textboxes),
          "translated_sheet_count": len(translated_sheet_names),
          "output_file": output_file,
          "report": report,
          "textbox_translations": translated_textboxes  # 返回文本框翻译结果供手动处理
      }
  
  def save_translated_excel_basic(self, input_file: str, output_file: str, translated_cells: List[Dict], 
                                 translated_sheet_names: Optional[List[Dict]] = None) -> bool:
      """保存翻译后的Excel文件（基础版本）"""
      try:
          print(f"正在保存翻译结果到: {output_file}")
          
          # 读取原始文件
          original_workbook = xlrd.open_workbook(input_file)
          new_workbook = xlwt.Workbook(encoding='utf-8')

          # 创建翻译映射
          translation_map = {}
          for cell in translated_cells:
              key = f"{cell['sheet_name']}_{cell['row']}_{cell['col']}"
              translation_map[key] = cell['translated_text']

          # 创建sheet名称映射
          sheet_name_map = {}
          if translated_sheet_names:
              for sheet_item in translated_sheet_names:
                  sheet_name_map[sheet_item['original_name']] = sheet_item['translated_name']

          # 复制所有工作表
          for sheet_name in original_workbook.sheet_names():
              original_sheet = original_workbook.sheet_by_name(sheet_name)
              new_sheet_name = sheet_name_map.get(sheet_name, sheet_name)
              new_sheet = new_workbook.add_sheet(new_sheet_name)

              print(f"  处理工作表: {sheet_name}" + (f" -> {new_sheet_name}" if new_sheet_name != sheet_name else ""))

              # 复制所有单元格
              for row in range(original_sheet.nrows):
                  for col in range(original_sheet.ncols):
                      try:
                          original_value = original_sheet.cell_value(row, col)
                          key = f"{sheet_name}_{row}_{col}"
                          
                          if key in translation_map:
                              new_value = translation_map[key]
                          else:
                              new_value = original_value

                          if isinstance(new_value, str):
                              new_sheet.write(row, col, new_value)
                          elif isinstance(new_value, (int, float)):
                              new_sheet.write(row, col, new_value)
                          else:
                              new_sheet.write(row, col, str(new_value))

                      except Exception:
                          continue

          new_workbook.save(output_file)
          print(f"Excel文件保存完成: {output_file}")
          return True

      except Exception as e:
          print(f"保存Excel文件失败: {e}")
          return False

def main():
  """主函数"""
  config = EnhancedExcelTranslatorConfig(
      target_language="中文",
      batch_size=5,
      handle_shapes=True,
      handle_textboxes=True,
      convert_xls_to_xlsx=True
  )
  
  translator = EnhancedExcelTranslator(config)
  
  input_file = "Duyen Hai 2 Financial Model.xls"
  output_file = "Duyen Hai 2 Financial Model_translated.xls"
  
  if not Path(input_file).exists():
      print(f"错误：输入文件不存在: {input_file}")
      return
  
  result = translator.translate_excel(input_file, output_file)
  
  print("\n=== 最终翻译结果 ===")
  print(f"成功: {result.get('success', False)}")
  if result.get('error'):
      print(f"错误: {result['error']}")
  else:
      print(f"工作表数量: {result.get('sheet_count', 0)}")
      print(f"翻译的单元格数量: {result.get('translated_cell_count', 0)}")
      print(f"翻译的文本框数量: {result.get('translated_textbox_count', 0)}")
      print(f"输出文件: {result.get('output_file', 'N/A')}")
      
      # 显示文本框翻译结果供手动处理
      textbox_translations = result.get('textbox_translations', [])
      if textbox_translations:
          print("\n=== 文本框翻译结果（需手动处理）===")
          for i, item in enumerate(textbox_translations, 1):
              print(f"{i}. 原文: {item['original_text']}")
              print(f"   译文: {item['translated_text']}")

if __name__ == "__main__":
    main()