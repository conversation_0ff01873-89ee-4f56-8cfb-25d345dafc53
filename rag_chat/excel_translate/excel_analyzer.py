#!/usr/bin/env python3
"""
Excel文件分析工具
用于分析Excel文件的结构和内容
"""

import pandas as pd
import xlrd
from pathlib import Path
import json

def analyze_excel_file(file_path: str):
    """分析Excel文件的结构和内容"""
    print(f"正在分析Excel文件: {file_path}")

    # 使用xlrd读取.xls文件
    try:
        workbook = xlrd.open_workbook(file_path)
        sheet_names = workbook.sheet_names()
        print(f"\n工作表数量: {len(sheet_names)}")
        print(f"工作表名称: {sheet_names}")

        analysis_result = {
            "file_path": file_path,
            "sheet_count": len(sheet_names),
            "sheet_names": sheet_names,
            "sheets_analysis": {}
        }

        # 分析每个工作表
        for sheet_name in sheet_names:
            print(f"\n=== 分析工作表: {sheet_name} ===")
            sheet = workbook.sheet_by_name(sheet_name)

            # 获取工作表的基本信息
            max_row = sheet.nrows
            max_col = sheet.ncols
            print(f"最大行数: {max_row}")
            print(f"最大列数: {max_col}")
            
            # 分析前10行的内容
            sample_data = []
            text_cells = []

            for row in range(0, min(10, max_row)):
                row_data = []
                for col in range(0, min(10, max_col)):
                    try:
                        value = sheet.cell_value(row, col)
                        if value is not None and value != "":
                            row_data.append(str(value))
                            # 收集包含文本的单元格（用于翻译）
                            if isinstance(value, str) and len(value.strip()) > 0:
                                # 简单判断是否包含中文或英文文本（非纯数字）
                                clean_value = value.strip().replace('.', '').replace(',', '').replace('-', '').replace('%', '')
                                if not clean_value.isdigit() and clean_value != "":
                                    text_cells.append({
                                        "row": row + 1,  # 转换为1基索引
                                        "col": col + 1,  # 转换为1基索引
                                        "value": value.strip(),
                                        "cell_ref": f"{chr(65 + col)}{row + 1}"  # A1格式
                                    })
                        else:
                            row_data.append("")
                    except Exception as e:
                        row_data.append(f"ERROR: {e}")
                sample_data.append(row_data)
            
            sheet_analysis = {
                "max_row": max_row,
                "max_col": max_col,
                "sample_data": sample_data,
                "text_cells": text_cells[:20],  # 只保留前20个文本单元格
                "text_cell_count": len(text_cells)
            }
            
            analysis_result["sheets_analysis"][sheet_name] = sheet_analysis
            
            print(f"包含文本的单元格数量: {len(text_cells)}")
            if text_cells:
                print("前5个文本单元格示例:")
                for i, cell in enumerate(text_cells[:5]):
                    print(f"  {cell['cell_ref']}: {cell['value']}")
        
        return analysis_result
        
    except Exception as e:
        print(f"分析Excel文件时出错: {e}")
        return None

def main():
    """主函数"""
    excel_file = "/Volumes/Extreme SSD/sx_ai/sx-ai-rag-chat/rag_chat/pdf_translate/Duyen Hai 2 Financial Model.xls"
    
    if not Path(excel_file).exists():
        print(f"文件不存在: {excel_file}")
        return
    
    # 分析Excel文件
    result = analyze_excel_file(excel_file)
    
    if result:
        # 保存分析结果
        output_file = "excel_analysis_result.json"
        with open(output_file, 'w', encoding='utf-8') as f:
            json.dump(result, f, ensure_ascii=False, indent=2)
        print(f"\n分析结果已保存到: {output_file}")
        
        # 打印总结
        print(f"\n=== 分析总结 ===")
        print(f"文件: {result['file_path']}")
        print(f"工作表数量: {result['sheet_count']}")
        
        total_text_cells = 0
        for sheet_name, sheet_data in result['sheets_analysis'].items():
            total_text_cells += sheet_data['text_cell_count']
            print(f"  {sheet_name}: {sheet_data['max_row']}行 x {sheet_data['max_col']}列, {sheet_data['text_cell_count']}个文本单元格")
        
        print(f"总计需要翻译的文本单元格: {total_text_cells}")

if __name__ == "__main__":
    main()
