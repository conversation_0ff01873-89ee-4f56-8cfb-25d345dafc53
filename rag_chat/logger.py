# import sys
#
# from loguru import logger
#
# logger.remove()
# # 打印控制台
# logger.add(sys.stdout, level='INFO')
# logger.add('logs/info.log', rotation='00:00', retention='1 weeks')
# logger.add('logs/error.log', rotation='00:00', retention='2 weeks', level='ERROR')
import sys
import os
from pathlib import Path
from loguru import logger

logger.remove()
# 打印控制台
logger.add(sys.stdout, level='INFO')


# 创建日志目录并处理权限问题
def setup_log_files():
  """设置日志文件，自动处理权限问题"""
  
  log_dirs = [
    'logs',  # 相对路径 - 原始配置
    '/project/logs',  # 绝对路径
    '/tmp/sx-jiliang-logs',  # 临时目录
    '/tmp'  # 系统临时目录
  ]
  
  for log_dir in log_dirs:
    try:
      # 创建目录
      Path(log_dir).mkdir(parents=True, exist_ok=True)
      
      # 测试写入权限
      test_file = os.path.join(log_dir, 'test_write.tmp')
      with open(test_file, 'w') as f:
        f.write('test')
      os.remove(test_file)
      
      # 添加日志文件
      info_log = os.path.join(log_dir, 'info.log')
      error_log = os.path.join(log_dir, 'error.log')
      
      logger.add(info_log, rotation='00:00', retention='1 weeks')
      logger.add(error_log, rotation='00:00', retention='2 weeks', level='ERROR')
      
      logger.info(f"日志文件已设置到: {log_dir}")
      return True
    
    except Exception as e:
      logger.warning(f"无法使用日志目录 {log_dir}: {e}")
      continue
  
  logger.error("所有日志目录都无法使用，仅保留控制台输出")
  return False


# 执行日志设置
setup_log_files()