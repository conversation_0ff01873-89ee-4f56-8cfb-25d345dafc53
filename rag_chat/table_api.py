"""
OCR表格识别API接口
"""

import json
import base64
import logging
from typing import Optional, Dict, Any
from fastapi import APIRouter, HTTPException, Header, Request
from pydantic import BaseModel, Field

from table_client import DocumentOCRClient

# 配置日志
logger = logging.getLogger(__name__)

# 创建路由
router = APIRouter()

# 全局OCR客户端实例
ocr_client = DocumentOCRClient()


class OCRRequest(BaseModel):
  """OCR识别请求模型"""
  image: str = Field(..., description="Base64编码的图片数据")
  object_type: str = Field("general_v5", description="识别类型")
  type: str = Field("st_ocrapi", description="返回类型，st_ocrapi 或 st_ocrapi_all")
  languagetype: str = Field("CHN_ENG", description="语言类型")
  line_probability: bool = Field(False, description="是否返回行置信度")
  img_direction: Optional[str] = Field(None, description="方向判断参数")
  rec_only: Optional[str] = Field(None, description="跳过检测参数")
  auto_detect_langtype: bool = Field(False, description="自动检测语种")
  eng_granularity: str = Field("word", description="英文粒度")
  disp_line_poly: bool = Field(False, description="是否返回行的多点坐标")
  disp_paragraph_poly: bool = Field(False, description="是否返回段落结果")


class OCRResponse(BaseModel):
  """OCR识别响应模型"""
  success: bool = Field(..., description="是否成功")
  message: str = Field(..., description="响应消息")
  error_code: Optional[int] = Field(None, description="错误代码")
  error_message: Optional[str] = Field(None, description="错误信息")
  text_lines: Optional[list] = Field(None, description="文本行列表")
  full_text: Optional[str] = Field(None, description="完整文本内容")
  line_count: Optional[int] = Field(None, description="文本行数")
  paragraphs: Optional[list] = Field(None, description="段落信息")
  calc_time_ms: Optional[int] = Field(None, description="计算时间(毫秒)")
  wait_time_ms: Optional[int] = Field(None, description="等待时间(毫秒)")
  image_info: Optional[dict] = Field(None, description="图片信息")
  data: Optional[dict] = Field(None, description="完整响应数据")
  error: Optional[str] = Field(None, description="错误详情")


def validate_auth_headers(x_api_key: str = None, x_username: str = None):
  """验证认证头部"""
  if not x_username:
    raise HTTPException(status_code=401, detail="缺少用户名")
  if not x_api_key:
    raise HTTPException(status_code=401, detail="缺少API密钥")
  
  if len(x_api_key) < 10:
    raise HTTPException(status_code=401, detail="无效的API密钥格式")
  
  if len(x_username) < 2:
    raise HTTPException(status_code=401, detail="无效的用户名格式")


def validate_image_data(image_base64: str) -> bytes:
  """验证和解码Base64图片数据"""
  if not image_base64:
    raise HTTPException(status_code=400, detail="图片数据不能为空")
  
  try:
    # 解码Base64图片数据
    image_data = base64.b64decode(image_base64)
    if len(image_data) == 0:
      raise ValueError("图片数据为空")
  except Exception as e:
    raise HTTPException(status_code=400, detail=f"无效的Base64图片数据: {str(e)}")
  
  # 检查文件大小限制（20MB）
  max_size = 20 * 1024 * 1024
  if len(image_data) > max_size:
    size_mb = len(image_data) / (1024 * 1024)
    raise HTTPException(
      status_code=400,
      detail=f"图片数据过大: {size_mb:.1f}MB，建议小于20MB"
    )
  
  return image_data


@router.post(
  "/jiliang/ocr/form",
  response_model=OCRResponse,
  summary="表格OCR识别",
  description="对上传的图片进行OCR识别，提取文本内容"
)
async def ocr_form_recognize(
    request: Request,
    ocr_request: OCRRequest,
    x_api_key: str = Header(None, alias="X-API-Key", description="API密钥"),
    x_username: str = Header(None, alias="X-Username", description="用户名")
):
  """
    表格OCR识别接口

    支持对图片进行OCR识别，提取文本内容和结构信息

    响应：
    - success: 识别是否成功
    - message: 响应消息
    - text_lines: 识别到的文本行列表
    - full_text: 完整文本内容
    - line_count: 文本行数
    - paragraphs: 段落信息
    - calc_time_ms: 处理时间
    """
  
  try:
    # 验证认证信息
    validate_auth_headers(x_api_key, x_username)
    logger.info(f"OCR请求 - 用户: {x_username}, API密钥: {x_api_key[:8]}***")
    
    # 验证和解码图片数据
    image_data = validate_image_data(ocr_request.image)
    logger.info(f"OCR请求 - 图片大小: {len(image_data)} bytes")
    
    result = ocr_client.recognize(
      image_data=image_data,
      object_type=ocr_request.object_type,
      type_param=ocr_request.type,
      languagetype=ocr_request.languagetype,
      line_probability=ocr_request.line_probability,
      img_direction=ocr_request.img_direction,
      rec_only=ocr_request.rec_only,
      auto_detect_langtype=ocr_request.auto_detect_langtype,
      eng_granularity=ocr_request.eng_granularity,
      disp_line_poly=ocr_request.disp_line_poly,
      disp_paragraph_poly=ocr_request.disp_paragraph_poly
    )
    
    # 处理结果
    if result.get('success'):
      logger.info(f"OCR识别成功 - 识别到 {result.get('line_count', 0)} 行文本，"
                  f"处理时间: {result.get('calc_time_ms', 0)}ms")
      
      return OCRResponse(
        success=True,
        message="OCR识别成功",
        error_code=result.get('error_code'),
        error_message=result.get('error_message'),
        text_lines=result.get('text_lines', []),
        full_text=result.get('full_text', ''),
        line_count=result.get('line_count', 0),
        paragraphs=result.get('paragraphs'),
        calc_time_ms=result.get('calc_time_ms'),
        wait_time_ms=result.get('wait_time_ms'),
        image_info=result.get('image_info'),
        data=result
      )
    else:
      logger.warning(f"OCR识别失败 - {result.get('error_message', '未知错误')}")
      
      return OCRResponse(
        success=False,
        message=f"OCR识别失败: {result.get('error_message', result.get('message', '未知错误'))}",
        error_code=result.get('error_code'),
        error_message=result.get('error_message'),
        text_lines=[],
        full_text='',
        line_count=0,
        error=result.get('error_type'),
        data=result
      )
  
  except HTTPException:
    # 重新抛出HTTP异常
    raise
  except Exception as e:
    # 处理其他异常
    logger.error(f"OCR接口异常: {str(e)}", exc_info=True)
    
    return OCRResponse(
      success=False,
      message=f"OCR识别失败: {str(e)}",
      error_code=-1,
      error_message="系统异常",
      text_lines=[],
      full_text='',
      line_count=0,
      error=str(e)
    )


if __name__ == "__main__":
  import uvicorn
  from fastapi import FastAPI
  
  app = FastAPI(
    title="Document OCR API",
    version="1.0.0",
    description="表格OCR识别服务API"
  )
  app.include_router(router)
  
  
  # 添加全局异常处理器
  @app.exception_handler(Exception)
  async def global_exception_handler(request, exc):
    """全局异常处理器"""
    logger.error(f"未处理的异常: {str(exc)}", exc_info=True)
    return {
      "success": False,
      "message": "服务器内部错误",
      "error": str(exc)
    }
  
  
  logger.info("启动Document OCR API测试服务器...")
  uvicorn.run(app, host="0.0.0.0", port=8001, log_level="info")