import os
import json
import tempfile
from dotenv import load_dotenv
from fastapi import APIRouter, Query
from logger import logger
from models import SessionSearchRequest, QARecordQueryRequest, QARecordResponse, ApiResponse, SessionQueryRequest, \
    SessionDeleteRequest, MessageDeleteRequest, MessageRecordsRequest
from dm_util import DMDatabase
from collections import defaultdict

UPLOAD_DIR = tempfile.gettempdir()

router = APIRouter()

load_dotenv()
dm_config = {
    'host': os.getenv('DM_HOST'),
    'port': int(os.getenv('DM_PORT')),
    'user': os.getenv('DM_USER'),
    'password': os.getenv('DM_PASSWORD'),
    'database': os.getenv('DM_DB'),
    'auto_commit': False
}


async def get_real_userid(db, input_userid: str) -> str:
    """
    根据前端传入的 USER_NAME 查询对应的真实 USER_ID
    :param db: 数据库连接对象
    :param input_userid: 可能是用户名或真实 USER_ID 的字符串
    :return: 真实的 USER_ID 或 None（未找到）
    """
    # 如果已经是数字形式，直接返回
    if input_userid.isdigit():
        return input_userid

    # 查询 SYS_USER 表获取真实 USER_ID
    user_sql = """
        SELECT USER_ID FROM SX_AIPLATFORM.SYS_USER 
        WHERE USER_NAME = :username
    """

    try:
        result = db.execute_query(user_sql, {'username': input_userid})
        logger.info(f"用户信息：{result}")
        if result:
            return str(result[0]['USER_ID'])
        else:
            return None
    except Exception as e:
        logger.info(f"查询用户信息失败: {e}")
        return None


@router.post("/jiliang/qa_session_list_records", response_model=ApiResponse)
async def query_qa_session_list_records(request: SessionQueryRequest):
    """查询用户按月分组的会话列表接口（支持图片问答）"""
    try:
        # 参数校验提前返回
        if not request.userid:
            return ApiResponse(
                code="400",
                msg="参数校验失败",
                errorCode="1001",
                errorMsg="用户ID不能为空"
            )

        with DMDatabase(**dm_config) as db:
            logger.info("开始执行查询：query_qa_session_list_records")

            # 获取真实 USER_ID
            real_userid = await get_real_userid(db, request.userid)
            if not real_userid:
                return ApiResponse(
                    code="400",
                    msg="参数校验失败",
                    errorCode="1002",
                    errorMsg=f"无效的用户ID或用户名不存在：{request.userid}"
                )
            logger.info(f"用户ID：{real_userid}")

            # 分页参数计算
            page = max(1, request.page)
            page_size = min(100, max(1, request.page_size))
            start = (page - 1) * page_size + 1
            end = page * page_size

            # SQL 查询语句：联合两个表并分类 chat_type，支持图片问答
            sql = """
                WITH unified_data AS (
                    SELECT
                        TO_CHAR(SESSION_ID) AS qa_id,
                        0 AS chat_type,
                        TO_CHAR(CREATE_TIME, 'YYYY-MM') AS month,
                        TO_CHAR(CREATE_TIME, 'YYYY-MM-DD HH24:MI:SS') AS createtime,
                        TITLE AS title,
                        USER_ID AS user_id
                    FROM "SX_AIPLATFORM"."T_USER_CHAT_SESSION"
                    WHERE DEL_FLAG = 0 AND USER_ID = :real_userid

                    UNION ALL

                    SELECT
                        TO_CHAR(sjr.qa_id) AS qa_id,
                        sjr.CHAT_TYPE AS chat_type,
                        TO_CHAR(sjr.createtime, 'YYYY-MM') AS month,
                        TO_CHAR(sjr.createtime, 'YYYY-MM-DD HH24:MI:SS') AS createtime,
                        CASE
                            WHEN sjr.CHAT_TYPE = 2 THEN CONCAT('图片问答: ', SUBSTR(sjr.question, 1, 30))
                            ELSE sjr.question
                        END AS title,
                        su.USER_ID AS user_id
                    FROM "SX_AIPLATFORM"."sx_jiliang_records" sjr
                    JOIN SX_AIPLATFORM.SYS_USER su ON sjr."userid" = su.USER_NAME
                    WHERE sjr.DEL_FLAG = 0 AND sjr.userid = :raw_userid AND sjr.qa_id IS NOT NULL
                ),
                paged_data AS (
                    SELECT
                        *,
                        ROW_NUMBER() OVER (ORDER BY createtime DESC) AS rn,
                        COUNT(*) OVER () AS total
                    FROM unified_data
                )
                SELECT
                    qa_id,
                    chat_type,
                    month,
                    createtime,
                    title,
                    total
                FROM paged_data
                WHERE rn BETWEEN :start AND :end;
            """

            params = {'real_userid': real_userid,  # 真实用户ID（数字）
                      'raw_userid': request.userid,  # 原始输入的用户名（如 li_bolu）
                      'start': start,
                      'end': end
                      }
            result = db.execute_query(sql, params)

            logger.info("处理查询结果...")

            if not result:
                logger.info("未查询到符合条件的会话记录")
                return ApiResponse(
                    data={
                        "sessions": [],
                        "page": page,
                        "page_size": page_size,
                        "total": 0,
                    }
                )

            logger.info(f"查询成功，共获取 {len(result)} 条记录")

            # 提取总记录数
            total = result[0].get('total', 0)

            # 按月份分组
            grouped_data = defaultdict(list)
            for row in result:
                month = row.get('MONTH')
                grouped_data[month].append({
                    "qa_id": row['qa_id'],
                    "chat_type": row['chat_type'],
                    "createtime": row['createtime'],
                    "title": row['title']
                })

            # 排序后输出
            formatted_sessions = [
                {"month": month, "session_list": sessions}
                for month, sessions in sorted(grouped_data.items(), reverse=True)
            ]

            return ApiResponse(
                data={
                    "sessions": formatted_sessions,
                    "page": page,
                    "page_size": page_size,
                    "total": total
                }
            )

    except Exception as e:
        logger.error(f"数据库操作异常: {e}", exc_info=True)
        return ApiResponse(
            code="500",
            msg="服务器内部错误",
            errorCode="5001",
            errorMsg="数据库操作异常",
            exception=str(e),
            data=None
        )


@router.post("/jiliang/qa_session_records_search", response_model=ApiResponse)
async def qa_session_records_search(request: SessionSearchRequest):
    """查询用户所有会话记录接口（支持图片问答搜索）"""
    try:
        # 参数校验提前返回
        if not request.userid:
            return ApiResponse(
                code="400",
                msg="参数校验失败",
                errorCode="1001",
                errorMsg="用户ID不能为空"
            )

        with DMDatabase(**dm_config) as db:
            # 获取真实 USER_ID
            real_userid = await get_real_userid(db, request.userid)
            if not real_userid:
                return ApiResponse(
                    code="400",
                    msg="参数校验失败",
                    errorCode="1002",
                    errorMsg=f"无效的用户ID或用户名不存在：{request.userid}"
                )
            # 分页参数处理
            page = max(1, request.page)
            page_size = min(100, max(1, request.page_size))
            start = (page - 1) * page_size + 1
            end = page * page_size

            sql_sessions = """
                SELECT
                    T1.QA_ID AS qa_id,
                    0 AS chat_type,
                    TO_CHAR(T1.CREATE_TIME, 'YYYY-MM-DD HH24:MI:SS') AS createtime,
                    T1.TITLE AS title
                FROM "SX_AIPLATFORM"."T_USER_CHAT_SESSION" T1
                WHERE T1.USER_ID = :real_userid
                  AND T1.DEL_FLAG = 0
                  AND T1.QA_ID IS NOT NULL
                  AND EXISTS (
                      SELECT 1
                      FROM "SX_AIPLATFORM"."T_USER_CHAT_MESSAGE" T2
                      WHERE T2.QA_ID = T1.QA_ID
                        AND T2.DEL_FLAG = 0
                        AND T2.CONTENT LIKE '%' || :keyword || '%'
                  )
            """

            sql_records = """
                SELECT
                    QA_ID AS qa_id,
                    sjr.CHAT_TYPE AS chat_type,
                    TO_CHAR(sjr.CREATETIME, 'YYYY-MM-DD HH24:MI:SS') AS createtime,
                    CASE
                        WHEN sjr.CHAT_TYPE = 2 THEN CONCAT('图片问答: ', SUBSTR(sjr.QUESTION, 1, 30))
                        ELSE sjr.QUESTION
                    END AS title
                FROM "SX_AIPLATFORM"."SX_JILIANG_RECORDS" sjr
                JOIN SX_AIPLATFORM.SYS_USER su ON sjr.userid = su.USER_NAME
                WHERE sjr.DEL_FLAG = 0
                  AND sjr.userid = :raw_userid
                  AND (
                      sjr.QUESTION LIKE '%' || :keyword || '%'
                      OR sjr.ANSWER LIKE '%' || :keyword || '%'
                  )
                  AND sjr.QA_ID IS NOT NULL
            """

            combined_sql = """
                WITH combined_data AS (
                    ({sql_sessions})
                    UNION
                    ({sql_records})
                ),
                deduped_data AS (
                    SELECT
                        *,
                        ROW_NUMBER() OVER (PARTITION BY qa_id ORDER BY createtime DESC) AS rn
                    FROM combined_data
                ),
                ranked_data AS (
                    SELECT
                        *,
                        ROW_NUMBER() OVER (ORDER BY createtime DESC) AS rn_total
                    FROM deduped_data
                    WHERE rn = 1
                ),
                aggregated_totals AS (
                    SELECT
                        *,
                        COUNT(*) OVER () AS total_results
                    FROM ranked_data
                )
                SELECT
                    qa_id,
                    chat_type,
                    createtime,
                    title,
                    total_results AS total
                FROM aggregated_totals
                WHERE rn_total BETWEEN :start AND :end
                ORDER BY createtime DESC
            """.format(sql_sessions=sql_sessions.strip(), sql_records=sql_records.strip())

            logger.info(f"执行语句：{combined_sql}")

            # 执行查询前日志记录
            logger.info("开始执行联合查询")

            # 参数拼接防注入
            params = {
                'real_userid': real_userid,
                'raw_userid': request.userid,
                'keyword': request.keyword or '',
                'start': start,
                'end': end
            }

            result = db.execute_query(combined_sql, params)
            logger.info("处理查询结果...")

            if not result:
                logger.info("未查询到符合条件的会话记录")
                return ApiResponse(
                    data={
                        "sessions": [],
                        "page": page,
                        "page_size": page_size,
                        "total": 0,
                    }
                )
            logger.info(f"查询成功，共获取 {len(result)} 条记录")

            # 处理结果
            session_list = [
                {
                    "qa_id": r['qa_id'],
                    "chat_type": r["chat_type"],
                    "createtime": r["createtime"],
                    "title": r["title"],
                } for r in result
            ]

            # 获取总数（所有记录中的任意一个都包含相同的total）
            total = result[0].get('total', 0) if result else 0

            return ApiResponse(
                data={
                    "sessions": session_list,
                    "total": total,
                    "page": page,
                    "page_size": page_size
                }
            )

    except Exception as e:
        logger.error(f"数据库操作异常: {e}", exc_info=True)
        return ApiResponse(
            code="500",
            msg="服务器内部错误",
            errorCode="5001",
            errorMsg="数据库操作异常",
            exception=str(e),
            data=None
        )



@router.post("/jiliang/qa_message_records", response_model=ApiResponse)
async def qa_session_records(request: MessageRecordsRequest):
    """查询用户会话详细记录"""
    conditions = ['"user_id" = :user_id', '"qa_id" = :qa_id', '"DEL_FLAG" = 0']
    conditions_jl = ['"userid" = :userid', '"qa_id" = :qa_id', '"DEL_FLAG" = 0']
    conditions_jl_session = ['t."userid" = :userid', 't."qa_id" = :qa_id', 't."DEL_FLAG" = 0']

    where_clause = " AND ".join(conditions)
    where_clause_jl = " AND ".join(conditions_jl)
    where_clause_jl_session = " AND ".join(conditions_jl_session)

    session_id_sql = f"""
        SELECT SESSION_ID 
        FROM "SX_AIPLATFORM"."t_user_chat_session"
        WHERE {where_clause}
    """

    jl_session_sql = f"""
        WITH ranked_records AS (
            SELECT 
                question,
                createtime,
                ROW_NUMBER() OVER (PARTITION BY qa_id, userid ORDER BY createtime) AS rn_asc
            FROM 
                SX_AIPLATFORM.SX_JILIANG_RECORDS
            WHERE {where_clause_jl}
        )
        SELECT 
            TO_CHAR(MIN(createtime),'YYYY-MM-DD"T"HH24:MI:SS"Z"') AS create_time,
            TO_CHAR(MAX(createtime),'YYYY-MM-DD"T"HH24:MI:SS"Z"') AS update_time,
            (SELECT question FROM ranked_records WHERE rn_asc = 1) AS title
        FROM 
            ranked_records;
    """

    jl_messages_sql = f"""
        WITH SplitRecords AS (
            SELECT 
                ROW_NUMBER() OVER (ORDER BY t."createtime") AS rn,
                t."id",
                t."question" AS "content",
                NULL AS "reasoning",
                'user' AS "role",
                TO_CHAR(t."createtime", 'YYYY-MM-DD"T"HH24:MI:SS"Z"') AS createtime
            FROM 
                SX_AIPLATFORM.SX_JILIANG_RECORDS t
            WHERE 
                {where_clause_jl_session}

            UNION ALL

            SELECT 
                ROW_NUMBER() OVER (ORDER BY t."createtime") + COUNT(*) OVER () AS rn,
                t."id",
                t."answer" AS "content",
                t."reasoning",
                'assistant' AS "role",
                TO_CHAR(t."createtime", 'YYYY-MM-DD"T"HH24:MI:SS"Z"') AS createtime
            FROM 
                SX_AIPLATFORM.SX_JILIANG_RECORDS t
            WHERE 
                {where_clause_jl_session}
          )
          SELECT * FROM SplitRecords
          ORDER BY "createtime", "role" DESC;
    """
    message_sql = """
        SELECT "role", "token_stats", 
               "reasoning", 
               CAST("message_id" AS VARCHAR) AS "message_id",
               "source",
               TO_CHAR("create_time", 'YYYY-MM-DD"T"HH24:MI:SS"Z"') AS create_time,
               "content"
        FROM "SX_AIPLATFORM"."t_user_chat_message"
        WHERE "session_id" = :session_id AND "ROLE" <> 'system' AND "DEL_FLAG" = 0
        ORDER BY "CREATE_TIME"
    """
    session_sql = """
        SELECT 
        TO_CHAR("create_time", 'YYYY-MM-DD"T"HH24:MI:SS"Z"') AS create_time, 
        TO_CHAR("update_time", 'YYYY-MM-DD"T"HH24:MI:SS"Z"') AS update_time, 
        "title"
        FROM "SX_AIPLATFORM"."t_user_chat_session"
        WHERE "session_id" = :session_id
    """

    try:
        # 参数校验
        if not request.userid or not request.qa_id:
            return ApiResponse(
                code="500",
                msg="参数校验失败",
                errorCode="1001",
                errorMsg="参数不能为空",
                data=None
            )
        with DMDatabase(**dm_config) as db:

            # 获取真实 USER_ID
            real_userid = await get_real_userid(db, request.userid)
            if not real_userid:
                return ApiResponse(
                    code="400",
                    msg="参数校验失败",
                    errorCode="1002",
                    errorMsg=f"无效的用户ID或用户名不存在：{request.userid}"
                )

            # 会话类问答
            # 获取会话类普通问答的session_id
            seesion_id = db.execute_query(session_id_sql, {"user_id": real_userid, "qa_id": request.qa_id})

            if seesion_id:
                seesion_id = seesion_id[0].get("SESSION_ID")
                seesion = db.execute_query(session_sql, {"session_id": seesion_id})
                messages = db.execute_query(message_sql, {"session_id": seesion_id})

                messages = [{**item, "token_stats": json.loads(item["token_stats"])
                if item.get("token_stats") and item["token_stats"] else {}}
                            for item in messages]

                return ApiResponse(
                    data={
                        "session": seesion[0],
                        "messages": messages
                    }
                )

            # 吉量类问答
            jl_session = db.execute_query(jl_session_sql, {"userid": request.userid, "qa_id": request.qa_id})

            jl_messages = db.execute_query(jl_messages_sql, {"userid": request.userid, "qa_id": request.qa_id})
            jl_messages_list = []
            jl_messages_dict = {}
            for message in jl_messages:
                jl_messages_dict = {
                    "role": message.get("role"),
                    "token_stats": "",
                    "reasoning": message.get("reasoning"),
                    "message_id": message.get("id"),
                    "source": "",
                    "create_time": message.get("createtime"),
                    "content": message.get("content")
                }
                jl_messages_list.append(jl_messages_dict)

            return ApiResponse(
                data={
                    "session": jl_session[0],
                    "messages": jl_messages_list
                }
            )
    except Exception as e:
        return ApiResponse(
            code="500",
            msg="服务器内部错误",
            errorCode="5001",
            errorMsg="数据库操作异常",
            exception=str(e)
        )


@router.post("/jiliang/qa_session_records_delete", response_model=ApiResponse)
async def delete_qa_session_records(request: SessionDeleteRequest):
    """删除用户会话记录接口（支持图片问答删除）"""
    conditions = ['"user_id" = :user_id', '"DEL_FLAG" = 0']
    conditions_jl = ['"userid" = :userid', '"DEL_FLAG" = 0']
    conditions_no_zsk = '"KNOWLEDGE_BASE_ID" IS NULL'
    conditions_zsk = '"KNOWLEDGE_BASE_ID" IS NOT NULL'

    session_id_sql = """
        SELECT SESSION_ID
        FROM "SX_AIPLATFORM"."t_user_chat_session"
        WHERE {}
    """
    delete_jl_sql = """
               UPDATE "SX_AIPLATFORM"."sx_jiliang_records"
               SET DEL_FLAG = 1
               WHERE {}
           """
    
    delete_message_sql = """
        UPDATE "SX_AIPLATFORM"."t_user_chat_message"
        SET DEL_FLAG = 1
        WHERE "DEL_FLAG" = 0 AND "session_id" = :session_id
    """

    delete_all_message_sql = """
        UPDATE "SX_AIPLATFORM"."t_user_chat_message"
        SET DEL_FLAG = 1
        WHERE session_id IN (
          SELECT session_id
          FROM "SX_AIPLATFORM"."t_user_chat_session"
          WHERE {}
        )
    """

    delete_session_sql = """
        UPDATE "SX_AIPLATFORM"."t_user_chat_session"
        SET DEL_FLAG = 1
        WHERE {}
    """

    try:
        # 参数校验
        if not request.userid:
            return ApiResponse(
                code="500",
                msg="参数校验失败",
                errorCode="1001",
                errorMsg="参数不能为空",
                data=None
            )
        with DMDatabase(**dm_config) as db:

            # 获取真实 USER_ID
            real_userid = await get_real_userid(db, request.userid)
            if not real_userid:
                return ApiResponse(
                    code="400",
                    msg="参数校验失败",
                    errorCode="1002",
                    errorMsg=f"无效的用户ID或用户名不存在：{request.userid}"
                )
            # 清空所有该用户的会话记录
            if request.delete_all:
                # 吉量类问答删除（包括图片问答）
                delete_jl_sql_conditions = ['"QA_ID" IS NOT NULL']
                delete_jl_sql_conditions.extend(conditions_jl)
                delete_jl_sql_where_clause = " AND ".join(delete_jl_sql_conditions)
                delete_jl_sql = delete_jl_sql.format(delete_jl_sql_where_clause)

                # 会话类问答删除
                delete_message_sql_where_clause = " AND ".join(conditions)
                delete_all_message_sql = delete_all_message_sql.format(delete_message_sql_where_clause)

                delete_session_sql_where_clause = " AND ".join(conditions)
                delete_session_sql = delete_session_sql.format(delete_session_sql_where_clause)

                update_info0 = db.execute_update(delete_jl_sql, {"userid": request.userid})
                update_info1 = db.execute_update(delete_all_message_sql, {"user_id": real_userid})
                update_info2 = db.execute_update(delete_session_sql, {"user_id": real_userid})
                logger.info(
                    "【userid:{},chat_type:{},qa_id{}】清空所有会话记录成功".format(request.userid, request.chat_type,
                                                                                  request.qa_id))
            else:
                # 删除指定会话记录
                if request.chat_type is None or not request.qa_id:
                    return ApiResponse(
                        code="500",
                        msg="参数校验失败",
                        errorCode="1001",
                        errorMsg="参数不能为空",
                        data=None
                    )
                if request.chat_type == 0:
                    # 会话类普通问答删除（含固定知识库）
                    session_id_sql_conditions = [conditions_no_zsk, '"qa_id" = :qa_id']
                    session_id_sql_conditions.extend(conditions)
                    session_id_sql_where_clause = " AND ".join(session_id_sql_conditions)
                    session_id_sql = session_id_sql.format(session_id_sql_where_clause)
                    # 获取会话类普通问答的session_id
                    seesion_id = db.execute_query(session_id_sql, {"user_id": real_userid, "qa_id": request.qa_id})

                    if seesion_id:
                        seesion_id = seesion_id[0].get("SESSION_ID")

                        # 会话记录删除
                        delete_session_sql_conditions = ['"session_id" = :session_id']
                        delete_session_sql_conditions.extend(conditions)
                        delete_session_sql_where_clause = " AND ".join(delete_session_sql_conditions)
                        delete_session_sql = delete_session_sql.format(delete_session_sql_where_clause)

                        update_info1 = db.execute_update(delete_message_sql,
                                                         {"session_id": seesion_id})
                        update_info2 = db.execute_update(delete_session_sql,
                                                         {"user_id": real_userid, "session_id": seesion_id})
                        logger.info(
                            "【userid:{},chat_type:{},qa_id{}】删除知识库类普通问答{}条记录成功（详细记录{}条）".format(
                                real_userid, request.chat_type, request.qa_id, update_info2, update_info1))
                    else:
                        # 吉量类普通问答删除
                        delete_jl_sql_conditions = ['"chat_type" = :chat_type', '"qa_id" = :qa_id']
                        delete_jl_sql_conditions.extend(conditions_jl)
                        delete_jl_sql_where_clause = " AND ".join(delete_jl_sql_conditions)
                        delete_jl_sql = delete_jl_sql.format(delete_jl_sql_where_clause)

                        update_info0 = db.execute_update(delete_jl_sql,
                                                         {"userid": request.userid,
                                                          "chat_type": request.chat_type,
                                                          "qa_id": request.qa_id})
                        logger.info(
                            "【userid:{},chat_type:{},qa_id{}】删除吉量类普通问答{}条记录成功".format(request.userid,
                                                                                                    request.chat_type,
                                                                                                    request.qa_id,
                                                                                                    update_info0))

                elif request.chat_type == 1:
                    # 吉量类文件问答删除
                    delete_jl_sql_conditions = ['"chat_type" = :chat_type', '"qa_id" = :qa_id']
                    delete_jl_sql_conditions.extend(conditions_jl)
                    delete_jl_sql_where_clause = " AND ".join(delete_jl_sql_conditions)
                    delete_jl_sql = delete_jl_sql.format(delete_jl_sql_where_clause)

                    update_info0 = db.execute_update(delete_jl_sql,
                                                     {"userid": request.userid, "chat_type": request.chat_type,
                                                      "qa_id": request.qa_id})
                    logger.info("【userid:{},chat_type:{},qa_id{}】删除吉量类文件问答{}条记录成功".format(request.userid,
                                                                                                        request.chat_type,
                                                                                                        request.qa_id,
                                                                                                        update_info0))
                elif request.chat_type == 2:
                    # 会话类知识库删除
                    session_id_sql_conditions = [conditions_zsk, '"qa_id" = :qa_id']
                    session_id_sql_conditions.extend(conditions)
                    session_id_sql_where_clause = " AND ".join(session_id_sql_conditions)
                    session_id_sql = session_id_sql.format(session_id_sql_where_clause)
                    # 获取会话类知识库问答的session_id
                    seesion_id = db.execute_query(session_id_sql, {"user_id": real_userid, "qa_id": request.qa_id})

                    if seesion_id:
                        seesion_id = seesion_id[0].get("SESSION_ID")

                        # 会话记录删除
                        delete_session_sql_conditions = ['"session_id" = :session_id']
                        delete_session_sql_conditions.extend(conditions)
                        delete_session_sql_where_clause = " AND ".join(delete_session_sql_conditions)
                        delete_session_sql = delete_session_sql.format(delete_session_sql_where_clause)

                        update_info0 = db.execute_update(delete_message_sql,
                                                     {"session_id": seesion_id})
                        update_info1 = db.execute_update(delete_session_sql,
                                                     {"user_id": real_userid, "session_id": seesion_id})
                        logger.info("【userid:{},chat_type:{},qa_id{}】删除知识库类问答{}条记录成功（详细记录{}条）".format(
                        real_userid, request.chat_type, request.qa_id, update_info1, update_info0))
                
                elif request.chat_type == 2:
                    # 吉量类图片问答删除
                    delete_jl_sql_conditions = ['"chat_type" = :chat_type', '"qa_id" = :qa_id']
                    delete_jl_sql_conditions.extend(conditions_jl)
                    delete_jl_sql_where_clause = " AND ".join(delete_jl_sql_conditions)
                    delete_jl_sql = delete_jl_sql.format(delete_jl_sql_where_clause)
                    
                    update_info0 = db.execute_update(delete_jl_sql,
                                                     {"userid": request.userid, "chat_type": request.chat_type,
                                                      "qa_id": request.qa_id})
                    logger.info("【userid:{},chat_type:{},qa_id{}】删除吉量类图片问答{}条记录成功".format(request.userid,
                                                                                                        request.chat_type,
                                                                                                        request.qa_id,
                                                                                                        update_info0))
            db.commit()
            return ApiResponse()
    except Exception as e:
        if 'db' in locals():
            db.rollback()
        return ApiResponse(
            code="500",
            msg="服务器内部错误",
            errorCode="5001",
            errorMsg="数据库操作异常",
            exception=str(e)
        )


@router.post("/jiliang/qa_message_records_delete", response_model=ApiResponse)
async def delete_qa_message_records(request: MessageDeleteRequest):
    """删除用户会话记录中指定的对话内容（支持图片问答）"""
    # 查询条件
    conditions = ['"user_id" = :user_id', '"DEL_FLAG" = 0']
    conditions_jl = ['"userid" = :userid', '"DEL_FLAG" = 0']
    conditions_no_zsk = '"KNOWLEDGE_BASE_ID" IS NULL'
    conditions_zsk = '"KNOWLEDGE_BASE_ID" IS NOT NULL'

    session_id_sql = """
        SELECT SESSION_ID
        FROM "SX_AIPLATFORM"."t_user_chat_session"
        WHERE {}
    """
    delete_jl_sql = """
               UPDATE "SX_AIPLATFORM"."sx_jiliang_records"
               SET DEL_FLAG = 1
               WHERE {}
           """
    delete_message_sql = """
        UPDATE "SX_AIPLATFORM"."t_user_chat_message"
        SET DEL_FLAG = 1
        WHERE {}
    """

    try:
        # 参数校验
        if not request.userid or not request.qa_id or request.chat_type is None:
            return ApiResponse(
                code="500",
                msg="参数校验失败",
                errorCode="1001",
                errorMsg="参数不能为空",
                data=None
            )
        with DMDatabase(**dm_config) as db:

            # 获取真实 USER_ID
            real_userid = await get_real_userid(db, request.userid)
            if not real_userid:
                return ApiResponse(
                    code="400",
                    msg="参数校验失败",
                    errorCode="1002",
                    errorMsg=f"无效的用户ID或用户名不存在：{request.userid}"
                )
            # 获取需要删除的会话ID
            ids_to_delete = []
            if request.message_id and isinstance(request.message_id, str):
                # 单条对话
                ids_to_delete = [request.message_id]
            elif request.message_ids and isinstance(request.message_ids, list):
                # 多条对话
                ids_to_delete = request.message_ids
            if len(ids_to_delete) != 0:
                ids_placeholders = ', '.join(ids_to_delete)
                # 吉量类
                ids_to_delete_condition = '"id" IN ({})'.format(ids_placeholders)
                # 会话类
                message_ids_to_delete_condition = '"message_id" IN ({})'.format(ids_placeholders)

                # 删除指定会话记录
                if request.chat_type == 0:
                    # 会话类普通问答删除（含固定知识库）
                    session_id_sql_conditions = [conditions_no_zsk, '"qa_id" = :qa_id']
                    session_id_sql_conditions.extend(conditions)
                    session_id_sql_where_clause = " AND ".join(session_id_sql_conditions)
                    session_id_sql = session_id_sql.format(session_id_sql_where_clause)
                    # 获取会话类普通问答的session_id
                    seesion_id = db.execute_query(session_id_sql, {"user_id": real_userid, "qa_id": request.qa_id})

                    if seesion_id:
                        seesion_id = seesion_id[0].get("SESSION_ID")

                        # 详细记录删除
                        delete_message_sql_conditions = ['"session_id" = :session_id', message_ids_to_delete_condition]
                        delete_message_sql_where_clause = " AND ".join(delete_message_sql_conditions)
                        delete_message_sql = delete_message_sql.format(delete_message_sql_where_clause)

                        update_info1 = db.execute_update(delete_message_sql,
                                                         { "session_id": seesion_id})
                        logger.info(
                            "【userid:{},chat_type:{},qa_id{},message_ids:{}】删除知识库类普通问答{}条记录成功".format(
                                real_userid, request.chat_type, request.qa_id,
                                ids_placeholders, update_info1))
                    else:
                        # 吉量类普通问答删除
                        delete_jl_sql_conditions = ['"chat_type" = :chat_type', '"qa_id" = :qa_id',
                                                    ids_to_delete_condition]
                        delete_jl_sql_conditions.extend(conditions_jl)
                        delete_jl_sql_where_clause = " AND ".join(delete_jl_sql_conditions)
                        delete_jl_sql = delete_jl_sql.format(delete_jl_sql_where_clause)
                        update_info0 = db.execute_update(delete_jl_sql,
                                                         {"userid": request.userid, "chat_type": request.chat_type,
                                                          "qa_id": request.qa_id})
                        logger.info(
                            "【userid:{},chat_type:{},qa_id{},message_ids:{}】删除吉量类普通问答{}条记录成功".format(
                                request.userid, request.chat_type, request.qa_id,
                                ids_placeholders, update_info0))

                elif request.chat_type == 1:
                    # 吉量类文件问答删除
                    delete_jl_sql_conditions = ['"chat_type" = :chat_type', '"qa_id" = :qa_id', ids_to_delete_condition]
                    delete_jl_sql_conditions.extend(conditions_jl)
                    delete_jl_sql_where_clause = " AND ".join(delete_jl_sql_conditions)
                    delete_jl_sql = delete_jl_sql.format(delete_jl_sql_where_clause)

                    update_info0 = db.execute_update(delete_jl_sql,
                                                     {"userid": request.userid, "chat_type": request.chat_type,
                                                      "qa_id": request.qa_id})
                    logger.info("【userid:{},chat_type:{},qa_id{},message_ids:{}】删除吉量类文件问答{}条记录成功".format(
                        request.userid, request.chat_type, request.qa_id,
                        ids_placeholders, update_info0))
                elif request.chat_type == 2:
                    # 会话类知识库删除
                    session_id_sql_conditions = [conditions_zsk, '"qa_id" = :qa_id']
                    session_id_sql_conditions.extend(conditions)
                    session_id_sql_where_clause = " AND ".join(session_id_sql_conditions)
                    session_id_sql = session_id_sql.format(session_id_sql_where_clause)
                    # 获取会话类知识库问答的session_id
                    seesion_id = db.execute_query(session_id_sql, {"user_id": real_userid, "qa_id": request.qa_id})
                    if seesion_id:
                        seesion_id = seesion_id[0].get("SESSION_ID")

                        # 详细记录删除
                        delete_message_sql_conditions = ['"session_id" = :session_id', message_ids_to_delete_condition]
                        delete_message_sql_where_clause = " AND ".join(delete_message_sql_conditions)
                        delete_message_sql = delete_message_sql.format(delete_message_sql_where_clause)

                        update_info0 = db.execute_update(delete_message_sql, {"session_id": seesion_id})
                        logger.info("【userid:{},chat_type:{},qa_id{},message_ids:{}】删除知识库类问答{}条记录成功".format(
                            real_userid, request.chat_type, request.qa_id,
                            ids_placeholders, update_info0))
                
                elif request.chat_type == 2:
                    # 吉量类图片问答删除
                    delete_jl_sql_conditions = ['"chat_type" = :chat_type', '"qa_id" = :qa_id', ids_to_delete_condition]
                    delete_jl_sql_conditions.extend(conditions_jl)
                    delete_jl_sql_where_clause = " AND ".join(delete_jl_sql_conditions)
                    delete_jl_sql = delete_jl_sql.format(delete_jl_sql_where_clause)
                    
                    update_info0 = db.execute_update(delete_jl_sql,
                                                     {"userid": request.userid, "chat_type": request.chat_type,
                                                      "qa_id": request.qa_id})
                    logger.info("【userid:{},chat_type:{},qa_id{},message_ids:{}】删除吉量类图片问答{}条记录成功".format(
                        request.userid, request.chat_type, request.qa_id,
                        ids_placeholders, update_info0))
            db.commit()
            return ApiResponse()
    except Exception as e:
        if 'db' in locals():
            db.rollback()
        return ApiResponse(
            code="500",
            msg="服务器内部错误",
            errorCode="5001",
            errorMsg="数据库操作异常",
            exception=str(e)
        )
