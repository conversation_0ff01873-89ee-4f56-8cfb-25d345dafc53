import os
import logging
import json
from dotenv import load_dotenv
from redis import Redis
from redis.exceptions import RedisError, ConnectionError, TimeoutError
from typing import Optional, Union, List, Dict, Any

# 配置日志
logger = logging.getLogger(__name__)

# 加载环境变量
load_dotenv()


class RedisClient:
  """
    Redis 客户端工具类（同时支持集群和单点模式），增加了详细的日志记录

    配置说明：
    REDIS_MODE: cluster/single (必填)
    REDIS_HOSTS: 集群模式用逗号分隔 host:port (如 "host1:6379,host2:6379")
                单点模式直接写 host:port (如 "localhost:6379")
    REDIS_PASSWORD: 密码 (可选)
    REDIS_DB: 单点模式使用的数据库 (默认 0)
    REDIS_MAX_CONNECTIONS: 连接池大小 (默认 32)
    """
  
  def __init__(self):
    try:
      self.mode = os.getenv("REDIS_MODE", "single")
      logger.info(f"初始化Redis客户端 - 模式: {self.mode}")
      
      # 记录连接参数
      self.hosts_str = os.getenv("REDIS_HOSTS", "")
      self.password = os.getenv("REDIS_PASSWORD")
      self.db = int(os.getenv("REDIS_DB", 0))
      self.max_connections = int(os.getenv("REDIS_MAX_CONNECTIONS", 32))
      
      # 记录隐藏密码的连接信息
      masked_password = "***" if self.password else "无"
      logger.info(
        f"Redis连接参数 - 主机: {self.hosts_str}, 数据库: {self.db}, 密码: {masked_password}, 最大连接数: {self.max_connections}")
      
      # 初始化连接
      if self.mode == "cluster":
        logger.info("正在初始化Redis集群连接...")
        self._init_cluster()
        logger.info("Redis集群连接初始化完成")
      else:
        logger.info("正在初始化Redis单点连接...")
        self._init_single()
        logger.info("Redis单点连接初始化完成")
      
      # 测试连接
      self._test_connection()
    except Exception as e:
      logger.error(f"Redis客户端初始化失败: {e}")
      raise
  
  def _parse_hosts(self) -> List[Dict[str, Union[str, int]]]:
    """解析主机列表"""
    hosts = []
    raw_hosts = self.hosts_str.split(",")
    logger.info(f"解析主机列表: {raw_hosts}")
    
    for host in raw_hosts:
      if ":" in host:
        h, p = host.strip().split(":")
        hosts.append({"host": h, "port": int(p)})
        logger.info(f"解析到主机: {h}:{p}")
    
    if not hosts:
      error_msg = "无效的REDIS_HOSTS配置，无法解析任何主机"
      logger.error(error_msg)
      raise ValueError(error_msg)
    
    logger.info(f"成功解析 {len(hosts)} 个Redis主机")
    return hosts
  
  def _init_cluster(self) -> None:
    """初始化集群客户端"""
    logger.error("Redis集群模式当前未实现，请使用单点模式")
    raise NotImplementedError("Redis集群模式当前未实现")
  
  def _init_single(self) -> None:
    """初始化单点客户端"""
    try:
      host_config = self._parse_hosts()[0]
      host = host_config["host"]
      port = host_config["port"]
      
      logger.info(f"初始化Redis单点连接 - 主机: {host}, 端口: {port}, 数据库: {self.db}")
      
      self.client = Redis(
        host=host,
        port=port,
        password=self.password,
        db=self.db,
        decode_responses=True,
        max_connections=self.max_connections,
        retry_on_timeout=True,
        socket_timeout=5.0,  # 添加明确的超时设置
        socket_connect_timeout=3.0,  # 连接超时
      )
      
      logger.info(f"Redis单点客户端初始化完成 - {host}:{port}, db={self.db}")
    except Exception as e:
      logger.error(f"初始化Redis单点客户端失败: {e}")
      raise
  
  def _test_connection(self) -> None:
    """测试Redis连接"""
    try:
      logger.info("正在测试Redis连接...")
      result = self.client.ping()
      logger.info(f"Redis连接测试成功: {result}")
    except ConnectionError as e:
      logger.error(f"Redis连接测试失败 - 连接错误: {e}")
      raise
    except TimeoutError as e:
      logger.error(f"Redis连接测试失败 - 连接超时: {e}")
      raise
    except Exception as e:
      logger.error(f"Redis连接测试失败 - 未知错误: {e}")
      raise
  
  def _get_connection_details(self) -> str:
    """获取连接详情用于日志"""
    if self.mode == "single":
      try:
        host_config = self._parse_hosts()[0]
        return f"单点模式 - 主机:{host_config['host']} 端口:{host_config['port']} 数据库:{self.db}"
      except:
        return f"单点模式 - 主机配置:{self.hosts_str} 数据库:{self.db}"
    else:
      return f"集群模式 - 主机配置:{self.hosts_str}"
  
  def get(self, key: str) -> Optional[str]:
    """获取Redis键值"""
    try:
      logger.info(f"正在获取Redis键: {key}")
      start_time = self._get_time()
      
      result = self.client.get(key)
      
      end_time = self._get_time()
      duration = end_time - start_time
      
      if result:
        logger.info(f"成功获取Redis键: {key} (耗时: {duration:.4f}秒)")
        return result
      
      logger.info(f"Redis键不存在: {key} (耗时: {duration:.4f}秒)")
      return None
    
    except ConnectionError as e:
      logger.error(f"Redis获取失败 - 连接错误: {e} (键: {key})")
      return None
    except TimeoutError as e:
      logger.error(f"Redis获取失败 - 连接超时: {e} (键: {key})")
      return None
    except Exception as e:
      logger.error(f"Redis获取失败 - 未知错误: {e} (键: {key})")
      return None
  
  def set(self, key: str, value: Any, ex: Optional[int] = None) -> bool:
    """设置Redis键值，可选过期时间"""
    try:
      value_length = len(str(value)) if value else 0
      logger.info(f"正在设置Redis键: {key} (长度: {value_length}, 过期时间: {ex}秒)")
      start_time = self._get_time()
      
      result = self.client.set(key, value, ex=ex)
      
      end_time = self._get_time()
      duration = end_time - start_time
      
      logger.info(f"设置Redis键完成: {key} (耗时: {duration:.4f}秒, 结果: {result})")
      return result
    
    except ConnectionError as e:
      logger.error(f"Redis设置失败 - 连接错误: {e} (键: {key})")
      raise RedisOperationError(f"set操作失败 - 连接错误: {e}") from e
    except TimeoutError as e:
      logger.error(f"Redis设置失败 - 连接超时: {e} (键: {key})")
      raise RedisOperationError(f"set操作失败 - 连接超时: {e}") from e
    except Exception as e:
      logger.error(f"Redis设置失败 - 未知错误: {e} (键: {key})")
      raise RedisOperationError(f"set操作失败: {e}") from e
  
  def delete(self, *keys: str) -> int:
    """删除Redis键"""
    try:
      logger.info(f"正在删除Redis键: {keys}")
      start_time = self._get_time()
      
      result = self.client.delete(*keys)
      
      end_time = self._get_time()
      duration = end_time - start_time
      
      logger.info(f"删除Redis键完成: {keys} (耗时: {duration:.4f}秒, 结果: {result})")
      return result
    
    except Exception as e:
      logger.error(f"Redis删除失败: {e} (键: {keys})")
      return 0
  
  def exists(self, key: str) -> bool:
    """检查Redis键是否存在"""
    try:
      return self.client.exists(key) == 1
    except Exception as e:
      logger.error(f"Redis exists操作失败: {e} (键: {key})")
      return False
  
  def setex(self, key: str, time: int, value: Any) -> bool:
    """
        设置键值并指定过期时间

        Args:
            key: 键名
            time: 过期时间（秒）
            value: 值
        Returns:
            bool: 操作是否成功
        """
    try:
      # 记录详细的操作信息
      value_str = str(value) if isinstance(value, str) else json.dumps(value, ensure_ascii=False)
      value_length = len(value_str)
      truncated_value = value_str[:100] + "..." if len(value_str) > 100 else value_str
      
      
      # 详细记录连接信息
      connection_info = self._get_connection_details()
      logger.info(f"Redis连接信息: {connection_info}")
      
      # 记录操作时间
      start_time = self._get_time()
      
      # 执行操作
      result = self.client.setex(key, time, value)
      
      # 计算耗时
      end_time = self._get_time()
      duration = end_time - start_time

      return result
    
    except ConnectionError as e:
      logger.error(f"Redis setex操作失败 - 连接错误: {e}")
      raise RedisOperationError(f"setex操作失败 - 连接错误: {e}") from e
    
    except TimeoutError as e:
      logger.error(f"Redis setex操作失败 - 连接超时: {e}")
      raise RedisOperationError(f"setex操作失败 - 连接超时: {e}") from e
    
    except Exception as e:
      logger.error(f"Redis setex操作失败 - 未知错误: {e}")
      raise RedisOperationError(f"setex操作失败: {e}") from e
  
  def _get_time(self):
    """获取当前时间，用于性能计时"""
    import time
    return time.time()
  
  def pipeline(self):
    """获取管道对象（注意：集群模式管道仅支持单节点操作）"""
    return self.client.pipeline()
  
  def close(self) -> None:
    """关闭连接"""
    try:
      logger.info("正在关闭Redis客户端连接...")
      self.client.close()
      logger.info("Redis客户端连接已关闭")
    except Exception as e:
      logger.error(f"关闭Redis客户端连接失败: {e}")


class RedisOperationError(Exception):
  """自定义 Redis 操作异常"""
  pass