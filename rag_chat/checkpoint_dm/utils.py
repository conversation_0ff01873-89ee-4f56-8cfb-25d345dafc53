"""达梦数据库 Checkpointer 修复后的工具函数

修复了原有工具函数中的问题，并添加了新的实用功能。
"""

import logging
import time
from contextlib import contextmanager
from functools import wraps
from typing import Any, Dict, Optional, Union

from .dm_exceptions import (
    DMCheckpointError,
    DMConnectionError,
    DMTableError,
    DMSerializationError,
    DMConfigError,
    DMTransactionError,
)

logger = logging.getLogger(__name__)


def safe_get_field(row: Dict[str, Any], field_name: str, default: Any = None) -> Any:
    """安全获取数据库字段值，兼容大小写
    
    达梦数据库可能返回大写或小写的字段名，这个函数能兼容处理。
    
    Args:
        row: 数据库查询结果行
        field_name: 字段名（小写）
        default: 默认值
        
    Returns:
        字段值或默认值
    """
    # 先尝试大写
    upper_field = field_name.upper()
    if upper_field in row:
        return row[upper_field]
    
    # 再尝试小写
    if field_name in row:
        return row[field_name]
    
    # 最后返回默认值
    return default


def validate_dm_config(config: Dict[str, Any]) -> Dict[str, Any]:
    """验证达梦数据库配置
    
    Args:
        config: 数据库配置字典
        
    Returns:
        验证并补充默认值后的配置
        
    Raises:
        DMConfigError: 配置验证失败
    """
    required_fields = ['host', 'port', 'user', 'password', 'database']
    
    for field in required_fields:
        if field not in config or config[field] is None:
            raise DMConfigError(f"Missing required config field: {field}")
    
    # 设置默认值
    validated_config = {
        'host': str(config['host']),
        'port': int(config['port']),
        'user': str(config['user']),
        'password': str(config['password']),
        'database': str(config['database']),
        'auto_commit': config.get('auto_commit', False),
        'timeout': config.get('timeout', 30),
        'charset': config.get('charset', 'UTF-8'),
    }
    
    # 验证端口范围
    if not (1 <= validated_config['port'] <= 65535):
        raise DMConfigError(f"Invalid port number: {validated_config['port']}")
    
    return validated_config


def parse_dm_conn_string(conn_string: str) -> Dict[str, Any]:
    """解析达梦数据库连接字符串
    
    支持格式：
    - dm://user:password@host:port/database
    - dm://user:password@host/database (默认端口5236)
    
    Args:
        conn_string: 连接字符串
        
    Returns:
        解析后的配置字典
        
    Raises:
        DMConfigError: 连接字符串格式错误
    """
    import urllib.parse as urlparse
    
    try:
        parsed = urlparse.urlparse(conn_string)
        
        if parsed.scheme.lower() not in ['dm', 'dameng']:
            raise DMConfigError(f"Unsupported scheme: {parsed.scheme}")
        
        if not parsed.hostname:
            raise DMConfigError("Missing hostname in connection string")
            
        if not parsed.username:
            raise DMConfigError("Missing username in connection string")
            
        if not parsed.password:
            raise DMConfigError("Missing password in connection string")
            
        database = parsed.path.lstrip('/')
        if not database:
            raise DMConfigError("Missing database name in connection string")
        
        config = {
            'host': parsed.hostname,
            'port': parsed.port or 5236,
            'user': parsed.username,
            'password': parsed.password,
            'database': database,
        }
        
        # 解析查询参数
        query_params = urlparse.parse_qs(parsed.query)
        for key, values in query_params.items():
            if values:
                # 处理布尔值
                if key == 'auto_commit':
                    config[key] = values[0].lower() in ('true', '1', 'yes')
                # 处理数值
                elif key in ('timeout', 'port'):
                    config[key] = int(values[0])
                else:
                    config[key] = values[0]
        
        return validate_dm_config(config)
        
    except Exception as e:
        if isinstance(e, DMConfigError):
            raise
        raise DMConfigError(f"Failed to parse connection string: {e}")


def retry_on_error(max_retries: int = 3, delay: float = 1.0, backoff: float = 2.0):
    """重试装饰器
    
    Args:
        max_retries: 最大重试次数
        delay: 初始延迟时间（秒）
        backoff: 延迟时间倍数
    """
    def decorator(func):
        @wraps(func)
        def wrapper(*args, **kwargs):
            last_exception = None
            current_delay = delay
            
            for attempt in range(max_retries + 1):
                try:
                    return func(*args, **kwargs)
                except (DMConnectionError, DMTransactionError) as e:
                    last_exception = e
                    if attempt < max_retries:
                        logger.warning(
                            f"Attempt {attempt + 1} failed, retrying in {current_delay}s: {e}"
                        )
                        time.sleep(current_delay)
                        current_delay *= backoff
                    else:
                        logger.error(f"All {max_retries + 1} attempts failed")
                        break
                except Exception as e:
                    # 非网络相关错误，不重试
                    logger.error(f"Non-retryable error: {e}")
                    raise
            
            raise last_exception
        
        return wrapper
    return decorator


@contextmanager
def dm_error_handler(operation_name: str):
    """达梦数据库错误处理上下文管理器
    
    Args:
        operation_name: 操作名称，用于日志记录
    """
    try:
        yield
    except Exception as e:
        error_msg = f"{operation_name} failed: {str(e)}"
        logger.error(error_msg, exc_info=True)
        
        # 将原始异常转换为自定义异常
        error_str = str(e).lower()
        if any(keyword in error_str for keyword in ["connection", "network", "timeout", "connect"]):
            raise DMConnectionError(error_msg) from e
        elif any(keyword in error_str for keyword in ["table", "column", "index", "constraint"]):
            raise DMTableError(error_msg) from e
        elif any(keyword in error_str for keyword in ["serializ", "json", "decode", "encode"]):
            raise DMSerializationError(error_msg) from e
        elif any(keyword in error_str for keyword in ["transaction", "commit", "rollback"]):
            raise DMTransactionError(error_msg) from e
        else:
            raise DMCheckpointError(error_msg) from e


def log_performance(func):
    """性能日志装饰器"""
    @wraps(func)
    def wrapper(*args, **kwargs):
        start_time = time.time()
        try:
            result = func(*args, **kwargs)
            execution_time = time.time() - start_time
            logger.debug(f"{func.__name__} executed in {execution_time:.3f}s")
            return result
        except Exception as e:
            execution_time = time.time() - start_time
            logger.warning(f"{func.__name__} failed after {execution_time:.3f}s: {e}")
            raise
    
    return wrapper


def sanitize_identifier(identifier: str) -> str:
    """清理数据库标识符
    
    Args:
        identifier: 原始标识符
        
    Returns:
        清理后的标识符
    """
    if not identifier:
        return "default_identifier"
    
    # 移除或替换特殊字符
    import re
    sanitized = re.sub(r'[^\w]', '_', str(identifier))
    
    # 确保不以数字开头
    if sanitized and sanitized[0].isdigit():
        sanitized = f"_{sanitized}"
    
    # 限制长度
    max_length = 128
    if len(sanitized) > max_length:
        sanitized = sanitized[:max_length]
    
    return sanitized


def format_checkpoint_id(checkpoint_id: Optional[Union[str, int]]) -> Optional[str]:
    """格式化 checkpoint ID
    
    Args:
        checkpoint_id: 原始 checkpoint ID
        
    Returns:
        格式化后的 checkpoint ID
    """
    if checkpoint_id is None:
        return None
    
    # 确保是字符串格式
    return str(checkpoint_id)


def build_config_key(*parts: Union[str, int, None]) -> str:
    """构建配置键
    
    Args:
        *parts: 配置键的各个部分
        
    Returns:
        组合后的配置键
    """
    valid_parts = [str(part) for part in parts if part is not None and str(part).strip()]
    return ":".join(valid_parts) if valid_parts else "default"


def estimate_storage_size(data: Any) -> int:
    """估算数据存储大小（字节）
    
    Args:
        data: 要估算的数据
        
    Returns:
        估算的字节数
    """
    import sys
    
    try:
        if hasattr(data, '__sizeof__'):
            return data.__sizeof__()
        elif isinstance(data, str):
            return len(data.encode('utf-8'))
        elif isinstance(data, (list, tuple)):
            return sum(estimate_storage_size(item) for item in data)
        elif isinstance(data, dict):
            return sum(
                estimate_storage_size(k) + estimate_storage_size(v) 
                for k, v in data.items()
            )
        else:
            return sys.getsizeof(data)
    except Exception:
        # 如果估算失败，返回一个合理的默认值
        return len(str(data).encode('utf-8', errors='ignore'))


def check_dm_version_compatibility() -> bool:
    """检查达梦数据库版本兼容性
    
    Returns:
        是否兼容
    """
    try:
        import dmPython
        # 检查 dmPython 是否可用
        return hasattr(dmPython, 'connect')
    except ImportError:
        logger.error("dmPython 模块未安装或不可用")
        return False


def create_safe_table_name(base_name: str, max_length: int = 30) -> str:
    """创建安全的表名
    
    Args:
        base_name: 基础表名
        max_length: 最大长度
        
    Returns:
        安全的表名
    """
    if not base_name:
        base_name = "table"
    
    # 清理表名
    safe_name = sanitize_identifier(base_name)
    
    # 限制长度
    if len(safe_name) > max_length:
        safe_name = safe_name[:max_length]
    
    # 确保不与保留字冲突
    reserved_words = {
        'user', 'table', 'index', 'column', 'constraint', 'primary', 'foreign',
        'key', 'references', 'check', 'unique', 'not', 'null', 'default'
    }
    
    if safe_name.lower() in reserved_words:
        safe_name = f"tbl_{safe_name}"
    
    return safe_name


class DMPerformanceMonitor:
    """达梦数据库性能监控器"""
    
    def __init__(self):
        self.stats = {
            'queries': 0,
            'total_time': 0,
            'errors': 0,
            'slow_queries': 0
        }
        self.slow_query_threshold = 1.0  # 秒
    
    def record_query(self, execution_time: float, success: bool = True):
        """记录查询性能
        
        Args:
            execution_time: 执行时间（秒）
            success: 是否成功
        """
        self.stats['queries'] += 1
        self.stats['total_time'] += execution_time
        
        if not success:
            self.stats['errors'] += 1
        
        if execution_time > self.slow_query_threshold:
            self.stats['slow_queries'] += 1
            logger.warning(f"Slow query detected: {execution_time:.3f}s")
    
    def get_stats(self) -> Dict[str, Any]:
        """获取性能统计
        
        Returns:
            性能统计字典
        """
        avg_time = (self.stats['total_time'] / self.stats['queries'] 
                   if self.stats['queries'] > 0 else 0)
        
        return {
            **self.stats,
            'average_time': avg_time,
            'success_rate': ((self.stats['queries'] - self.stats['errors']) / 
                           self.stats['queries'] if self.stats['queries'] > 0 else 0)
        }
    
    def reset_stats(self):
        """重置统计信息"""
        self.stats = {
            'queries': 0,
            'total_time': 0,
            'errors': 0,
            'slow_queries': 0
        }


# 全局性能监控器实例
performance_monitor = DMPerformanceMonitor()