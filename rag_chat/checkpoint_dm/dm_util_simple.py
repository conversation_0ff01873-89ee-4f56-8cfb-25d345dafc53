"""
简化版达梦数据库工具类

通过正确的编码设置解决checkpoint存储问题。
主要修复：使用local_code=1参数指定UTF-8编码
"""

import dmPython
from typing import List, Tuple, Optional, Dict, Any
import logging

logger = logging.getLogger(__name__)


class DMDatabaseSimple:
    def __init__(self,
                 host: str,
                 port: int,
                 user: str,
                 password: str,
                 database: str,
                 auto_commit: bool = False):
        """
        简化版达梦数据库工具类构造函数

        :param host: 数据库地址
        :param port: 端口号（默认5236）
        :param user: 用户名
        :param password: 密码
        :param database: 数据库名
        :param auto_commit: 是否自动提交事务（默认False）
        """
        self.config = {
            'host': host,
            'port': port,
            'user': user,
            'password': password,
            'database': database,
            'auto_commit': auto_commit
        }
        self.conn: Optional[dmPython.Connection] = None
        self.cursor: Optional[dmPython.Cursor] = None

    def __enter__(self):
        self.connect()
        return self

    def __exit__(self, exc_type, exc_val, exc_tb):
        self.close()

    def connect(self):
        """建立数据库连接，优化BLOB字段处理"""
        # 根据网络搜索结果，使用多种连接策略来处理编码问题
        connection_strategies = [
             # 策略3: 使用UTF-8编码 (local_code=1) - 可能对BLOB数据有问题
            {'local_code': 1, 'description': 'UTF-8编码'},
            # 策略1: 使用默认编码（通常对BLOB数据更友好）
            {'description': '默认编码'}, 
            # 策略2: 明确指定GBK编码
            {'local_code': 0, 'description': 'GBK编码'},
           
        ]
        
        last_error = None
        for i, strategy in enumerate(connection_strategies):
            try:
                logger.debug(f"尝试连接策略{i+1}: {strategy.get('description', '默认编码')}")
                
                conn_params = {
                    'server': self.config['host'],
                    'port': self.config['port'],
                    'user': self.config['user'],
                    'password': self.config['password']
                }
                
                # 添加策略特定的参数
                if 'local_code' in strategy:
                    conn_params['local_code'] = strategy['local_code']
                
                self.conn = dmPython.connect(**conn_params)
                self.cursor = self.conn.cursor()
                
                # 测试连接是否可以正确处理BLOB数据
                test_result = self._test_blob_handling()
                if test_result:
                    logger.info(f"连接策略{i+1}成功，使用{strategy.get('description', '默认编码')}")
                    return
                else:
                    logger.warning(f"连接策略{i+1}无法正确处理BLOB数据")
                    self.close()
                    continue
                    
            except Exception as e:
                last_error = e
                logger.warning(f"连接策略{i+1}失败: {e}")
                if self.conn:
                    try:
                        self.conn.close()
                    except:
                        pass
                    self.conn = None
                    self.cursor = None
                continue
        
        # 如果所有策略都失败
        logger.error(f"所有连接策略都失败，最后错误: {last_error}")
        raise ConnectionError(f"无法建立数据库连接: {last_error}")

    def _test_blob_handling(self) -> bool:
        """
        测试当前连接是否能正确处理BLOB数据
        
        :return: True if BLOB handling works, False otherwise
        """
        try:
            # 首先测试表结构
            test_sql = """
            SELECT COUNT(*) as cnt 
            FROM USER_TAB_COLUMNS 
            WHERE TABLE_NAME = 'CHECKPOINTS' 
            AND COLUMN_NAME IN ('CHECKPOINT', 'METADATA')
            """
            
            self.cursor.execute(test_sql)
            result = self.cursor.fetchone()
            if result and result[0] >= 2:
                logger.debug("检测到BLOB字段，开始测试BLOB数据读取")
                
                # 测试实际的BLOB数据读取 - 这是关键测试
                blob_test_sql = """
                SELECT thread_id, checkpoint_id, checkpoint, metadata
                FROM checkpoints 
                WHERE ROWNUM <= 1
                """
                
                self.cursor.execute(blob_test_sql)
                
                # 尝试读取包含BLOB字段的行
                test_row = self.cursor.fetchone()
                if test_row:
                    # 检查BLOB数据类型
                    checkpoint_data = test_row[2]  # checkpoint字段
                    metadata_data = test_row[3]    # metadata字段
                    
                    logger.debug(f"BLOB测试数据类型 - checkpoint: {type(checkpoint_data)}, metadata: {type(metadata_data)}")
                    
                    # 验证数据能够正确读取
                    if checkpoint_data is not None and metadata_data is not None:
                        logger.debug("BLOB数据读取测试通过")
                        return True
                    else:
                        logger.debug("BLOB数据为空，但连接工作正常")
                        return True
                else:
                    logger.debug("没有现有的BLOB数据可测试，但连接工作正常")
                    return True
            else:
                logger.debug("未找到BLOB字段，使用标准处理")
                return True
                
        except UnicodeDecodeError as decode_error:
            logger.warning(f"BLOB数据编码测试失败: {decode_error}")
            # 编码错误意味着这个连接策略不适合当前的BLOB数据
            return False
        except Exception as e:
            logger.warning(f"BLOB处理测试失败: {e}")
            return False

    def close(self):
        """关闭数据库连接"""
        try:
            if self.cursor:
                self.cursor.close()
            if self.conn:
                self.conn.close()
        except dmPython.Error as e:
            logger.warning(f"关闭连接时发生错误: {str(e)}")

    def execute_query(self,
                      sql: str,
                      params: Optional[Tuple] = None) -> List[Dict[str, Any]]:
        """
        执行查询语句，使用HEX函数方法彻底解决BLOB字段编码问题

        :param sql: SQL语句
        :param params: 查询参数（可选）
        :return: 查询结果列表（字典形式）
        """
        if not self.conn or not self.cursor:
            raise ConnectionError("数据库未连接")

        try:
            # 检查SQL是否包含BLOB字段
            has_blob_fields = self._has_blob_fields(sql)
            
            if has_blob_fields:
                # 使用HEX函数方法处理BLOB字段
                logger.debug("检测到BLOB字段，使用HEX函数处理模式")
                return self._execute_blob_hex_query(sql, params)
            else:
                # 标准查询处理
                return self._execute_standard_query(sql, params)
            
        except Exception as e:
            self.conn.rollback()
            logger.error(f"查询执行失败: {str(e)}")
            raise RuntimeError(f"查询执行失败: {str(e)}") from e

    def _has_blob_fields(self, sql: str) -> bool:
        """检查SQL是否包含BLOB字段"""
        sql_lower = sql.lower()
        return any(field in sql_lower for field in ['checkpoint', 'metadata'])

    def _execute_standard_query(self, sql: str, params: Optional[Tuple] = None) -> List[Dict[str, Any]]:
        """执行标准查询"""
        self.cursor.execute(sql, params)
        columns = [col[0] for col in self.cursor.description]
        
        results = []
        rows = self.cursor.fetchall()
        
        for row in rows:
            row_dict = {columns[i]: row[i] for i in range(len(columns))}
            results.append(row_dict)
        
        logger.debug(f"标准查询成功处理 {len(results)} 行数据")
        return results

    def _execute_blob_hex_query(self, sql: str, params: Optional[Tuple] = None) -> List[Dict[str, Any]]:
        """
        使用HEX函数处理BLOB字段查询
        
        这是解决dmPython BLOB编码问题的最佳方案：
        1. 将BLOB字段转换为HEX字符串避免编码问题
        2. 在Python端将HEX字符串转回二进制数据
        3. 确保数据完整性不受编码影响
        """
        try:
            # 修改SQL，将BLOB字段转换为HEX格式
            modified_sql = self._convert_blob_to_hex(sql)
            logger.debug(f"原始SQL: {sql}")
            logger.debug(f"修改后SQL: {modified_sql}")
            
            self.cursor.execute(modified_sql, params)
            columns = [col[0] for col in self.cursor.description]
            
            results = []
            rows = self.cursor.fetchall()
            
            for row in rows:
                row_dict = {}
                for i, value in enumerate(row):
                    col_name = columns[i]
                    
                    # 检查是否是HEX转换后的BLOB字段
                    if col_name.upper().endswith('_HEX') and value is not None:
                        # 获取原始字段名
                        original_field = col_name[:-4]  # 移除 '_HEX' 后缀
                        
                        if original_field.upper() in ['CHECKPOINT', 'METADATA']:
                            try:
                                # 将HEX字符串转换回二进制数据，然后解码为UTF-8字符串
                                # 因为LangGraph序列化器期望字符串输入
                                binary_data = bytes.fromhex(value)
                                string_data = binary_data.decode('utf-8', errors='replace')
                                row_dict[original_field.upper()] = string_data
                                logger.debug(f"成功转换BLOB字段 {original_field}: {len(binary_data)} bytes -> {len(string_data)} chars")
                            except ValueError as hex_error:
                                logger.warning(f"HEX转换失败 {original_field}: {hex_error}")
                                row_dict[original_field.upper()] = None
                        else:
                            row_dict[col_name] = value
                    else:
                        row_dict[col_name] = value
                
                results.append(row_dict)
            
            logger.debug(f"BLOB HEX查询成功处理 {len(results)} 行数据")
            return results
            
        except Exception as e:
            logger.error(f"BLOB HEX查询失败: {e}")
            # 如果HEX方法失败，回退到标准方法
            logger.warning("HEX方法失败，回退到标准查询")
            logger.warning(f"sql: {sql}")
            logger.warning(f"params: {params}")
            return self._execute_standard_query(sql, params)

    def _convert_blob_to_hex(self, sql: str) -> str:
        """
        将SQL中的BLOB字段转换为HEX函数调用
        
        例如：
        SELECT checkpoint, metadata FROM table
        转换为：
        SELECT HEX(checkpoint) as checkpoint_hex, HEX(metadata) as metadata_hex FROM table
        """
        import re
        
        # 匹配SELECT子句中的BLOB字段
        def replace_blob_field(match):
            field_name = match.group(1).strip()
            if field_name.lower() in ['checkpoint', 'metadata']:
                return f"HEX({field_name}) as {field_name}_hex"
            return field_name
        
        # 处理SELECT语句中的字段列表
        # 匹配 SELECT 后面的字段列表
        select_pattern = r'SELECT\s+(.*?)\s+FROM'
        select_match = re.search(select_pattern, sql, re.IGNORECASE | re.DOTALL)
        
        if select_match:
            fields_part = select_match.group(1)
            
            # 分割字段列表
            fields = [f.strip() for f in fields_part.split(',')]
            
            # 转换BLOB字段
            converted_fields = []
            for field in fields:
                field_clean = field.strip()
                if field_clean.lower() in ['checkpoint', 'metadata']:
                    converted_fields.append(f"HEX({field_clean}) as {field_clean}_hex")
                else:
                    converted_fields.append(field_clean)
            
            # 重构SQL
            new_fields = ', '.join(converted_fields)
            new_sql = sql.replace(fields_part, new_fields)
            
            return new_sql
        
        # 如果无法解析，返回原SQL
        logger.warning("无法解析SQL进行BLOB转换，使用原始SQL")
        return sql


    def execute_update(self,
                       sql: str,
                       params: Optional[Tuple] = None) -> int:
        """
        执行更新操作（INSERT/UPDATE/DELETE）

        :param sql: SQL语句
        :param params: 查询参数（可选）
        :return: 受影响的行数
        """
        if not self.conn or not self.cursor:
            raise ConnectionError("数据库未连接")

        try:
            self.cursor.execute(sql, params)
            return self.cursor.rowcount
        except Exception as e:
            self.conn.rollback()
            logger.error(f"更新操作失败: {str(e)}")
            raise RuntimeError(f"更新操作失败: {str(e)}") from e

    def commit(self):
        """提交事务"""
        if self.conn:
            try:
                self.conn.commit()
            except dmPython.Error as e:
                raise RuntimeError(f"提交事务失败: {str(e)}") from e

    def rollback(self):
        """回滚事务"""
        if self.conn:
            try:
                self.conn.rollback()
            except dmPython.Error as e:
                raise RuntimeError(f"回滚事务失败: {str(e)}") from e

    def test_connection(self) -> bool:
        """测试数据库连接"""
        try:
            result = self.execute_query("SELECT 1 as test")
            return len(result) > 0 and result[0].get('TEST', result[0].get('test')) == 1
        except Exception as e:
            logger.error(f"连接测试失败: {e}")
            return False


# 兼容性函数，方便替换原有的DMDatabase
def create_dm_connection(dm_config: Dict[str, Any]):
    """
    创建简化版DM数据库连接

    :param dm_config: 数据库配置字典
    :return: DMDatabaseSimple实例
    """
    return DMDatabaseSimple(
        host=dm_config.get('host'),
        port=dm_config.get('port'),
        user=dm_config.get('user'),
        password=dm_config.get('password'),
        database=dm_config.get('database'),
        auto_commit=dm_config.get('auto_commit', False)
    )


# 示例用法
if __name__ == '__main__':
    # 数据库配置
    db_config = {
        'host': '*************',
        'port': 5236,
        'user': 'SX_NACOS',
        'password': 'Gznt_85535888',
        'database': 'SX_PLATFORM',
        'auto_commit': False
    }

    try:
        with DMDatabaseSimple(**db_config) as db:
            # 测试连接
            if db.test_connection():
                print("✅ 数据库连接测试成功")
                
                # 测试查询checkpoint表
                try:
                    result = db.execute_query(
                        "SELECT thread_id, checkpoint_id, checkpoint, metadata FROM SX_AIPLATFORM.checkpoints LIMIT 1"
                    )
                    print(f"✅ 查询checkpoint表成功，找到 {len(result)} 条记录")
                    
                    if result:
                        row = result[0]
                        print(f"   Thread ID: {row.get('THREAD_ID', row.get('thread_id'))}")
                        print(f"   Checkpoint ID: {row.get('CHECKPOINT_ID', row.get('checkpoint_id'))}")
                        checkpoint_data = row.get('CHECKPOINT', row.get('checkpoint'))
                        print(f"   Checkpoint数据类型: {type(checkpoint_data)}")
                        print(f"   Checkpoint数据长度: {len(checkpoint_data) if checkpoint_data else 0}")
                        
                        metadata_data = row.get('METADATA', row.get('metadata'))
                        print(f"   Metadata数据类型: {type(metadata_data)}")
                        print(f"   Metadata数据长度: {len(metadata_data) if metadata_data else 0}")
                        
                except Exception as e:
                    print(f"❌ 查询失败: {e}")
            else:
                print("❌ 数据库连接测试失败")

    except Exception as e:
        print(f"❌ 数据库操作异常: {str(e)}")