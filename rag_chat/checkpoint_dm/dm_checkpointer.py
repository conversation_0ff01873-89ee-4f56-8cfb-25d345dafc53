"""达梦数据库同步 Checkpointer 实现

基于 BaseCheckpointSaver 接口实现的达梦数据库同步checkpointer。
参考 langgraph-checkpoint-postgres 的实现逻辑，适配达梦数据库。
"""

import json
import logging
from contextlib import contextmanager
from typing import Any, Dict, Iterator, List, Optional, Sequence, Tuple, Union
from uuid import uuid4

from langgraph.checkpoint.base import (
    BaseCheckpointSaver,
    Checkpoint,
    CheckpointMetadata,
    CheckpointTuple,
    get_checkpoint_id,
)

# 处理 PendingWrite 的导入（版本兼容性）
try:
    from langgraph.checkpoint.base import PendingWrite
except ImportError:
    # 早期版本可能使用不同的导入路径
    try:
        from langgraph.checkpoint import PendingWrite
    except ImportError:
        # 如果都找不到，创建一个简单的替代实现
        from typing import NamedTuple, Any
        
        class PendingWrite(NamedTuple):
            task_id: str
            channel: str
            value: Any
from langgraph.checkpoint.serde.jsonplus import JsonPlusSerializer

from .dm_schema import (
    CHECK_TABLE_EXISTS_SQL,
    CHECK_INDEX_EXISTS_SQL,
    CREATE_CHECKPOINTS_TABLE_TEMPLATE,
    CREATE_WRITES_TABLE_TEMPLATE,
    CREATE_CHECKPOINTS_INDEXES_TEMPLATE,
    CREATE_WRITES_INDEXES_TEMPLATE,
    INSERT_CHECKPOINT_SQL_TEMPLATE,
    INSERT_WRITE_SQL_TEMPLATE,
    DELETE_DUPLICATE_WRITE_SQL_TEMPLATE,
    SELECT_LATEST_CHECKPOINT_SQL_TEMPLATE,
    SELECT_CHECKPOINT_BY_ID_SQL_TEMPLATE,
    LIST_CHECKPOINTS_SQL_TEMPLATE,
    SELECT_WRITES_SQL_TEMPLATE,
    DELETE_THREAD_CHECKPOINTS_SQL_TEMPLATE,
    DELETE_THREAD_WRITES_SQL_TEMPLATE,
)

logger = logging.getLogger(__name__)


class DMCheckpointSaver(BaseCheckpointSaver):
    """达梦数据库同步 Checkpointer 实现
    
    使用达梦数据库存储 LangGraph 的 checkpoints 和 writes。
    支持完整的 BaseCheckpointSaver 接口。
    """
    
    def __init__(
        self, 
        dm_config: Dict[str, Any],
        serde: Optional[Any] = None
    ):
        """初始化达梦 Checkpointer
        
        Args:
            dm_config: 达梦数据库连接配置
            serde: 序列化器，默认使用 JsonPlusSerializer
        """
        super().__init__(serde=serde or JsonPlusSerializer())
        self.dm_config = dm_config
        self._is_setup = False
        
        # 获取数据库名称并生成所有 SQL 语句
        self.database_name = self.dm_config.get('database', 'SX_AIPLATFORM').upper()
        self._generate_sql_statements()
        
    def _generate_sql_statements(self):
        """生成所有 SQL 语句，使用实际的数据库名称"""
        # 生成创建表的 SQL
        self.create_checkpoints_table_sql = CREATE_CHECKPOINTS_TABLE_TEMPLATE.format(database=self.database_name)
        self.create_writes_table_sql = CREATE_WRITES_TABLE_TEMPLATE.format(database=self.database_name)
        
        # 生成索引创建 SQL
        self.create_checkpoints_indexes = [
            (index_name, index_sql_template.format(database=self.database_name))
            for index_name, index_sql_template in CREATE_CHECKPOINTS_INDEXES_TEMPLATE
        ]
        self.create_writes_indexes = [
            (index_name, index_sql_template.format(database=self.database_name))
            for index_name, index_sql_template in CREATE_WRITES_INDEXES_TEMPLATE
        ]
        
        # 生成操作 SQL
        self.insert_checkpoint_sql = INSERT_CHECKPOINT_SQL_TEMPLATE.format(database=self.database_name)
        self.insert_write_sql = INSERT_WRITE_SQL_TEMPLATE.format(database=self.database_name)
        self.delete_duplicate_write_sql = DELETE_DUPLICATE_WRITE_SQL_TEMPLATE.format(database=self.database_name)
        self.select_latest_checkpoint_sql = SELECT_LATEST_CHECKPOINT_SQL_TEMPLATE.format(database=self.database_name)
        self.select_checkpoint_by_id_sql = SELECT_CHECKPOINT_BY_ID_SQL_TEMPLATE.format(database=self.database_name)
        self.list_checkpoints_sql = LIST_CHECKPOINTS_SQL_TEMPLATE.format(database=self.database_name)
        self.select_writes_sql = SELECT_WRITES_SQL_TEMPLATE.format(database=self.database_name)
        self.delete_thread_checkpoints_sql = DELETE_THREAD_CHECKPOINTS_SQL_TEMPLATE.format(database=self.database_name)
        self.delete_thread_writes_sql = DELETE_THREAD_WRITES_SQL_TEMPLATE.format(database=self.database_name)
        
    @classmethod
    def from_conn_info(
        cls,
        host: str,
        port: int,
        user: str,
        password: str,
        database: str,
        **kwargs
    ) -> "DMCheckpointSaver":
        """从连接信息创建 DMCheckpointSaver
        
        Args:
            host: 数据库主机
            port: 数据库端口
            user: 数据库用户名
            password: 数据库密码
            database: 数据库名
            **kwargs: 其他连接参数
            
        Returns:
            DMCheckpointSaver 实例
        """
        dm_config = {
            'host': host,
            'port': port,
            'user': user,
            'password': password,
            'database': database,
            'auto_commit': False,
            **kwargs
        }
        return cls(dm_config)
    
    @contextmanager
    def _get_connection(self):
        """获取数据库连接的上下文管理器"""
        from .dm_util_simple import DMDatabaseSimple
        
        db = None
        try:
            db = DMDatabaseSimple(**self.dm_config)
            db.connect()  # 确保连接已建立
            yield db
        except Exception as e:
            logger.error(f"数据库操作错误: {e}")
            if db and hasattr(db, 'rollback'):
                try:
                    db.rollback()
                except Exception:
                    pass  # 回滚失败不应该掩盖原始错误
            raise
        finally:
            if db and hasattr(db, 'close'):
                try:
                    db.close()
                except Exception as e:
                    logger.warning(f"关闭数据库连接失败: {e}")
    
    def setup(self) -> None:
        """设置数据库表和索引"""
        if self._is_setup:
            return
            
        with self._get_connection() as db:
            try:
                # 检查并创建 checkpoints 表
                result = db.execute_query(CHECK_TABLE_EXISTS_SQL, ('CHECKPOINTS', self.database_name))
                if result and result[0].get('COUNT', result[0].get('count', 0)) == 0:
                    db.execute_update(self.create_checkpoints_table_sql)
                    logger.info("创建 checkpoints 表成功")
                else:
                    logger.debug("checkpoints 表已存在")
                
                # 检查并创建 writes 表
                result = db.execute_query(CHECK_TABLE_EXISTS_SQL, ('WRITES', self.database_name))
                if result and result[0].get('COUNT', result[0].get('count', 0)) == 0:
                    db.execute_update(self.create_writes_table_sql)
                    logger.info("创建 writes 表成功")
                else:
                    logger.debug("writes 表已存在")
                
                # 检查并创建 checkpoints 索引
                for index_name, index_sql in self.create_checkpoints_indexes:
                    try:
                        result = db.execute_query(CHECK_INDEX_EXISTS_SQL, (index_name, self.database_name))
                        if result and result[0].get('COUNT', result[0].get('count', 0)) == 0:
                            db.execute_update(index_sql)
                            logger.debug(f"创建索引 {index_name} 成功")
                        else:
                            logger.debug(f"索引 {index_name} 已存在")
                    except Exception as e:
                        logger.warning(f"创建索引 {index_name} 失败: {e}")
                
                # 检查并创建 writes 索引
                for index_name, index_sql in self.create_writes_indexes:
                    try:
                        result = db.execute_query(CHECK_INDEX_EXISTS_SQL, (index_name, self.database_name))
                        if result and result[0].get('COUNT', result[0].get('count', 0)) == 0:
                            db.execute_update(index_sql)
                            logger.debug(f"创建索引 {index_name} 成功")
                        else:
                            logger.debug(f"索引 {index_name} 已存在")
                    except Exception as e:
                        logger.warning(f"创建索引 {index_name} 失败: {e}")
                
                db.commit()
                self._is_setup = True
                logger.info("达梦数据库 checkpointer 表和索引设置完成")
                
            except Exception as e:
                db.rollback()
                logger.error(f"设置数据库表失败: {e}")
                raise
    
    def _parse_config(self, config: Dict[str, Any]) -> Tuple[str, str, Optional[str]]:
        """解析配置获取关键参数
        
        支持从 configurable 和 metadata 两个地方获取参数，
        兼容 LangGraph get_state_history 的调用方式
        
        Returns:
            (thread_id, checkpoint_ns, checkpoint_id)
        """
        logger.debug(f"_parse_config input: {config}")
        
        configurable = config.get("configurable", {})
        logger.debug(f"configurable: {configurable}")
        
        # 首先尝试从 configurable 中获取 thread_id
        thread_id = configurable.get("thread_id")
        logger.debug(f"thread_id from configurable: {thread_id}")
        
        # 如果 configurable 中没有，尝试从 metadata 中获取
        # LangGraph 的 get_state_history 会将参数放在 metadata 中
        if not thread_id and "metadata" in config:
            metadata = config["metadata"]
            logger.debug(f"metadata found: {metadata}, type: {type(metadata)}")
            # metadata 可能是 ChainMap 或者 dict
            if hasattr(metadata, 'get'):
                thread_id = metadata.get("thread_id")
                logger.debug(f"thread_id from metadata.get(): {thread_id}")
            elif hasattr(metadata, '__getitem__'):
                try:
                    thread_id = metadata["thread_id"]
                    logger.debug(f"thread_id from metadata[]: {thread_id}")
                except (KeyError, TypeError):
                    logger.debug("thread_id not found in metadata[]")
                    pass
        
        if not thread_id:
            raise ValueError("Missing required 'thread_id' in config")
        
        # checkpoint_ns 的处理：优先从 configurable，然后从 metadata
        checkpoint_ns = configurable.get("checkpoint_ns")
        logger.debug(f"checkpoint_ns from configurable: {checkpoint_ns}")
        
        if checkpoint_ns is None and "metadata" in config:
            metadata = config["metadata"]
            if hasattr(metadata, 'get'):
                checkpoint_ns = metadata.get("checkpoint_ns")
                logger.debug(f"checkpoint_ns from metadata.get(): {checkpoint_ns}")
            elif hasattr(metadata, '__getitem__'):
                try:
                    checkpoint_ns = metadata["checkpoint_ns"]
                    logger.debug(f"checkpoint_ns from metadata[]: {checkpoint_ns}")
                except (KeyError, TypeError):
                    logger.debug("checkpoint_ns not found in metadata[]")
                    pass
        
        # 默认值处理
        if checkpoint_ns is None:
            checkpoint_ns = ""
            logger.debug(f"checkpoint_ns defaulted to empty string")
            
        checkpoint_id = configurable.get("checkpoint_id")
        logger.debug(f"checkpoint_id: {checkpoint_id}")
        
        result = (thread_id, checkpoint_ns, checkpoint_id)
        logger.debug(f"_parse_config result: {result}")
        
        return result
    
    def put(
        self,
        config: Dict[str, Any],
        checkpoint: Checkpoint,
        metadata: CheckpointMetadata,
        new_versions: Dict[str, Union[str, int]],
    ) -> Dict[str, Any]:
        """存储 checkpoint
        
        Args:
            config: 配置信息
            checkpoint: checkpoint 数据
            metadata: 元数据
            new_versions: 新版本信息
            
        Returns:
            更新后的配置
        """
        if not self._is_setup:
            self.setup()
            
        thread_id, checkpoint_ns, parent_checkpoint_id = self._parse_config(config)
        checkpoint_id = checkpoint.get("id", str(uuid4()))
        
        # 确保 checkpoint 有 id
        checkpoint = {**checkpoint, "id": checkpoint_id}
        
        with self._get_connection() as db:
            try:
                # 序列化数据
                checkpoint_data = self.serde.dumps(checkpoint)
                metadata_data = self.serde.dumps(metadata)
                
                # 插入 checkpoint
                db.execute_update(
                    self.insert_checkpoint_sql,
                    (
                        thread_id,
                        checkpoint_ns, 
                        checkpoint_id,
                        parent_checkpoint_id,
                        "checkpoint",
                        checkpoint_data,
                        metadata_data
                    )
                )
                
                db.commit()
                logger.debug(f"成功存储 checkpoint: {checkpoint_id}")
                
                # 返回更新后的配置
                return {
                    "configurable": {
                        "thread_id": thread_id,
                        "checkpoint_ns": checkpoint_ns,
                        "checkpoint_id": checkpoint_id,
                    }
                }
                
            except Exception as e:
                db.rollback()
                logger.error(f"存储 checkpoint 失败: {e}")
                raise
    
    def put_writes(
        self,
        config: Dict[str, Any],
        writes: Sequence[Tuple[str, Any]],
        task_id: str,
    ) -> None:
        """存储 writes
        
        Args:
            config: 配置信息
            writes: 写入数据序列
            task_id: 任务ID
        """
        if not self._is_setup:
            self.setup()
            
        thread_id, checkpoint_ns, checkpoint_id = self._parse_config(config)
        
        if not checkpoint_id:
            raise ValueError("Missing required 'checkpoint_id' in config for writes")
        
        with self._get_connection() as db:
            try:
                for idx, (channel, value) in enumerate(writes):
                    # 序列化 value
                    value_data = self.serde.dumps(value)
                    
                    # 先删除可能存在的重复记录（处理分支创建场景）
                    try:
                        db.execute_update(
                            self.delete_duplicate_write_sql,
                            (thread_id, checkpoint_ns, checkpoint_id, task_id, idx)
                        )
                    except Exception as delete_error:
                        logger.debug(f"删除重复记录时忽略错误: {delete_error}")
                    
                    # 插入 write，如果冲突则忽略
                    try:
                        db.execute_update(
                            self.insert_write_sql,
                            (
                                thread_id,
                                checkpoint_ns,
                                checkpoint_id,
                                task_id,
                                idx,
                                channel,
                                "write",
                                value_data
                            )
                        )
                    except Exception as insert_error:
                        # 如果是主键冲突，记录警告但继续执行
                        if "违反" in str(insert_error) or "constraint" in str(insert_error).lower():
                            logger.warning(f"跳过重复write记录: thread_id={thread_id}, task_id={task_id}, idx={idx}")
                        else:
                            # 其他错误仍然抛出
                            raise
                
                db.commit()
                logger.debug(f"成功存储 {len(writes)} 个 writes")
                
            except Exception as e:
                db.rollback()
                logger.error(f"存储 writes 失败: {e}")
                raise
    
    def get_tuple(self, config: Dict[str, Any]) -> Optional[CheckpointTuple]:
        """获取 checkpoint 元组
        
        Args:
            config: 配置信息
            
        Returns:
            CheckpointTuple 或 None
        """
        if not self._is_setup:
            self.setup()
            
        thread_id, checkpoint_ns, checkpoint_id = self._parse_config(config)
        
        with self._get_connection() as db:
            try:
                # 查询 checkpoint
                if checkpoint_id:
                    # 查询指定的 checkpoint
                    result = db.execute_query(
                        self.select_checkpoint_by_id_sql,
                        (thread_id, checkpoint_ns, checkpoint_id)
                    )
                else:
                    # 查询最新的 checkpoint
                    result = db.execute_query(
                        self.select_latest_checkpoint_sql,
                        (thread_id, checkpoint_ns)
                    )
                
                if not result:
                    return None
                
                row = result[0]
                
                # 反序列化数据（兼容大小写字段名）
                checkpoint_raw = row.get('CHECKPOINT', row.get('checkpoint'))
                metadata_raw = row.get('METADATA', row.get('metadata', '{}'))
                
                checkpoint_data = self.serde.loads(checkpoint_raw)
                metadata = self.serde.loads(metadata_raw)
                
                # 构建当前配置
                current_config = {
                    "configurable": {
                        "thread_id": row.get('THREAD_ID', row.get('thread_id')),
                        "checkpoint_ns": row.get('CHECKPOINT_NS', row.get('checkpoint_ns', '')),
                        "checkpoint_id": row.get('CHECKPOINT_ID', row.get('checkpoint_id')),
                    }
                }
                
                # 构建父配置
                parent_config = None
                parent_id = row.get('PARENT_CHECKPOINT_ID', row.get('parent_checkpoint_id'))
                if parent_id:
                    parent_config = {
                        "configurable": {
                            "thread_id": row.get('THREAD_ID', row.get('thread_id')),
                            "checkpoint_ns": row.get('CHECKPOINT_NS', row.get('checkpoint_ns', '')),
                            "checkpoint_id": parent_id,
                        }
                    }
                
                # 查询 pending writes
                pending_writes = self._get_pending_writes(
                    db, thread_id, checkpoint_ns, row.get('CHECKPOINT_ID', row.get('checkpoint_id'))
                )
                
                return CheckpointTuple(
                    config=current_config,
                    checkpoint=checkpoint_data,
                    metadata=metadata,
                    parent_config=parent_config,
                    pending_writes=pending_writes,
                )
                
            except Exception as e:
                logger.error(f"获取 checkpoint 失败: {e}")
                raise
    
    def _get_pending_writes(
        self, 
        db: Any, 
        thread_id: str, 
        checkpoint_ns: str, 
        checkpoint_id: str
    ) -> List[PendingWrite]:
        """获取 pending writes"""
        try:
            writes_result = db.execute_query(
                self.select_writes_sql,
                (thread_id, checkpoint_ns, checkpoint_id)
            )
            
            pending_writes = []
            for write_row in writes_result:
                value_raw = write_row.get('VALUE', write_row.get('value'))
                value = self.serde.loads(value_raw)
                # 不再创建 PendingWrite 对象，而是直接使用 tuple 形式
                # 这与 LangGraph 的期望格式兼容
                pending_write = (
                    write_row.get('TASK_ID', write_row.get('task_id')),
                    write_row.get('CHANNEL', write_row.get('channel')),
                    value,
                )
                pending_writes.append(pending_write)
            
            return pending_writes
            
        except Exception as e:
            # 只记录警告，不显示给用户，因为没有 pending writes 是正常情况
            logger.debug(f"获取 pending writes 失败: {e}")
            return []
    
    def list(
        self, 
        config: Dict[str, Any], 
        *, 
        filter: Optional[Dict[str, Any]] = None,
        limit: int = 10,
        before: Optional[Dict[str, Any]] = None,
    ) -> Iterator[CheckpointTuple]:
        """列出 checkpoints
        
        Args:
            config: 配置信息
            filter: 过滤条件（暂未实现）
            limit: 限制数量
            before: 在指定配置之前的 checkpoints（暂未实现）
            
        Yields:
            CheckpointTuple 迭代器
        """
        logger.debug(f"list() called with config: {config}")
        
        if not self._is_setup:
            self.setup()
            
        thread_id, checkpoint_ns, _ = self._parse_config(config)
        
        # 修复 limit 参数：如果为 None，使用默认值 10
        if limit is None:
            limit = 10
            logger.debug(f"limit was None, defaulted to 10")
        
        logger.debug(f"list() parsed: thread_id={thread_id}, checkpoint_ns='{checkpoint_ns}', limit={limit}")
        
        with self._get_connection() as db:
            try:
                logger.debug(f"Executing query: {self.list_checkpoints_sql}")
                logger.debug(f"Query params: ({thread_id}, '{checkpoint_ns}', {limit})")
                
                result = db.execute_query(
                    self.list_checkpoints_sql,
                    (thread_id, checkpoint_ns, limit)
                )
                
                logger.debug(f"Query returned {len(result)} rows")
                
                yielded_count = 0
                for row in result:
                    logger.debug(f"Processing row {yielded_count + 1}: {row.get('CHECKPOINT_ID', row.get('checkpoint_id'))[:8]}...")
                    try:
                        # 反序列化数据（兼容大小写字段名）
                        checkpoint_raw = row.get('CHECKPOINT', row.get('checkpoint'))
                        metadata_raw = row.get('METADATA', row.get('metadata', '{}'))
                        
                        checkpoint_data = self.serde.loads(checkpoint_raw)
                        metadata = self.serde.loads(metadata_raw)
                        logger.debug(f"Deserialized checkpoint and metadata successfully")
                        
                        # 构建当前配置
                        current_config = {
                            "configurable": {
                                "thread_id": row.get('THREAD_ID', row.get('thread_id')),
                                "checkpoint_ns": row.get('CHECKPOINT_NS', row.get('checkpoint_ns', '')),
                                "checkpoint_id": row.get('CHECKPOINT_ID', row.get('checkpoint_id')),
                            }
                        }
                        logger.debug(f"Built current_config: {current_config}")
                        
                        # 构建父配置
                        parent_config = None
                        parent_id = row.get('PARENT_CHECKPOINT_ID', row.get('parent_checkpoint_id'))
                        if parent_id:
                            parent_config = {
                                "configurable": {
                                    "thread_id": row.get('THREAD_ID', row.get('thread_id')),
                                    "checkpoint_ns": row.get('CHECKPOINT_NS', row.get('checkpoint_ns', '')),
                                    "checkpoint_id": parent_id,
                                }
                            }
                        logger.debug(f"Built parent_config: {parent_config}")
                        
                        # 获取 pending writes
                        pending_writes = self._get_pending_writes(
                            db, thread_id, checkpoint_ns, row.get('CHECKPOINT_ID', row.get('checkpoint_id'))
                        )
                        logger.debug(f"Got {len(pending_writes)} pending writes")
                        
                        checkpoint_tuple = CheckpointTuple(
                            config=current_config,
                            checkpoint=checkpoint_data,
                            metadata=metadata,
                            parent_config=parent_config,
                            pending_writes=pending_writes,
                        )
                        
                        logger.debug(f"Created CheckpointTuple successfully")
                        yielded_count += 1
                        yield checkpoint_tuple
                        
                    except Exception as e:
                        logger.error(f"Error processing row {yielded_count + 1}: {e}")
                        continue
                
                logger.debug(f"list() yielded {yielded_count} CheckpointTuple objects")
                    
            except Exception as e:
                logger.error(f"列出 checkpoints 失败: {e}")
                raise
    
    def delete_thread(self, thread_id: str, checkpoint_ns: str = "") -> None:
        """删除线程的所有数据
        
        Args:
            thread_id: 线程ID
            checkpoint_ns: checkpoint 命名空间
        """
        if not self._is_setup:
            self.setup()
            
        with self._get_connection() as db:
            try:
                # 删除 writes
                db.execute_update(
                    self.delete_thread_writes_sql,
                    (thread_id, checkpoint_ns)
                )
                
                # 删除 checkpoints
                db.execute_update(
                    self.delete_thread_checkpoints_sql,
                    (thread_id, checkpoint_ns)
                )
                
                db.commit()
                logger.info(f"成功删除线程 {thread_id} 的所有数据")
                
            except Exception as e:
                db.rollback()
                logger.error(f"删除线程数据失败: {e}")
                raise