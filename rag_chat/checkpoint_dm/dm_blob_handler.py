"""
达梦数据库BLOB字段安全处理器

专门解决dmPython在处理LangGraph checkpoint BLOB数据时的编码问题。
通过绕过dmPython的自动编码机制，直接处理原始二进制数据。
"""

import dmPython
import logging
from typing import Any, Dict, List, Optional, Tuple, Union
import base64
import struct

logger = logging.getLogger(__name__)


class DMBlobSafeHandler:
    """达梦数据库BLOB安全处理器"""
    
    def __init__(self, connection: dmPython.Connection):
        """
        初始化BLOB安全处理器
        
        :param connection: dmPython数据库连接
        """
        self.connection = connection
        self.cursor = connection.cursor()
    
    def execute_blob_query(self, sql: str, params: Optional[Tuple] = None) -> List[Dict[str, Any]]:
        """
        安全执行包含BLOB字段的查询
        
        :param sql: SQL查询语句
        :param params: 查询参数
        :return: 查询结果列表
        """
        try:
            # 检测是否包含BLOB字段
            if not self._has_blob_fields(sql):
                # 非BLOB查询使用标准方法
                return self._execute_standard_query(sql, params)
            
            logger.debug("检测到BLOB字段，使用安全处理模式")
            
            # 使用特殊方法处理BLOB查询
            return self._execute_blob_safe_query(sql, params)
            
        except Exception as e:
            logger.error(f"BLOB查询执行失败: {e}")
            raise
    
    def _has_blob_fields(self, sql: str) -> bool:
        """检测SQL是否包含BLOB字段"""
        sql_lower = sql.lower()
        return any(field in sql_lower for field in ['checkpoint', 'metadata'])
    
    def _execute_standard_query(self, sql: str, params: Optional[Tuple] = None) -> List[Dict[str, Any]]:
        """执行标准查询"""
        self.cursor.execute(sql, params)
        columns = [col[0] for col in self.cursor.description]
        
        results = []
        rows = self.cursor.fetchall()
        
        for row in rows:
            row_dict = {columns[i]: row[i] for i in range(len(columns))}
            results.append(row_dict)
        
        return results
    
    def _execute_blob_safe_query(self, sql: str, params: Optional[Tuple] = None) -> List[Dict[str, Any]]:
        """
        安全执行BLOB查询
        
        使用分离的查询策略：
        1. 先查询非BLOB字段
        2. 再单独查询BLOB字段（使用特殊处理）
        3. 合并结果
        """
        try:
            # 分析SQL，提取表名和WHERE条件
            table_info = self._parse_sql_info(sql)
            
            if not table_info:
                # 无法解析，回退到逐行处理
                return self._execute_row_by_row(sql, params)
            
            # 分离查询策略
            return self._execute_separated_query(table_info, params)
            
        except Exception as e:
            logger.warning(f"BLOB安全查询失败，回退到逐行处理: {e}")
            return self._execute_row_by_row(sql, params)
    
    def _parse_sql_info(self, sql: str) -> Optional[Dict[str, str]]:
        """解析SQL获取表信息"""
        import re
        
        # 简化的SQL解析（针对checkpoint表的常见查询模式）
        sql_lower = sql.lower().strip()
        
        # 提取表名
        table_match = re.search(r'from\s+(\w+)', sql_lower)
        if not table_match:
            return None
        
        table_name = table_match.group(1)
        
        # 提取WHERE条件
        where_match = re.search(r'where\s+(.+?)(?:\s+order\s+|\s+limit\s+|$)', sql_lower)
        where_clause = where_match.group(1) if where_match else ""
        
        # 提取ORDER BY
        order_match = re.search(r'order\s+by\s+(.+?)(?:\s+limit\s+|$)', sql_lower)
        order_clause = order_match.group(1) if order_match else ""
        
        # 提取LIMIT
        limit_match = re.search(r'limit\s+(\d+)', sql_lower)
        limit_clause = limit_match.group(1) if limit_match else ""
        
        return {
            'table': table_name,
            'where': where_clause,
            'order': order_clause,
            'limit': limit_clause
        }
    
    def _execute_separated_query(self, table_info: Dict[str, str], params: Optional[Tuple] = None) -> List[Dict[str, Any]]:
        """执行分离的查询策略"""
        
        # 1. 查询非BLOB字段获取行标识
        base_fields = ['thread_id', 'checkpoint_ns', 'checkpoint_id', 'parent_checkpoint_id', 'type', 'timestamp']
        
        base_sql = f"SELECT {', '.join(base_fields)} FROM {table_info['table']}"
        if table_info['where']:
            base_sql += f" WHERE {table_info['where']}"
        if table_info['order']:
            base_sql += f" ORDER BY {table_info['order']}"
        if table_info['limit']:
            base_sql += f" LIMIT {table_info['limit']}"
        
        logger.debug(f"执行基础查询: {base_sql}")
        self.cursor.execute(base_sql, params)
        
        base_columns = [col[0] for col in self.cursor.description]
        base_rows = self.cursor.fetchall()
        
        results = []
        
        # 2. 对每一行单独查询BLOB字段
        for base_row in base_rows:
            base_dict = {base_columns[i]: base_row[i] for i in range(len(base_columns))}
            
            # 构造BLOB查询条件
            thread_id = base_dict.get('THREAD_ID') or base_dict.get('thread_id')
            checkpoint_id = base_dict.get('CHECKPOINT_ID') or base_dict.get('checkpoint_id')
            
            if thread_id and checkpoint_id:
                blob_data = self._fetch_blob_data_safe(table_info['table'], thread_id, checkpoint_id)
                base_dict.update(blob_data)
            
            results.append(base_dict)
        
        return results
    
    def _fetch_blob_data_safe(self, table_name: str, thread_id: str, checkpoint_id: str) -> Dict[str, Any]:
        """安全获取BLOB数据"""
        try:
            # 使用十六进制转换方法读取BLOB
            blob_sql = f"""
            SELECT 
                HEX(checkpoint) as checkpoint_hex,
                HEX(metadata) as metadata_hex
            FROM {table_name} 
            WHERE thread_id = ? AND checkpoint_id = ?
            """
            
            self.cursor.execute(blob_sql, (thread_id, checkpoint_id))
            blob_row = self.cursor.fetchone()
            
            if blob_row:
                # 将十六进制字符串转换回二进制
                checkpoint_hex = blob_row[0]
                metadata_hex = blob_row[1]
                
                checkpoint_data = bytes.fromhex(checkpoint_hex) if checkpoint_hex else None
                metadata_data = bytes.fromhex(metadata_hex) if metadata_hex else None
                
                return {
                    'CHECKPOINT': checkpoint_data,
                    'METADATA': metadata_data
                }
            else:
                return {'CHECKPOINT': None, 'METADATA': None}
                
        except Exception as e:
            logger.warning(f"安全BLOB获取失败: {e}")
            return {'CHECKPOINT': None, 'METADATA': None}
    
    def _execute_row_by_row(self, sql: str, params: Optional[Tuple] = None) -> List[Dict[str, Any]]:
        """逐行处理查询结果"""
        try:
            self.cursor.execute(sql, params)
            columns = [col[0] for col in self.cursor.description]
            
            results = []
            
            while True:
                try:
                    row = self.cursor.fetchone()
                    if row is None:
                        break
                    
                    row_dict = {}
                    for i, value in enumerate(row):
                        col_name = columns[i]
                        
                        # 特殊处理BLOB字段
                        if col_name.upper() in ['CHECKPOINT', 'METADATA'] and value is not None:
                            if isinstance(value, str):
                                # 如果是字符串，尝试转换为bytes
                                try:
                                    # 使用latin-1编码保持字节完整性
                                    row_dict[col_name] = value.encode('latin-1')
                                except UnicodeEncodeError:
                                    # 如果失败，使用错误替换
                                    row_dict[col_name] = value.encode('utf-8', errors='replace')
                            else:
                                row_dict[col_name] = value
                        else:
                            row_dict[col_name] = value
                    
                    results.append(row_dict)
                    
                except UnicodeDecodeError as e:
                    logger.warning(f"跳过编码错误行: {e}")
                    continue
                except Exception as e:
                    logger.error(f"处理行时发生错误: {e}")
                    break
            
            return results
            
        except Exception as e:
            logger.error(f"逐行处理失败: {e}")
            return []


def create_dm_blob_safe_connection(host: str, port: int, user: str, password: str, 
                                 database: str = None) -> DMBlobSafeHandler:
    """
    创建BLOB安全的达梦数据库连接
    
    :param host: 数据库主机
    :param port: 端口
    :param user: 用户名
    :param password: 密码
    :param database: 数据库名
    :return: BLOB安全处理器
    """
    
    # 尝试不同的连接策略
    connection_strategies = [
        # 策略1: 不指定编码（让数据库自己处理）
        {},
        # 策略2: 明确使用GBK编码
        {'local_code': 0},
        # 策略3: 使用UTF-8编码
        {'local_code': 1},
    ]
    
    for i, strategy in enumerate(connection_strategies):
        try:
            conn_params = {
                'server': host,
                'port': port,
                'user': user,
                'password': password
            }
            
            if database:
                conn_params['database'] = database
                
            conn_params.update(strategy)
            
            logger.debug(f"尝试连接策略 {i+1}: {strategy}")
            
            connection = dmPython.connect(**conn_params)
            handler = DMBlobSafeHandler(connection)
            
            # 测试连接是否能处理BLOB
            test_sql = "SELECT COUNT(*) FROM USER_TABLES WHERE TABLE_NAME = 'CHECKPOINTS'"
            try:
                handler.execute_blob_query(test_sql)
                logger.info(f"BLOB安全连接建立成功，使用策略 {i+1}")
                return handler
            except Exception as test_error:
                logger.warning(f"连接策略 {i+1} 测试失败: {test_error}")
                connection.close()
                continue
                
        except Exception as e:
            logger.warning(f"连接策略 {i+1} 失败: {e}")
            continue
    
    raise ConnectionError("所有连接策略都失败，无法建立BLOB安全连接")


if __name__ == '__main__':
    # 测试用例
    try:
        handler = create_dm_blob_safe_connection(
            host='127.0.0.1',
            port=5236,
            user='SYSDBA',
            password='737498Cx',
            database='TEST_CHECKPOINTER'
        )
        
        # 测试查询
        results = handler.execute_blob_query(
            "SELECT thread_id, checkpoint_id, checkpoint, metadata FROM checkpoints LIMIT 1"
        )
        
        print("✅ BLOB安全处理器测试成功")
        for result in results:
            print(f"Thread ID: {result.get('THREAD_ID', result.get('thread_id'))}")
            print(f"Checkpoint ID: {result.get('CHECKPOINT_ID', result.get('checkpoint_id'))}")
            
            checkpoint_data = result.get('CHECKPOINT', result.get('checkpoint'))
            metadata_data = result.get('METADATA', result.get('metadata'))
            
            print(f"Checkpoint数据类型: {type(checkpoint_data)}")
            print(f"Metadata数据类型: {type(metadata_data)}")
            
            if checkpoint_data:
                print(f"Checkpoint数据长度: {len(checkpoint_data)} bytes")
            if metadata_data:
                print(f"Metadata数据长度: {len(metadata_data)} bytes")
        
        handler.connection.close()
        
    except Exception as e:
        print(f"❌ 测试失败: {e}")