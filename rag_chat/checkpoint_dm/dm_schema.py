"""达梦数据库表结构定义

基于 LangGraph PostgreSQL checkpointer 的表结构，
适配达梦数据库的 SQL 语法和数据类型。
"""

# 检查表是否存在的 SQL
CHECK_TABLE_EXISTS_SQL = """
SELECT COUNT(*) as count FROM ALL_TABLES WHERE TABLE_NAME = ? AND OWNER = ?
"""

# 检查索引是否存在的 SQL
CHECK_INDEX_EXISTS_SQL = """
SELECT COUNT(*) as count FROM ALL_INDEXES WHERE INDEX_NAME = ? AND OWNER = ?
"""

# 创建 checkpoints 表的 SQL
CREATE_CHECKPOINTS_TABLE_TEMPLATE = """
CREATE TABLE {database}.checkpoints (
    thread_id VARCHAR(255) NOT NULL,
    checkpoint_ns VARCHAR(255) DEFAULT '',
    checkpoint_id VARCHAR(255) NOT NULL,
    parent_checkpoint_id VARCHAR(255),
    type VARCHAR(128),
    checkpoint CLOB NOT NULL,
    metadata CLOB DEFAULT '{default_dict}',
    created_at TIMESTAMP DEFAULT SYSDATE,
    CONSTRAINT pk_checkpoints PRIMARY KEY (thread_id, checkpoint_ns, checkpoint_id)
)
"""

# 创建 writes 表的 SQL
CREATE_WRITES_TABLE_TEMPLATE = """
CREATE TABLE {database}.writes (
    thread_id VARCHAR(255) NOT NULL,
    checkpoint_ns VARCHAR(255) DEFAULT '',
    checkpoint_id VARCHAR(255) NOT NULL,
    task_id VARCHAR(255) NOT NULL,
    idx INTEGER NOT NULL,
    channel VARCHAR(128) NOT NULL,
    type VARCHAR(128),
    value CLOB NOT NULL,
    created_at TIMESTAMP DEFAULT SYSDATE
)
"""

# 创建索引的 SQL 和名称映射
CREATE_CHECKPOINTS_INDEXES_TEMPLATE = [
    ("IDX_CHECKPOINTS_THREAD_ID", "CREATE INDEX IDX_CHECKPOINTS_THREAD_ID ON {database}.checkpoints(thread_id)"),
    ("IDX_CHECKPOINTS_PARENT_ID", "CREATE INDEX IDX_CHECKPOINTS_PARENT_ID ON {database}.checkpoints(parent_checkpoint_id)"),
    ("IDX_CHECKPOINTS_CREATED_AT", "CREATE INDEX IDX_CHECKPOINTS_CREATED_AT ON {database}.checkpoints(created_at)"),
]

CREATE_WRITES_INDEXES_TEMPLATE = [
    ("IDX_WRITES_THREAD_ID", "CREATE INDEX IDX_WRITES_THREAD_ID ON {database}.writes(thread_id)"),
    ("IDX_WRITES_CHECKPOINT_ID", "CREATE INDEX IDX_WRITES_CHECKPOINT_ID ON {database}.writes(checkpoint_id)"),
    ("IDX_WRITES_CREATED_AT", "CREATE INDEX IDX_WRITES_CREATED_AT ON {database}.writes(created_at)"),
]

# 插入 checkpoint 的 SQL
INSERT_CHECKPOINT_SQL_TEMPLATE = """
INSERT INTO {database}.checkpoints 
(thread_id, checkpoint_ns, checkpoint_id, parent_checkpoint_id, type, checkpoint, metadata)
VALUES (?, ?, ?, ?, ?, ?, ?)
"""

# 插入 write 的 SQL
INSERT_WRITE_SQL_TEMPLATE = """
INSERT INTO {database}.writes 
(thread_id, checkpoint_ns, checkpoint_id, task_id, idx, channel, type, value)
VALUES (?, ?, ?, ?, ?, ?, ?, ?)
"""

# 删除重复 write 的 SQL
DELETE_DUPLICATE_WRITE_SQL_TEMPLATE = """
DELETE FROM {database}.writes 
WHERE thread_id = ? AND checkpoint_ns = ? AND checkpoint_id = ? AND task_id = ? AND idx = ?
"""

# 查询最新 checkpoint 的 SQL
SELECT_LATEST_CHECKPOINT_SQL_TEMPLATE = """
SELECT 
    thread_id,
    checkpoint_ns,
    checkpoint_id,
    parent_checkpoint_id,
    type,
    checkpoint,
    metadata,
    created_at
FROM {database}.checkpoints 
WHERE thread_id = ? AND checkpoint_ns = ?
ORDER BY created_at DESC
LIMIT 1
"""

# 根据 checkpoint_id 查询 checkpoint 的 SQL
SELECT_CHECKPOINT_BY_ID_SQL_TEMPLATE = """
SELECT 
    thread_id,
    checkpoint_ns,
    checkpoint_id,
    parent_checkpoint_id,
    type,
    checkpoint,
    metadata,
    created_at
FROM {database}.checkpoints 
WHERE thread_id = ? AND checkpoint_ns = ? AND checkpoint_id = ?
"""

# 列出 checkpoints 的 SQL
LIST_CHECKPOINTS_SQL_TEMPLATE = """
SELECT 
    thread_id,
    checkpoint_ns,
    checkpoint_id,
    parent_checkpoint_id,
    type,
    checkpoint,
    metadata,
    created_at
FROM {database}.checkpoints 
WHERE thread_id = ? AND checkpoint_ns = ?
ORDER BY created_at DESC
LIMIT ?
"""

# 查询 pending writes 的 SQL
SELECT_WRITES_SQL_TEMPLATE = """
SELECT 
    task_id,
    idx,
    channel,
    type,
    value
FROM {database}.writes 
WHERE thread_id = ? AND checkpoint_ns = ? AND checkpoint_id = ?
ORDER BY task_id, idx
"""

# 删除线程的所有数据的 SQL
DELETE_THREAD_CHECKPOINTS_SQL_TEMPLATE = """
DELETE FROM {database}.checkpoints 
WHERE thread_id = ? AND checkpoint_ns = ?
"""

DELETE_THREAD_WRITES_SQL_TEMPLATE = """
DELETE FROM {database}.writes 
WHERE thread_id = ? AND checkpoint_ns = ?
"""

# 清理过期数据的 SQL
DELETE_EXPIRED_CHECKPOINTS_SQL_TEMPLATE = """
DELETE FROM {database}.checkpoints 
WHERE created_at < ?
"""

DELETE_EXPIRED_WRITES_SQL_TEMPLATE = """
DELETE FROM {database}.writes 
WHERE created_at < ?
"""