"""达梦数据库 Checkpointer 异常定义

定义了达梦数据库 checkpointer 特有的异常类，
用于更好的错误处理和调试。
"""


class DMCheckpointError(Exception):
    """达梦 Checkpointer 基础异常"""
    pass


class DMConnectionError(DMCheckpointError):
    """达梦数据库连接异常"""
    pass


class DMTableError(DMCheckpointError):
    """达梦数据库表操作异常"""
    pass


class DMSerializationError(DMCheckpointError):
    """序列化/反序列化异常"""
    pass


class DMConfigError(DMCheckpointError):
    """配置错误异常"""
    pass


class DMTransactionError(DMCheckpointError):
    """事务操作异常"""
    pass