import asyncio

import docx
import fitz
import os
import docx2txt
import logging
import filetype
from concurrent.futures import <PERSON><PERSON>oolExecutor, ThreadPoolExecutor

from pdf_util import is_standard_pdf
import ocr_processor

# 配置日志
logger = logging.getLogger(__name__)

# 创建全局执行器
process_executor = ProcessPoolExecutor(max_workers=os.cpu_count())
thread_executor = ThreadPoolExecutor(max_workers=32)


def pdf_to_text_worker_V2(file_path, max_size):
  """
  将PDF转换为文本（同步工作函数）
  首先判断PDF类型，如果是标准PDF直接提取文本，如果是图片类型则使用OCR

  Args:
      file_path: PDF文件路径
      max_size: 最大文本大小

  Returns:
      tuple: (text, processed_pages, total_pages)
  """
  try:
    # 判断PDF类型
    is_standard, reason = is_standard_pdf(file_path)
    logger.info(f"PDF类型检测: {reason}")
    
    if is_standard:
      # 标准PDF，直接提取文本
      text = ''
      doc = fitz.open(file_path)
      max_page = len(doc)
      processed_pages = 0
      
      for idx, page in enumerate(doc):
        try:
          now_text = page.get_text().replace(' ', '')
          if len(text) + len(now_text) > max_size:
            break
          text += '第{}页\n'.format(idx + 1)
          text += now_text
          processed_pages += 1
        except Exception as e:
          logger.error(f"处理PDF第{idx + 1}页时出错: {e}")
      
      return text, processed_pages, max_page
    else:
      return "OCR_NEEDED", 0, 0
  except Exception as e:
    logger.error(f"PDF处理异常: {e}", exc_info=True)
    return '', 0, 0


async def pdf_to_text_V2(file_path, max_size):
  try:
    # 使用filetype检查文件类型
    kind = filetype.guess(file_path)
    if not kind or kind.mime != 'application/pdf':
      mime_type = kind.mime if kind else "未知"
      logger.error(f"文件类型错误: {mime_type}，应为PDF")
      return f"文件类型错误: {mime_type}，应为PDF", 0, 0
    
    loop = asyncio.get_running_loop()
    # 使用全局process_executor
    result, processed_pages, total_pages = await loop.run_in_executor(
      process_executor, pdf_to_text_worker_V2, file_path, max_size
    )
    
    # 如果需要OCR处理
    if result == "OCR_NEEDED":
      logger.info("检测到图片类型PDF，启动OCR处理...")
      try:
        # 添加OCR配置检查
        from ocr_config import get_ocr_config
        ocr_config = get_ocr_config()
        logger.info(f"OCR配置信息: 服务URL={ocr_config['base_url']}")
        
        # 调用OCR处理
        text, processed_pages, total_pages = await ocr_processor.add_images_to_pdf(file_path, max_size)
        logger.info(f"OCR处理完成: 获取到文本长度={len(text)}")
        
        if len(text) <= 5:
          logger.warning("OCR处理似乎没有返回有效内容，检查OCR服务连接")
          return "OCR处理未返回有效内容，请检查OCR服务配置", processed_pages, total_pages
        
        return text, processed_pages, total_pages
      except Exception as e:
        logger.error(f"OCR处理PDF失败: {e}", exc_info=True)
        import traceback
        logger.error(f"详细错误: {traceback.format_exc()}")
        return f"OCR处理失败: {str(e)}", 0, 0
    
    return result, processed_pages, total_pages
  except Exception as e:
    logger.error(f"PDF文本提取异常: {e}", exc_info=True)
    return f"处理PDF文件失败: {str(e)}", 0, 0


def word_to_text_worker_V2(file_path, max_size):
  """
  将Word文档转换为文本（同步工作函数）

  Args:
      file_path: Word文件路径
      max_size: 最大文本大小

  Returns:
      tuple: (text, processed_paragraphs, total_paragraphs)
  """
  text = ''
  processed_paragraphs = 0
  total_paragraphs = 0
  
  try:
    # 通过文件内容而非扩展名判断文件类型
    file_extension = os.path.splitext(file_path)[1].lower()
    
    if file_extension == '.docx':
      doc = docx.Document(file_path)
      total_paragraphs = len(doc.paragraphs)
      
      all_paragraphs = []
      for idx, paragraph in enumerate(doc.paragraphs):
        if paragraph.text.strip():  # 如果段落不是空的
          all_paragraphs.append((idx, paragraph.text))
      
      # 分页处理，确保段落完整性
      current_page_text = ""
      current_page_paragraphs = []
      page_num = 1
      
      for idx, para_text in all_paragraphs:
        clean_text = para_text.replace(' ', '')
        
        if len(text) + len(current_page_text) + len(clean_text) > max_size:
          # 如果会超出，先把当前页添加到结果中
          if current_page_text:
            text += f'第{page_num}页\n{current_page_text}\n'
            processed_paragraphs += len(current_page_paragraphs)
            page_num += 1
          
          # 如果单个段落就超出了最大大小按句子拆分
          if len(clean_text) > max_size:
            sentences = split_into_sentences(para_text)
            current_page_text = ""
            current_page_paragraphs = []
            
            for sentence in sentences:
              clean_sentence = sentence.replace(' ', '')
              if len(text) + len(current_page_text) + len(clean_sentence) > max_size:
                if current_page_text:
                  text += f'第{page_num}页\n{current_page_text}\n'
                  page_num += 1
                  if len(text) >= max_size:
                    break
                current_page_text = clean_sentence + "\n"
                current_page_paragraphs = [(idx, sentence)]
              else:
                current_page_text += clean_sentence + "\n"
                current_page_paragraphs.append((idx, sentence))
          else:
            current_page_text = clean_text + "\n"
            current_page_paragraphs = [(idx, para_text)]
        else:
          # 如果不超出，添加到当前页
          current_page_text += clean_text + "\n"
          current_page_paragraphs.append((idx, para_text))
        
        if len(text) >= max_size:
          break
      
      # 处理最后一页
      if current_page_text and len(text) + len(current_page_text) <= max_size:
        text += f'第{page_num}页\n{current_page_text}\n'
        processed_paragraphs += len(current_page_paragraphs)
      
      # 处理表格，保留结构信息
      try:
        table_num = 0
        for table in doc.tables:
          table_num += 1
          table_text = f'表格{table_num}:\n'
          
          # 提取表头（如果存在）
          header_row = []
          if len(table.rows) > 0:
            for cell in table.rows[0].cells:
              header_row.append(cell.text.strip())
          
          # 处理表格内容
          for i, row in enumerate(table.rows):
            row_text = ""
            for j, cell in enumerate(row.cells):
              cell_text = cell.text.replace(' ', '')
              if cell_text.strip():
                if i == 0:  # 表头
                  row_text += f"{cell_text} | "
                else:  # 数据行
                  # 添加列标识，关联表头
                  if j < len(header_row) and header_row[j]:
                    row_text += f"{header_row[j]}:{cell_text} | "
                  else:
                    row_text += f"{cell_text} | "
            
            if row_text:
              table_text += row_text.rstrip(" | ") + "\n"
          
          # 检查添加表格是否超出大小限制
          if len(text) + len(table_text) <= max_size:
            text += table_text + "\n"
          else:
            break
      except Exception as e:
        logger.error(f"处理表格时出错: {e}")
    
    elif file_extension == '.doc':
      try:
        full_text = docx2txt.process(file_path)
        
        # 分段处理
        paragraphs = full_text.split('\n\n')
        total_paragraphs = len(paragraphs)
        
        current_page_text = ""
        page_num = 1
        current_page_paragraphs = []
        
        for idx, paragraph in enumerate(paragraphs):
          if not paragraph.strip():
            continue
          
          clean_text = paragraph.replace(' ', '')
          
          # 检查添加本段是否会超出大小限制
          if len(text) + len(current_page_text) + len(clean_text) > max_size:
            if current_page_text:
              text += f'第{page_num}页\n{current_page_text}\n'
              processed_paragraphs += len(current_page_paragraphs)
              page_num += 1
            
            # 处理超长段落
            if len(clean_text) > max_size:
              sentences = split_into_sentences(paragraph)
              current_page_text = ""
              current_page_paragraphs = []
              
              for sentence in sentences:
                clean_sentence = sentence.replace(' ', '')
                if len(text) + len(current_page_text) + len(clean_sentence) > max_size:
                  if current_page_text:
                    text += f'第{page_num}页\n{current_page_text}\n'
                    page_num += 1
                    if len(text) >= max_size:
                      break
                  current_page_text = clean_sentence + "\n"
                  current_page_paragraphs = [(idx, sentence)]
                else:
                  current_page_text += clean_sentence + "\n"
                  current_page_paragraphs.append((idx, sentence))
            else:
              # 重新开始新一页
              current_page_text = clean_text + "\n"
              current_page_paragraphs = [(idx, paragraph)]
          else:
            # 如果不超出，添加到当前页
            current_page_text += clean_text + "\n"
            current_page_paragraphs.append((idx, paragraph))
          
          if len(text) >= max_size:
            break
        
        # 处理最后一页
        if current_page_text and len(text) + len(current_page_text) <= max_size:
          text += f'第{page_num}页\n{current_page_text}\n'
          processed_paragraphs += len(current_page_paragraphs)
      except Exception as e:
        logger.error(f"处理DOC文件时出错: {e}")
  
  except Exception as e:
    logger.error(f"处理Word文件时出错: {e}", exc_info=True)
    return f"处理Word文件失败: {str(e)}", 0, 0
  
  return text, processed_paragraphs, total_paragraphs


def split_into_sentences(text):
  """
  将文本分割成句子

  Args:
      text: 要分割的文本

  Returns:
      list: 句子列表
  """
  # 处理中文和英文的句子分隔符
  separators = ['。', '！', '？', '；', '.', '!', '?', ';']
  sentences = []
  current = ""
  
  for char in text:
    current += char
    if char in separators:
      sentences.append(current.strip())
      current = ""
  
  # 处理没有标点的部分
  if current.strip():
    sentences.append(current.strip())
  
  # 如果没有符合的分隔符，整段作为一个句子
  if not sentences and text.strip():
    sentences.append(text.strip())
  
  return sentences


async def word_to_text_V2(file_path, max_size):
  """
  异步将Word文档转换为文本

  Args:
      file_path: Word文件路径
      max_size: 最大文本大小

  Returns:
      tuple: (text, processed_paragraphs, total_paragraphs)
  """
  try:
    # 检查文件类型
    kind = filetype.guess(file_path)
    if not kind:
      # 对于某些Word文件，filetype无法识别，通过扩展名进行检查
      ext = os.path.splitext(file_path)[1].lower()
      if ext in ['.docx', '.doc']:
        # 直接处理文件
        pass
      else:
        logger.error(f"文件类型无法识别，请确保是Word文档: {file_path}")
        return f"文件类型错误：无法识别的文件类型，应为Word文档", 0, 0
    elif kind.mime not in ['application/vnd.openxmlformats-officedocument.wordprocessingml.document',
                           'application/msword']:
      logger.error(f"文件类型错误: {kind.mime}，应为Word文档")
      return f"文件类型错误: {kind.mime}，应为Word文档", 0, 0
    
    loop = asyncio.get_running_loop()
    # 使用全局执行器
    text, processed_paragraphs, total_paragraphs = await loop.run_in_executor(
      process_executor, word_to_text_worker_V2, file_path, max_size
    )
    return text, processed_paragraphs, total_paragraphs
  except Exception as e:
    logger.error(f"Word文档处理异常: {e}", exc_info=True)
    return f"处理Word文档失败: {str(e)}", 0, 0


# 在程序结束时关闭执行器
def shutdown_executors():
  process_executor.shutdown(wait=True)
  thread_executor.shutdown(wait=True)


async def main():
  """测试函数"""
  # 标准PDF测试
  logger.info("测试标准PDF处理...")
  text, read_page, max_page = await pdf_to_text_V2('test_standard.pdf', 50000)
  logger.info(f'标准PDF: {len(text)} 读取了 {read_page} 页，总共 {max_page} 页')
  
  # 图片PDF测试
  logger.info("测试图片类型PDF处理...")
  text, read_page, max_page = await pdf_to_text_V2('test_scanned.pdf', 50000)
  logger.info(f'图片PDF: {len(text)} 读取了 {read_page} 页，总共 {max_page} 页')