"""
基于RAG的MCP服务路由器
使用Milvus向量数据库实现向量化索引和语义检索来动态选择相关的MCP服务
"""

import json
import os
from typing import Dict, List, Any, Optional
import hashlib

from pymilvus import Collection, CollectionSchema, FieldSchema, DataType, connections, utility
from langchain_openai import OpenAIEmbeddings
from langchain_mcp_adapters.client import MultiServerMCPClient
from .logger import logger
from pydantic import SecretStr

class MCPRAGRouter:
    """基于RAG的MCP服务路由器 - 使用Milvus向量数据库"""
    
    def __init__(self, config_path: str, milvus_host: str = "localhost", milvus_port: str = "19530", 
                 milvus_db: str = "mcp_routing", milvus_user: str = "", milvus_password: str = ""):
        """
        初始化MCP RAG路由器
        
        Args:
            config_path: MCP配置文件路径
            milvus_host: Milvus服务器地址
            milvus_port: Milvus服务器端口
            milvus_db: Milvus数据库名称
            milvus_user: Milvus用户名（可选）
            milvus_password: Milvus密码（可选）
        """
        self.config_path = config_path
        self.milvus_host = milvus_host
        self.milvus_port = milvus_port
        self.milvus_db = milvus_db
        self.milvus_user = milvus_user
        self.milvus_password = milvus_password
        self.collection_name = "mcp_servers"
        self.mcp_servers = {}
        self.collection = None
        
        # 初始化embedding模型
        # self.embeddings = OpenAIEmbeddings(
        #     model=str(os.getenv("EMBEDDING_MODEL")),
        #     api_key=SecretStr(str(os.getenv("EMBEDDING_API_KEY"))),
        #     base_url=os.getenv("EMBEDDING_BASE_URL")
        # )

        # 本地测试
        from langchain_huggingface import HuggingFaceEmbeddings
        self.embeddings = HuggingFaceEmbeddings(
            model_name="../../LanguageModel/LLM_deploy/qwen/text2vec-base-chinese",
            model_kwargs={"device": "mps", "local_files_only": True}
        )
        
    async def initialize(self):
        """异步初始化方法"""
        try:
            # 连接Milvus
            self._connect_milvus()
            
            # 加载配置和构建索引
            self._load_config()
            self._setup_collection()
            await self._build_index_async()
            
            logger.info("MCP RAG路由器初始化完成")
            return True
        except Exception as e:
            logger.error(f"MCP RAG路由器初始化失败: {e}")
            return False
    
    def _connect_milvus(self):
        """连接到Milvus数据库"""
        try:
            # 如果host包含.db后缀，使用Milvus Lite模式
            if self.milvus_host.endswith('.db'):
                connect_params = {
                    "alias": "default",
                    "uri": self.milvus_host
                }
                logger.info(f"使用Milvus Lite模式: {self.milvus_host}")
            else:
                # 构建连接参数
                connect_params = {
                    "alias": "default",
                    "host": self.milvus_host,
                    "port": int(self.milvus_port),
                    "db_name": self.milvus_db
                }
                
                # 如果提供了用户名和密码，添加认证信息
                if self.milvus_user:
                    connect_params["user"] = self.milvus_user
                if self.milvus_password:
                    connect_params["password"] = self.milvus_password
                
                logger.info(f"使用Milvus服务器模式: {self.milvus_host}:{self.milvus_port}/{self.milvus_db}")
            
            connections.connect(**connect_params)
            logger.info("成功连接到Milvus数据库")
        except Exception as e:
            logger.error(f"连接Milvus失败: {e}")
            raise
    
    def _load_config(self):
        """加载MCP服务配置"""
        try:
            with open(self.config_path, 'r', encoding='utf-8') as f:
                config = json.load(f)
            self.mcp_servers = config.get('mcp_servers', {})
            logger.info(f"加载了 {len(self.mcp_servers)} 个MCP服务配置")
        except Exception as e:
            logger.error(f"加载MCP配置失败: {e}")
            self.mcp_servers = {}
    
    def _setup_collection(self):
        """设置Milvus集合"""
        # 定义字段架构
        fields = [
            FieldSchema(name="id", dtype=DataType.INT64, is_primary=True, auto_id=True),
            FieldSchema(name="server_name", dtype=DataType.VARCHAR, max_length=100),
            FieldSchema(name="description", dtype=DataType.VARCHAR, max_length=2000),
            FieldSchema(name="capabilities", dtype=DataType.VARCHAR, max_length=1000),
            FieldSchema(name="use_cases", dtype=DataType.VARCHAR, max_length=1000),
            FieldSchema(name="url", dtype=DataType.VARCHAR, max_length=200),
            FieldSchema(name="transport", dtype=DataType.VARCHAR, max_length=50),
            FieldSchema(name="embedding", dtype=DataType.FLOAT_VECTOR, dim=768),  # text2vec-base-chinese的维度
            FieldSchema(name="config_hash", dtype=DataType.VARCHAR, max_length=64)
        ]
        
        # 创建集合架构
        schema = CollectionSchema(
            fields=fields,
            description="MCP服务向量索引"
        )
        
        # 检查集合是否存在
        if utility.has_collection(self.collection_name):
            # 检查是否需要重建
            collection = Collection(self.collection_name)
            
            # 计算当前配置的哈希
            current_hash = self._calculate_config_hash()
            
            # 查询是否有相同哈希的记录
            collection.load()
            search_params = {"metric_type": "L2", "params": {"nprobe": 10}}
            
            try:
                # 检查现有数据的配置哈希
                expr = f'config_hash == "{current_hash}"'
                results = collection.query(expr=expr, output_fields=["server_name"])
                
                if len(results) == len(self.mcp_servers):
                    logger.info("MCP集合已存在且配置未变更，跳过重建")
                    self.collection = collection
                    return
                else:
                    logger.info("配置已变更，重建MCP集合")
                    utility.drop_collection(self.collection_name)
            except Exception as e:
                logger.warning(f"检查现有集合失败: {e}，重建集合")
                utility.drop_collection(self.collection_name)
        
        # 创建新集合
        self.collection = Collection(self.collection_name, schema)
        
        # 创建索引
        index_params = {
            "metric_type": "L2",
            "index_type": "IVF_FLAT",
            "params": {"nlist": 128}
        }
        self.collection.create_index(field_name="embedding", index_params=index_params)
        
        logger.info(f"MCP集合 {self.collection_name} 创建完成")
    
    def _calculate_config_hash(self) -> str:
        """计算配置文件的哈希值"""
        config_str = json.dumps(self.mcp_servers, sort_keys=True)
        return hashlib.md5(config_str.encode()).hexdigest()
    
    async def _build_index_async(self):
        """异步构建MCP服务的向量索引"""
        if not self.mcp_servers:
            logger.warning("没有可用的MCP服务配置")
            return
        
        # 检查集合是否已初始化
        if self.collection is None:
            logger.error("集合未初始化，无法构建索引")
            raise RuntimeError("Collection not initialized")
        
        # 检查集合是否已有数据
        try:
            self.collection.load()
            if self.collection.num_entities > 0:
                logger.info("MCP集合已包含数据，跳过索引构建")
                return
        except Exception as e:
            logger.error(f"检查集合数据失败: {e}")
            raise
        
        logger.info("开始构建MCP服务向量索引...")
        
        # 准备数据
        server_names = []
        descriptions = []
        capabilities_list = []
        use_cases_list = []
        urls = []
        transports = []
        description_texts = []
        
        current_hash = self._calculate_config_hash()
        
        for server_name, config in self.mcp_servers.items():
            server_names.append(server_name)
            descriptions.append(config.get('description', ''))
            capabilities_list.append(' | '.join(config.get('capabilities', [])))
            use_cases_list.append(' | '.join(config.get('use_cases', [])))
            urls.append(config.get('url', ''))
            transports.append(config.get('transport', 'streamable_http'))
            
            # 组合描述、功能和用例信息用于embedding
            description_parts = [
                config.get('description', ''),
                ' '.join(config.get('capabilities', [])),
                ' '.join(config.get('use_cases', []))
            ]
            full_description = ' '.join(filter(None, description_parts))
            description_texts.append(full_description)
        
        try:
            # 异步生成embeddings
            logger.info("生成向量embeddings...")
            embeddings_list = await self.embeddings.aembed_documents(description_texts)
            
            # 准备插入数据
            data = [
                server_names,
                descriptions,
                capabilities_list,
                use_cases_list,
                urls,
                transports,
                embeddings_list,
                [current_hash] * len(server_names)
            ]
            
            # 插入数据到Milvus
            insert_result = self.collection.insert(data)
            self.collection.flush()
            
            logger.info(f"成功插入 {len(server_names)} 个MCP服务到向量数据库")
            
        except Exception as e:
            logger.error(f"构建向量索引失败: {e}")
            raise
    
    async def route_mcp_servers(self, query: str, top_k: int = 3) -> Dict[str, Any]:
        """
        根据查询路由到最相关的MCP服务
        
        Args:
            query: 用户查询
            top_k: 返回top-k个最相关的服务
            
        Returns:
            包含选中服务和距离值的字典，distances越小代表越相似
        """
        try:
            # 检查集合是否已初始化
            if self.collection is None:
                logger.error("集合未初始化，无法进行路由")
                return {
                    "selected_servers": [],
                    "scores": [],
                    "reasoning": "集合未初始化"
                }
            
            # 确保集合已加载
            self.collection.load()
            
            # 生成查询的embedding
            query_embedding = await self.embeddings.aembed_query(query)
            
            # 设置搜索参数
            search_params = {
                "metric_type": "L2",
                "params": {"nprobe": 10}
            }
            
            # 在Milvus中搜索 - 获取所有服务作为候选，然后用动态阈值过滤
            results = self.collection.search(
                data=[query_embedding],
                anns_field="embedding",
                param=search_params,
                limit=len(self.mcp_servers),  # 获取所有可能的候选
                output_fields=["server_name", "description", "capabilities", "url", "transport"]
            )
            
            # 检查搜索结果
            if not results:
                return {
                    "selected_servers": [],
                    "scores": [],
                    "reasoning": "没有找到相关的MCP服务"
                }
            
            # 尝试获取第一个查询的结果
            try:
                hits = results[0]
                if not hits:
                    return {
                        "selected_servers": [],
                        "scores": [],
                        "reasoning": "没有找到相关的MCP服务"
                    }
            except (IndexError, AttributeError):
                return {
                    "selected_servers": [],
                    "scores": [],
                    "reasoning": "搜索结果格式错误"
                }
            
            # 处理搜索结果 - results是SearchResult类型，可以直接遍历
            selected_servers = []
            scores = []
            server_details = []
            
            # 动态阈值过滤：基于最小距离的相对阈值
            if hits:
                min_distance = hits[0].distance  # 最相似的距离
                # 动态阈值：最小距离的1.5倍，确保只返回相对相关的服务
                dynamic_threshold = min_distance * 1.5
                
                # 遍历查询结果并应用动态阈值
                for hit in hits:
                    distance = hit.distance
                    server_name = hit.entity.get("server_name")
                    
                    # 只选择距离在动态阈值内的服务
                    if distance <= dynamic_threshold and len(selected_servers) < top_k:
                        selected_servers.append(server_name)
                        scores.append(distance)
                        server_details.append({
                            "name": server_name,
                            "description": hit.entity.get("description"),
                            "distance": distance
                        })
                
                logger.info(f"动态阈值: {dynamic_threshold:.3f} (基于最小距离 {min_distance:.3f})")
            
            if not selected_servers:
                return {
                    "selected_servers": [],
                    "scores": [],
                    "reasoning": "没有找到相关的MCP服务"
                }
            
            # 生成选择理由
            reasoning = self._generate_reasoning(query, server_details)
            
            logger.info(f"为查询 '{query}' 选择了 {len(selected_servers)} 个MCP服务: {selected_servers}")
            
            return {
                "selected_servers": selected_servers,
                "scores": scores,
                "reasoning": reasoning
            }
            
        except Exception as e:
            logger.error(f"MCP服务路由失败: {e}")
            return {
                "selected_servers": [],
                "scores": [],
                "reasoning": f"路由过程出错: {str(e)}"
            }
    
    def _generate_reasoning(self, query: str, server_details: List[Dict]) -> str:
        """生成选择理由"""
        if not server_details:
            return "未找到相关服务"
        
        reasoning_parts = [f"基于查询 '{query}' 选择了以下MCP服务:"]
        
        for detail in server_details:
            server_name = detail["name"]
            distance = detail["distance"]
            description = detail["description"]
            reasoning_parts.append(f"- {server_name} (距离: {distance:.3f}): {description[:100]}...")
        
        return ' '.join(reasoning_parts)
    
    async def get_dynamic_mcp_tools(self, query: str, top_k: int = 2) -> List:
        """
        根据查询动态获取MCP工具
        
        Args:
            query: 用户查询
            top_k: 最多选择的MCP服务数量
            
        Returns:
            MCP工具列表
        """
        # 路由到相关的MCP服务
        routing_result = await self.route_mcp_servers(query, top_k=top_k)
        selected_servers = routing_result["selected_servers"]
        
        if not selected_servers:
            logger.info("未选择任何MCP服务")
            return []
        
        try:
            # 构建MultiServerMCPClient的连接配置
            connections = {}
            for server_name in selected_servers:
                if server_name in self.mcp_servers:
                    server_config = self.mcp_servers[server_name]
                    connections[server_name] = {
                        "url": server_config["url"],
                        "transport": server_config.get("transport", "streamable_http")
                    }
            
            if connections:
                logger.info(f"动态连接MCP服务: {list(connections.keys())}")
                client = MultiServerMCPClient(connections)
                tools = await client.get_tools()
                logger.info(f"成功加载 {len(tools)} 个MCP工具")
                return tools
            else:
                logger.warning("未找到有效的MCP服务配置")
                return []
                
        except Exception as e:
            logger.error(f"动态加载MCP工具失败: {e}")
            return []
    
    async def reload_config(self):
        """重新加载配置并重建索引"""
        logger.info("重新加载MCP配置...")
        self._load_config()
        
        # 删除旧集合
        if utility.has_collection(self.collection_name):
            utility.drop_collection(self.collection_name)
        
        # 重新设置集合和构建索引
        self._setup_collection()
        await self._build_index_async()
    
    def get_server_info(self, server_name: str) -> Optional[Dict]:
        """获取指定MCP服务的详细信息"""
        try:
            # 检查集合是否已初始化
            if self.collection is None:
                logger.error("集合未初始化，无法获取服务信息")
                return None
            
            self.collection.load()
            expr = f'server_name == "{server_name}"'
            results = self.collection.query(
                expr=expr,
                output_fields=["server_name", "description", "capabilities", "use_cases", "url", "transport"]
            )
            
            if results:
                return results[0]
            return None
            
        except Exception as e:
            logger.error(f"获取服务信息失败: {e}")
            return None
    
    def list_all_servers(self) -> List[str]:
        """列出所有可用的MCP服务名称"""
        try:
            # 检查集合是否已初始化
            if self.collection is None:
                logger.error("集合未初始化，无法列出服务")
                return []
            
            self.collection.load()
            results = self.collection.query(
                expr="",
                output_fields=["server_name"],
                limit=100  # 添加limit参数
            )
            return [result["server_name"] for result in results]
            
        except Exception as e:
            logger.error(f"列出服务失败: {e}")
            return []
    
    def __del__(self):
        """清理资源"""
        try:
            if hasattr(self, 'collection') and self.collection is not None:
                self.collection.release()
        except:
            pass