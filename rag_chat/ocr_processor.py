import os
import json
import base64
import asyncio
import tempfile
import logging
import shutil
import time
from io import BytesIO
from concurrent.futures import <PERSON>PoolExecutor, ThreadPoolExecutor

import fitz
import requests
import docx
from docx import Document
from docx.shared import Pt, Inches, RGBColor
from docx.enum.text import WD_PARAGRAPH_ALIGNMENT, WD_LINE_SPACING
from docx.enum.table import WD_TABLE_ALIGNMENT
from docx.oxml.ns import nsdecls
from docx.oxml import parse_xml

from ocr_config import get_ocr_config

# 配置日志
logger = logging.getLogger(__name__)


class APIClient:
  def __init__(self, base_url, auth_token):
    self.base_url = base_url
    self.auth_token = auth_token
  
  def upload_image(self, track_id, image_path, category='', params=None):
    """
    上传图像到OCR服务

    Args:
        track_id: 跟踪ID
        image_path: 图像文件路径
        category: 类别
        params: OCR参数

    Returns:
        Response: 请求响应
    """
    logger.info(f"开始上传图片到OCR服务: {image_path}, track_id: {track_id}, URL: {self.base_url}")
    if not params:
      params = {}
    
    url = self.base_url
    files = {
      'picFile': open(image_path, 'rb'),
      'trackId': (None, str(track_id)),
    }
    data = {
      'category': category,
      'params': json.dumps(params)
    }
    headers = {
      'Authorization': self.auth_token
    }
    
    try:
      response = requests.post(url, files=files, data=data, headers=headers)
      response.raise_for_status()
      logger.info(f"图片上传成功: {image_path}, 状态码: {response.status_code}")
      return response
    except requests.exceptions.RequestException as e:
      logger.error(f"图片上传到OCR服务失败: {image_path}, 错误: {e}")
      raise
    finally:
      files['picFile'].close()


def create_paragraph_with_format(doc, text, style_info=None):
  """创建格式化的段落"""
  logger.info(f"开始创建格式化段落: {text[:50]}...")
  paragraph = doc.add_paragraph(style='Normal')
  if style_info:
    run = paragraph.add_run(text)
    font = run.font
    if 'font_size' in style_info:
      font.size = Pt(style_info['font_size'])
    if 'bold' in style_info:
      font.bold = style_info['bold']
    if 'italic' in style_info:
      font.italic = style_info['italic']
    if 'underline' in style_info:
      font.underline = style_info['underline']
    if 'color' in style_info:
      font.color.rgb = RGBColor(*style_info['color'])
    if 'alignment' in style_info:
      paragraph.alignment = getattr(WD_PARAGRAPH_ALIGNMENT, style_info['alignment'].upper())
    if 'indent' in style_info:
      paragraph.paragraph_format.first_line_indent = Inches(style_info['indent'])
    if 'line_spacing' in style_info:
      paragraph.paragraph_format.line_spacing_rule = getattr(WD_LINE_SPACING, style_info['line_spacing'].upper())
      paragraph.paragraph_format.line_spacing = style_info.get('line_spacing_value', 1.0)
  else:
    paragraph.add_run(text)
  logger.info("格式化段落创建完成")
  return paragraph


def create_table(doc, table_info):
  """创建表格"""
  logger.info(f"开始创建表格: rows={table_info.get('rows', 1)}, cols={table_info.get('cols', 1)}")
  rows = table_info.get('rows', 1)
  cols = table_info.get('cols', 1)
  table = doc.add_table(rows=rows, cols=cols)
  for i, row in enumerate(table.rows):
    for j, cell in enumerate(row.cells):
      cell_text = table_info.get('content', {}).get(f'{i},{j}', '')
      cell.text = cell_text
      if 'cell_style' in table_info:
        shading_elm = parse_xml(r'<w:shd {} w:fill="{color}"/>'.format(nsdecls('w'),
                                                                       color=table_info['cell_style'].get(
                                                                         'background_color', 'FFFFFF')))
        cell._tc.get_or_add_tcPr().append(shading_elm)
        for paragraph in cell.paragraphs:
          for run in paragraph.runs:
            font = run.font
            if 'font_size' in table_info['cell_style']:
              font.size = Pt(table_info['cell_style']['font_size'])
            if 'bold' in table_info['cell_style']:
              font.bold = table_info['cell_style']['bold']
            if 'italic' in table_info['cell_style']:
              font.italic = table_info['cell_style']['italic']
            if 'underline' in table_info['cell_style']:
              font.underline = table_info['cell_style']['underline']
            if 'color' in table_info['cell_style']:
              font.color.rgb = RGBColor(*table_info['cell_style']['color'])
  if 'table_style' in table_info:
    if 'alignment' in table_info['table_style']:
      table.alignment = getattr(WD_TABLE_ALIGNMENT, table_info['table_style']['alignment'].upper())
    if 'border' in table_info['table_style']:
      tbl_pr = table._tblPr
      tbl_borders = parse_xml(f'<w:tblBorders {nsdecls("w")}>' +
                              f'<w:top w:val="single" w:sz="4" w:space="0" w:color="{table_info["table_style"]["border"]}" />' +
                              f'<w:left w:val="single" w:sz="4" w:space="0" w:color="{table_info["table_style"]["border"]}" />' +
                              f'<w:bottom w:val="single" w:sz="4" w:space="0" w:color="{table_info["table_style"]["border"]}" />' +
                              f'<w:right w:val="single" w:sz="4" w:space="0" w:color="{table_info["table_style"]["border"]}" />' +
                              f'<w:insideH w:val="single" w:sz="4" w:space="0" w:color="{table_info["table_style"]["border"]}" />' +
                              f'<w:insideV w:val="single" w:sz="4" w:space="0" w:color="{table_info["table_style"]["border"]}" />' +
                              f'</w:tblBorders>')
      tbl_pr.append(tbl_borders)
  logger.info("表格创建完成")
  return table


def add_images(doc, image_info):
  """添加图片到文档"""
  logger.info(f"开始添加图片: {image_info}")
  for img in image_info:
    doc.add_picture(img['path'], width=Inches(img.get('width', 4)))
  logger.info("图片添加完成")


def parse_and_create_word_document(json_responses, output_path):
  """
  解析OCR JSON响应并创建Word文档

  Args:
      json_responses: OCR JSON响应列表
      output_path: 输出Word文档路径

  Returns:
      dict: 处理结果，包含状态码和消息
  """
  logger.info(f"开始解析并创建 Word 文档: {output_path}")
  doc = Document()
  style = doc.styles['Normal']
  font = style.font
  font.name = 'SimSun'
  font.size = Pt(12)
  
  try:
    for json_response in json_responses:
      json_response = json_response.json()
      json_response = json_response.get('body')
      if not json_response:
        logger.warning("OCR响应中没有body字段")
        continue
      
      if isinstance(json_response, str):
        try:
          response_data = json.loads(json_response)
        except json.JSONDecodeError:
          logger.error(f"无法解析OCR响应body: {json_response[:100]}...")
          continue
      else:
        response_data = json_response
      
      # 处理word类型文档
      documents = response_data.get('documents', [])
      for doc_info in documents:
        if doc_info.get("type") == "word" and doc_info.get("content"):
          try:
            logger.info("处理word类型的OCR结果")
            word_content = base64.b64decode(doc_info["content"])
            byte_stream = BytesIO(word_content)
            temp_doc = Document(byte_stream)
            for element in temp_doc.element.body:
              doc.element.body.append(element)
          except Exception as e:
            logger.error(f"处理文档内容失败: {e}")
      
      # 处理页面内容
      pages = response_data.get('pages', [])
      for page in pages:
        for block in page.get("blocks", []):
          block_type = block.get('type')
          if block_type == 'text':
            lines = block.get('lines', [])
            for line in lines:
              content = ''
              style_info = {}
              for unit in line.get('word_units', []):
                content += unit.get('content', '')
                if 'style_info' in unit:
                  style_info.update(unit['style_info'])
              create_paragraph_with_format(doc, content, style_info)
          elif block_type == 'table':
            table_info = {
              'rows': len(block.get('rows', [])),
              'cols': len(block.get('cols', [])),
              'content': block.get('content', {})
            }
            create_table(doc, table_info)
          elif block_type == 'image':
            image_info = [{
              'path': block.get('path', ''),
              'width': block.get('width', 4)
            }]
            add_images(doc, image_info)
      
      # 如果没有从上面提取到内容，尝试从fullText字段提取
      if not doc.paragraphs or all(not p.text.strip() for p in doc.paragraphs):
        if response_data.get("fullText"):
          logger.info("从fullText字段提取文本")
          doc.add_paragraph(response_data["fullText"])
    
    doc.save(output_path)
    logger.info(f"Word 文档创建成功: {output_path}")
    return {"code": 200, "message": "OK", "data": output_path}
  except Exception as e:
    logger.error(f"解析并创建 Word 文档失败: {e}")
    return {"code": 500, "message": str(e)}


def extract_image_from_page_worker(pdf_path, page_num, output_path):
  """
  从PDF页面提取图像的worker函数（在子进程中运行）

  Args:
      pdf_path: PDF文件路径
      page_num: 页码
      output_path: 输出图像路径

  Returns:
      str: 成功则返回图像路径，失败则返回None
  """
  try:
    pdf_document = fitz.open(pdf_path)
    page = pdf_document.load_page(page_num)
    pix = page.get_pixmap(alpha=False)
    pix.save(output_path)
    pdf_document.close()
    return output_path
  except Exception as e:
    logger.error(f"从PDF第{page_num + 1}页提取图像失败: {e}")
    return None

def extract_text_from_docx(docx_path):
  """
  从Word文档中提取文本

  Args:
      docx_path: Word文档路径

  Returns:
      str: 提取的文本
  """
  try:
    doc = docx.Document(docx_path)
    text = ""
    
    # 提取段落文本
    for i, para in enumerate(doc.paragraphs):
      if para.text.strip():
        text += para.text.strip() + "\n"
    
    # 处理表格
    for i, table in enumerate(doc.tables):
      text += f"表格{i + 1}：\n"
      for row in table.rows:
        row_texts = []
        for cell in row.cells:
          row_texts.append(cell.text.strip())
        text += " | ".join(row_texts) + "\n"
      text += "\n"
    
    return text
  except Exception as e:
    logger.error(f"从Word文档提取文本失败: {e}")
    # 如果文件存在但无法解析，返回一个错误信息而不是空字符串
    if os.path.exists(docx_path):
      return f"无法从文档中提取文本，可能是格式不兼容: {str(e)}"
    return "文档不存在或处理过程中被删除"


async def add_images_to_pdf(pdf_path, max_size):
  """
  处理图片类PDF文件，使用OCR服务提取文本

  Args:
      pdf_path: PDF文件路径
      max_size: 最大文本大小

  Returns:
      tuple: (text, processed_pages, total_pages)
  """
  start_time = time.time()
  logger.info(f"开始OCR处理PDF: {pdf_path}")
  
  # 获取OCR配置
  ocr_config = get_ocr_config()
  base_url = ocr_config["base_url"]
  auth_token = ocr_config["auth_token"]
  track_id = ocr_config["track_id"]
  params = ocr_config["params"]
  
  # 创建API客户端
  client = APIClient(base_url, auth_token)
  
  # 创建临时目录
  temp_dir = tempfile.mkdtemp(prefix="ocr_temp_")
  logger.info(f"创建临时目录: {temp_dir}")
  
  try:
    # 获取PDF页数
    with fitz.open(pdf_path) as pdf_document:
      total_pages = len(pdf_document)
      logger.info(f"PDF总页数: {total_pages}")
    
    # 并行处理
    loop = asyncio.get_running_loop()
    all_results = []
    processed_pages = 0
    
    # 提取图像
    with ProcessPoolExecutor() as executor:
      # 准备图像提取任务
      extract_tasks = []
      for page_num in range(total_pages):
        output_path = os.path.join(temp_dir, f"page_{page_num}.png")
        task = loop.run_in_executor(
          executor,
          extract_image_from_page_worker,
          pdf_path,
          page_num,
          output_path
        )
        extract_tasks.append(task)
      
      # 等待所有图像提取完成
      image_paths = await asyncio.gather(*extract_tasks)
      image_paths = [p for p in image_paths if p]  # 过滤掉提取失败的页面
      logger.info(f"成功提取图像数量: {len(image_paths)}/{total_pages}")
    
    # 上传图像到OCR服务
    with ThreadPoolExecutor(max_workers=min(10, len(image_paths))) as executor:
      # 对提取的图像排序，确保按顺序处理
      sorted_images = sorted(image_paths, key=lambda x: int(os.path.basename(x).split('_')[1].split('.')[0]))
      
      upload_tasks = []
      for image_path in sorted_images[:max_size]:  # 限制处理页数
        task = loop.run_in_executor(
          executor,
          client.upload_image,
          track_id,
          image_path,
          '',
          params
        )
        upload_tasks.append(task)
      
      # 等待所有上传任务完成
      upload_results = await asyncio.gather(*upload_tasks, return_exceptions=True)
      
      # 处理上传结果
      for i, result in enumerate(upload_results):
        if isinstance(result, Exception):
          logger.error(f"上传图像失败: {result}")
        else:
          all_results.append(result)
          processed_pages += 1
      
      logger.info(f"成功上传图像数量: {processed_pages}/{len(sorted_images)}")
    
    # 创建Word文档
    output_docx_path = os.path.join(temp_dir, "ocr_result.docx")
    parse_result = await loop.run_in_executor(
      None,  # 使用默认执行器
      parse_and_create_word_document,
      all_results,
      output_docx_path
    )
    
    if parse_result["code"] != 200:
      logger.error(f"创建Word文档失败: {parse_result['message']}")
      return "OCR处理失败，无法创建文档", 0, total_pages
    
    # 从Word文档中提取文本
    text = await loop.run_in_executor(
      None,
      extract_text_from_docx,
      output_docx_path
    )
    
    elapsed_time = time.time() - start_time
    logger.info(f"OCR处理PDF完成: {pdf_path}, 耗时: {elapsed_time:.2f}秒, 提取文本长度: {len(text)}")
    
    return text, processed_pages, total_pages
  
  except Exception as e:
    logger.error(f"OCR处理PDF失败: {e}", exc_info=True)
    return f"OCR处理失败: {str(e)}", 0, 0
  
  finally:
    # 清理临时文件
    try:
      shutil.rmtree(temp_dir)
      logger.info(f"已清理临时目录: {temp_dir}")
    except Exception as e:
      logger.error(f"清理临时目录失败: {e}")