import os
import fitz
import logging

# 配置日志
logger = logging.getLogger(__name__)


def is_standard_pdf(file_path):
  """
  检查PDF是否为标准PDF（包含文本）或扫描PDF（图片）

  Args:
      file_path: PDF文件路径

  Returns:
      tuple: (is_standard, reason)
          is_standard: 布尔值，True表示标准PDF，False表示扫描PDF
          reason: 原因说明
  """
  try:
    # 检查文件是否存在
    if not os.path.exists(file_path):
      return False, "文件不存在"
    
    # 检查文件大小
    file_size = os.path.getsize(file_path)
    if file_size == 0:
      return False, "文件大小为0"
    
    # 打开PDF文件
    doc = fitz.open(file_path)
    
    # 检查页数
    if len(doc) == 0:
      doc.close()
      return False, "PDF没有页面"
    
    # 检查是否有文本
    text_length = 0
    for page in doc:
      text = page.get_text()
      text_length += len(text)
      if len(text) > 50:  # 如果页面有足够的文本
        doc.close()
        return True, f"页面包含文本：{len(text)}字符"
    
    doc.close()
    
    # 如果所有页面文本总量很少，则可能是扫描PDF
    if text_length < 100:
      return False, f"所有页面文本总量太少：{text_length}字符"
    
    return True, f"标准PDF，文本总量：{text_length}字符"
  
  except Exception as e:
    logger.error(f"检查PDF类型时出错: {e}", exc_info=True)
    return False, f"检查过程出错: {str(e)}"