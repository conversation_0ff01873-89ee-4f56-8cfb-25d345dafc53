"""
简单的MCP服务路由器
通过API调用ai/mcpServer/listBylds获取用户可用的MCP服务列表，直接连接所有服务
与MCPRAGRouter保持一致的接口设计
"""

import json
import os
import requests
from typing import Dict, List, Any, Optional
from langchain_mcp_adapters.client import MultiServerMCPClient
from .logger import logger
from dotenv import load_dotenv

load_dotenv()

class MCPSimpleRouter:
    """简单的MCP服务路由器 - 基于API获取用户MCP服务列表"""
    
    def __init__(self, api_base_url: str):
        """
        初始化简单MCP路由器
        
        Args:
            api_base_url: MCP服务API的基础URL
        """
        self.api_base_url = api_base_url.rstrip('/')
        self.mcp_servers = {}
        self.current_user_id = None
        
        logger.info(f"简单MCP路由器初始化完成，API地址: {self.api_base_url}")
    
    async def initialize_for_user(self, mcp_ids: list, api_key: str = None, username: str = None) -> bool:
        """
        为指定用户初始化MCP服务配置
        
        Args:
            mcp_ids: MCP服务ID列表
            api_key: API密钥（从请求头获取）
            username: 用户名（从请求头获取）
            
        Returns:
            初始化是否成功
        """
        try:
            # 获取用户可用的MCP服务列表
            mcp_list = await self._fetch_user_mcp_servers(mcp_ids, api_key, username)
            logger.info("mcp_list: {}".format(mcp_list))
            
            if not mcp_list:
                logger.warning("没有可用的MCP服务")
                return False
            
            # 将API返回的服务列表转换为内部格式
            self.mcp_servers = self._convert_api_to_internal_format(mcp_list)
            self.current_mcp_ids = mcp_ids
            
            logger.info(f"MCP服务配置加载完成，共 {len(self.mcp_servers)} 个服务")
            return True
            
        except Exception as e:
            logger.error(f"MCP服务初始化失败: {e}")
            return False
    
    async def _fetch_user_mcp_servers(self, mcp_ids: list, api_key: str = None, username: str = None) -> List[Dict[str, Any]]:
        """
        通过API获取用户可用的MCP服务列表
        
        Args:
            mcp_ids: MCP服务ID列表
            api_key: API密钥（从请求头获取）
            username: 用户名（从请求头获取）
            
        Returns:
            MCP服务列表
        """
        try:
            api_url = f"{self.api_base_url}/ai/mcpServer/listByIds"
            logger.info("mcp url: {}".format(api_url))
            
            # 构建请求数据
            request_data = {
                "ids": mcp_ids
            }
            
            # 构建请求头
            headers = {
                "Content-Type": "application/json",
                "X-API-Key": api_key or os.getenv("MCP-X-API-Key", ""),
                "X-Username": username or os.getenv("MCP-X-Username", "")
            }

            logger.info(f"mcp 查询请求头：{headers}")
            
            # 发送POST请求
            response = requests.post(
                api_url,
                json=request_data,
                headers=headers,
                timeout=150
            )

            logger.error(response)
            
            if response.status_code != 200:
                logger.info(f"获取MCP服务列表失败，状态码: {response.status_code}, 错误信息: {response.text}")
                return []
            
            result = response.json()
            
            # 假设API返回格式为 {"code": 0, "data": [mcp_server_list]}
            if result.get("code") == '200' and "data" in result:
                mcp_list = result["data"]
                logger.info(f"获取到 {len(mcp_list)} 个MCP服务")
                return mcp_list
            else:
                logger.error(f"API返回错误: {result}")
                return []
                
        except requests.exceptions.RequestException as e:
            logger.error(f"API请求失败: {e}")
            return []
        except json.JSONDecodeError as e:
            logger.error(f"API响应解析失败: {e}")
            return []
        except Exception as e:
            logger.error(f"获取MCP服务列表时发生未知错误: {e}")
            return []
    
    def _convert_api_to_internal_format(self, mcp_list: List[Dict[str, Any]]) -> Dict[str, Dict[str, Any]]:
        """
        将API返回的MCP服务列表转换为内部格式（与MCPRAGRouter保持一致）
        
        Args:
            mcp_list: API返回的MCP服务列表
            
        Returns:
            内部格式的MCP服务配置字典
        """
        servers = {}
        
        for mcp_server in mcp_list:
            try:
                name = mcp_server.get("name")
                loading_type = mcp_server.get("loadingType", "1")
                
                if not name:
                    logger.warning("跳过未命名的MCP服务")
                    continue
                
                # 构建内部格式的服务配置
                server_config = {
                    "description": mcp_server.get("description", ""),
                    "detail": mcp_server.get("detail", ""),
                    "tag": mcp_server.get("tag", ""),
                    "loadingType": loading_type,
                    "capabilities": [mcp_server.get("description", "")],  # 使用描述作为能力说明
                    "use_cases": [mcp_server.get("detail", "")]  # 使用详细描述作为用例
                }
                
                # 根据加载类型设置连接信息
                if loading_type == 1:  # stdio模式
                    server_config["transport"] = "stdio"
                    server_config["command"] = mcp_server.get("args", "").split() if mcp_server.get("args") else []
                    server_config["env"] = self._parse_env_string(mcp_server.get("env", ""))
                    
                elif loading_type == 2:  # SSE模式
                    url = mcp_server.get("url")
                    if not url:
                        logger.warning(f"MCP服务 {name} 的SSE模式缺少URL，跳过")
                        continue
                    
                    server_config["transport"] = "sse"

                    server_config["url"] = url
                
                elif loading_type == 3:  # streamable_http模式
                    url = mcp_server.get("url")
                    if not url:
                        logger.warning(f"MCP服务 {name} 的streamable_http模式缺少URL，跳过")
                        continue

                    server_config["transport"] = "streamable_http"
                    server_config["url"] = url
                
                else:
                    logger.warning(f"未知的加载类型 {loading_type}，跳过MCP服务 {name}")
                    continue
                
                servers[name] = server_config
                logger.info(f"转换MCP服务配置: {name} (类型: {loading_type})")
                
            except Exception as e:
                logger.warning(f"转换MCP服务配置时出错: {e}")
                continue
        
        return servers
    
    def _parse_env_string(self, env_string: str) -> Dict[str, str]:
        """
        解析环境变量字符串为字典
        
        Args:
            env_string: 环境变量字符串，格式如 "KEY1=value1;KEY2=value2"
            
        Returns:
            环境变量字典
        """
        env_dict = {}
        if env_string:
            for env_pair in env_string.split(";"):
                if "=" in env_pair:
                    key, value = env_pair.split("=", 1)
                    env_dict[key.strip()] = value.strip()
        return env_dict
    
    async def get_dynamic_mcp_tools(self, query: str = "", top_k: int = None) -> List:
        """
        根据查询动态获取MCP工具（简单版本：返回所有可用工具）
        
        Args:
            query: 用户查询（在简单版本中忽略，返回所有工具）
            top_k: 最多选择的MCP服务数量（在简单版本中忽略）
            
        Returns:
            MCP工具列表
        """
        if not self.mcp_servers:
            logger.info("没有可用的MCP服务")
            return []
        
        try:
            # 构建MultiServerMCPClient的连接配置
            connections = {}
            for server_name, server_config in self.mcp_servers.items():
                connection_config = {}
                
                if server_config["transport"] == "streamable_http":
                    connection_config = {
                        "url": server_config["url"],
                        "transport": "streamable_http"
                    }
                elif server_config["transport"] == "stdio":
                    connection_config = {
                        "transport": "stdio",
                        "command": server_config["command"]
                    }
                    if server_config.get("env"):
                        connection_config["env"] = server_config["env"]
                elif server_config["transport"] == "sse":
                    connection_config = {
                        "transport": "sse",
                        "url": server_config["url"]
                    }
                else:
                    logger.warning(f"未知的传输类型 {server_config['transport']}，跳过MCP服务 {server_name}")
                    continue
                
                if connection_config:
                    connections[server_name] = connection_config
            
            if connections:
                logger.info(f"连接MCP服务: {list(connections.keys())}")
                logger.info(f"connections: {connections}")
                client = MultiServerMCPClient(connections)
                tools = await client.get_tools()
                logger.info(f"成功加载 {len(tools)} 个MCP工具")
                return tools
            else:
                logger.warning("未找到有效的MCP服务配置")
                return []
                
        except Exception as e:
            logger.error(f"动态加载MCP工具失败: {e}")
            return []
    
    async def route_mcp_servers(self, query: str, top_k: int = None) -> Dict[str, Any]:
        """
        路由MCP服务（简单版本：返回所有服务）
        
        Args:
            query: 用户查询
            top_k: 返回top-k个最相关的服务
            
        Returns:
            包含选中服务的字典
        """
        try:
            if not self.mcp_servers:
                return {
                    "selected_servers": [],
                    "scores": [],
                    "reasoning": "没有可用的MCP服务"
                }
            
            # 简单版本：返回所有可用的服务
            selected_servers = list(self.mcp_servers.keys())
            scores = [0.0] * len(selected_servers)  # 简单版本给予相同分数
            reasoning = f"简单路由模式：返回所有 {len(selected_servers)} 个可用的MCP服务"
            
            logger.info(f"为查询 '{query}' 返回所有 {len(selected_servers)} 个MCP服务")
            
            return {
                "selected_servers": selected_servers,
                "scores": scores,
                "reasoning": reasoning
            }
            
        except Exception as e:
            logger.error(f"MCP服务路由失败: {e}")
            return {
                "selected_servers": [],
                "scores": [],
                "reasoning": f"路由过程出错: {str(e)}"
            }
    
    def get_server_info(self, server_name: str) -> Optional[Dict]:
        """获取指定MCP服务的详细信息"""
        return self.mcp_servers.get(server_name)
    
    def list_all_servers(self) -> List[str]:
        """列出所有可用的MCP服务名称"""
        return list(self.mcp_servers.keys())
    
    def get_mcp_ids(self) -> Optional[list]:
        """获取当前用户ID"""
        return self.current_mcp_ids
    
    async def reload_config_for_user(self, mcp_ids: list = None):
        """重新加载指定用户的配置"""
        mcp_ids = mcp_ids or self.current_mcp_ids
        if mcp_ids:
            logger.info(f"重新加载MCP配置...")
            await self.initialize_for_user(mcp_ids)
        else:
            logger.warning("没有指定用户ID，无法重新加载配置")