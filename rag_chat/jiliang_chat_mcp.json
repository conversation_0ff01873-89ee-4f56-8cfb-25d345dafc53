{"mcp_servers": {"langgraph-docs": {"url": "http://127.0.0.1:8001/sse", "transport": "streamable_http", "description": "LangGraph和LangChain框架的官方文档查询工具。用于回答关于LangGraph工作流、节点、边、状态管理、LangChain组件使用等技术问题。适用于开发者需要了解框架API、最佳实践、示例代码等场景。", "capabilities": ["查询LangGraph工作流创建方法", "LangChain工具调用和组件使用", "状态管理和检查点功能说明", "框架API文档和参数说明", "最佳实践和示例代码"], "use_cases": ["如何使用LangGraph创建工作流", "LangChain的工具调用方法", "状态管理和检查点功能", "框架API文档查询"]}, "web-search": {"url": "http://127.0.0.1:8002/sse", "transport": "streamable_http", "description": "实时网络搜索工具，能够获取最新的互联网信息、新闻、技术动态等。适用于需要最新信息、实时数据、当前事件、市场动态等无法从本地知识库获得的信息查询。", "capabilities": ["实时网络信息搜索", "最新新闻和技术动态获取", "市场信息和股票价格查询", "天气和交通实时信息", "当前事件和热点话题"], "use_cases": ["最新新闻和时事", "股票价格和市场信息", "技术发展动态", "实时天气和交通信息"]}, "file-operations": {"url": "http://127.0.0.1:8003/sse", "transport": "streamable_http", "description": "文件系统操作工具，提供文件和目录的创建、读取、写入、删除、移动等功能。适用于需要处理本地文件、管理项目文件、批量文件操作等场景。", "capabilities": ["文件内容读取和写入", "目录创建和管理", "文件移动、复制、删除", "批量文件处理", "文件权限和属性管理"], "use_cases": ["读取和写入文件内容", "创建和删除目录", "文件批量处理", "项目文件管理"]}, "database-query": {"url": "http://127.0.0.1:8004/sse", "transport": "streamable_http", "description": "数据库查询和操作工具，支持SQL查询、数据分析、报表生成等功能。适用于数据查询、统计分析、业务报表、数据维护等数据库相关操作。", "capabilities": ["复杂SQL查询执行", "数据统计和分析", "业务报表生成", "数据库结构查询", "数据导入导出"], "use_cases": ["执行复杂SQL查询", "生成数据统计报表", "数据库结构查询", "业务数据分析"]}, "code-analysis": {"url": "http://127.0.0.1:8005/sse", "transport": "streamable_http", "description": "代码分析和编程辅助工具，提供代码审查、bug检测、性能分析、重构建议等功能。适用于代码质量检查、技术债务分析、编程最佳实践建议等开发场景。", "capabilities": ["代码质量检查和改进建议", "性能瓶颈识别和优化", "安全漏洞扫描和修复", "代码重构和架构优化", "编程最佳实践指导"], "use_cases": ["代码质量分析和改进建议", "性能瓶颈检测", "安全漏洞扫描", "重构和优化建议"]}}}