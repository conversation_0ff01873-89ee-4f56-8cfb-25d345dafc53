import time
import json
import asyncio
from datetime import datetime
from functools import wraps
from typing import Optional, Dict, Any
from fastapi import Request
from dm_util import DMDatabase
from logger import logger
import os

# 达梦配置
dm_config = {
  'host': os.getenv('DM_HOST'),
  'port': int(os.getenv('DM_PORT')),
  'user': os.getenv('DM_USER'),
  'password': os.getenv('DM_PASSWORD'),
  'database': os.getenv('DM_DB'),
  'auto_commit': False
}


class SimplifiedStatsService:
  """简化的功能调用统计服务 - 扩展现有表"""
  
  @staticmethod
  def _get_function_info(function_type: str) -> Optional[Dict[str, str]]:
    """根据功能类型获取功能信息"""
    try:
      with DMDatabase(**dm_config) as db:
        sql = """
                SELECT "FUNCTION_NAME", "CATEGORY", "DESCRIPTION"
                FROM "SX_AIPLATFORM"."T_FUNCTION_TYPE_CONFIG"
                WHERE "FUNCTION_TYPE" = ? AND "IS_ACTIVE" = '1'
                """
        result = db.execute_query(sql, (function_type,))
        if result:
          return {
            'function_name': result[0]['FUNCTION_NAME'],
            'category': result[0].get('CATEGORY', ''),
            'description': result[0].get('DESCRIPTION', '')
          }
    except Exception as e:
      logger.error(f"获取功能信息失败: {e}")
    return None


def save_record_dm_with_stats(question, answer, reasoning, chat_type=0, userid='', apikey='', qa_id='',
                              function_type=None, function_name=None, request_time=None, response_time=None,
                              duration_ms=None, status='SUCCESS', file_count=0, model_name=None, temperature=None):
  """保存记录到吉量表（扩展版本）"""
  try:
    with DMDatabase(**dm_config) as db:
      # 如果没有提供功能名称，尝试查询
      if function_type and not function_name:
        func_info = SimplifiedStatsService._get_function_info(function_type)
        if func_info:
          function_name = func_info['function_name']
      
      sql = """
            INSERT INTO "SX_AIPLATFORM"."SX_JILIANG_RECORDS"
            (QUESTION, ANSWER, REASONING, CHAT_TYPE, createtime, userid, apikey, QA_ID,
             FUNCTION_TYPE, FUNCTION_NAME, REQUEST_TIME, RESPONSE_TIME, DURATION_MS,
             STATUS, FILE_COUNT, MODEL_NAME, TEMPERATURE)
            VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?)
            """
      
      values = (
        question, answer, reasoning, chat_type,
        datetime.now().strftime("%Y-%m-%d %H:%M:%S"),
        userid, apikey, qa_id,
        function_type, function_name, request_time, response_time,
        duration_ms, status, file_count, model_name, temperature
      )
      
      insert_count = db.execute_update(sql, values)
      logger.info(f"插入吉量记录成功: {insert_count}行, 功能类型: {function_type}")
      db.commit()
      return True
  except Exception as e:
    logger.error(f"保存吉量记录异常: {str(e)}", exc_info=True)
    if 'db' in locals():
      db.rollback()
    return False


def save_wps_record_dm_with_stats(question, answer, reasoning, userid='', apikey='', qa_id='',
                                  function_type=None, function_name=None, request_time=None, response_time=None,
                                  duration_ms=None, status='SUCCESS', model_name=None, temperature=None):
  """保存记录到WPS表（扩展版本）"""
  try:
    with DMDatabase(**dm_config) as db:
      # 如果没有提供功能名称，尝试查询
      if function_type and not function_name:
        func_info = SimplifiedStatsService._get_function_info(function_type)
        if func_info:
          function_name = func_info['function_name']
      
      sql = """
            INSERT INTO "SX_AIPLATFORM"."SX_WPS_RECORDS"
            (QUESTION, ANSWER, REASONING, createtime, userid, apikey, QA_ID, CHAT_TYPE,
             FUNCTION_TYPE, FUNCTION_NAME, REQUEST_TIME, RESPONSE_TIME, DURATION_MS,
             STATUS, MODEL_NAME, TEMPERATURE)
            VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?)
            """
      
      values = (
        question, answer, reasoning,
        datetime.now().strftime("%Y-%m-%d %H:%M:%S"),
        userid, apikey, qa_id, 0,  # WPS默认chat_type为0
        function_type, function_name, request_time, response_time,
        duration_ms, status, model_name, temperature
      )
      
      insert_count = db.execute_update(sql, values)
      logger.info(f"插入WPS记录成功: {insert_count}行, 功能类型: {function_type}")
      db.commit()
      return True
  except Exception as e:
    logger.error(f"保存WPS记录异常: {str(e)}", exc_info=True)
    if 'db' in locals():
      db.rollback()
    return False


def function_stats_simple(table_type='jiliang'):
  """简化的功能调用统计装饰器"""
  
  def decorator(func):
    @wraps(func)
    async def wrapper(request: Request, *args, **kwargs):
      # 提取请求信息
      header = dict(request.headers)
      user_id = header.get('x-username', '')
      api_key = header.get('x-api-key', '')
      
      # 从请求体中提取功能类型等信息
      function_type = None
      function_name = None
      qa_id = None
      model_name = None
      temperature = None
      file_count = 0
      chat_type = 0
      
      # 尝试从不同的参数位置获取信息
      if args:
        body = args[0] if hasattr(args[0], '__dict__') else None
        if body:
          function_type = getattr(body, 'function_type', None)
          function_name = getattr(body, 'function_name', None)
          qa_id = getattr(body, 'qa_id', None)
          model_name = getattr(body, 'model', None)
          temperature = getattr(body, 'temperature', None)
          chat_type = getattr(body, 'chat_type', 0)
          # 检查文件相关参数
          if hasattr(body, 'file_ids') and body.file_ids:
            file_count = len(body.file_ids)
          elif hasattr(body, 'file_id') and body.file_id:
            file_count = 1
      
      # 记录开始时间
      start_time = datetime.now()
      start_timestamp = time.time()
      
      try:
        # 执行原函数
        result = await func(request, *args, **kwargs)
        
        # 如果提供了功能类型，记录统计信息
        if function_type:
          end_time = datetime.now()
          duration_ms = int((time.time() - start_timestamp) * 1000)
          
          # 提取问题内容
          question = getattr(args[0], 'question', '') if args and hasattr(args[0], 'question') else ''
          
          # 异步保存统计信息（不保存完整的answer，只记录统计信息）
          asyncio.create_task(
            asyncio.get_event_loop().run_in_executor(
              None,
              save_stats_record,
              table_type, question, user_id, api_key, qa_id, function_type, function_name,
              start_time, end_time, duration_ms, 'SUCCESS', file_count, model_name, temperature, chat_type
            )
          )
        
        return result
      
      except Exception as e:
        # 记录错误信息
        if function_type:
          end_time = datetime.now()
          duration_ms = int((time.time() - start_timestamp) * 1000)
          question = getattr(args[0], 'question', '') if args and hasattr(args[0], 'question') else ''
          
          # 异步保存错误统计信息
          asyncio.create_task(
            asyncio.get_event_loop().run_in_executor(
              None,
              save_stats_record,
              table_type, question, user_id, api_key, qa_id, function_type, function_name,
              start_time, end_time, duration_ms, 'ERROR', file_count, model_name, temperature, chat_type
            )
          )
        
        # 重新抛出异常
        raise e
    
    return wrapper
  
  return decorator


def save_stats_record(table_type, question, user_id, api_key, qa_id, function_type, function_name,
                      start_time, end_time, duration_ms, status, file_count, model_name, temperature, chat_type):
  """保存统计记录（只包含统计信息，不包含完整答案）"""
  try:
    # 截断问题长度以节省空间
    question_summary = question[:200] + '...' if len(question) > 200 else question
    
    if table_type == 'wps':
      save_wps_record_dm_with_stats(
        question_summary, '', '',  # answer和reasoning为空，只记录统计
        user_id, api_key, qa_id, function_type, function_name,
        start_time, end_time, duration_ms, status, model_name, temperature
      )
    else:  # jiliang
      save_record_dm_with_stats(
        question_summary, '', '', chat_type,  # answer和reasoning为空，只记录统计
        user_id, api_key, qa_id, function_type, function_name,
        start_time, end_time, duration_ms, status, file_count, model_name, temperature
      )
  except Exception as e:
    logger.error(f"保存统计记录失败: {e}")


class StatsQueryService:
  """统计查询服务"""
  
  @staticmethod
  def get_function_stats_from_existing_tables(table_name='SX_JILIANG_RECORDS', start_date=None, end_date=None,
                                              function_type=None):
    """从现有表查询功能统计"""
    try:
      with DMDatabase(**dm_config) as db:
        conditions = ['"FUNCTION_TYPE" IS NOT NULL']
        params = []
        
        if function_type:
          conditions.append('"FUNCTION_TYPE" = ?')
          params.append(function_type)
        
        if start_date:
          conditions.append('"createtime" >= ?')
          params.append(start_date)
        
        if end_date:
          conditions.append('"createtime" <= ?')
          params.append(end_date)
        
        where_clause = ' AND '.join(conditions)
        
        sql = f"""
                SELECT "FUNCTION_TYPE", "FUNCTION_NAME",
                       COUNT(*) as total_calls,
                       COUNT(DISTINCT "userid") as unique_users,
                       AVG("DURATION_MS") as avg_duration,
                       COUNT(CASE WHEN "STATUS" = 'SUCCESS' THEN 1 END) as success_calls,
                       COUNT(CASE WHEN "STATUS" = 'ERROR' THEN 1 END) as error_calls
                FROM "SX_AIPLATFORM"."{table_name}"
                WHERE {where_clause}
                GROUP BY "FUNCTION_TYPE", "FUNCTION_NAME"
                ORDER BY total_calls DESC
                """
        
        result = db.execute_query(sql, params)
        return result
    
    except Exception as e:
      logger.error(f"查询功能统计失败: {e}")
      return []
  
  @staticmethod
  def get_user_function_stats(user_id, table_name='SX_JILIANG_RECORDS', start_date=None, end_date=None):
    """查询用户功能使用统计"""
    try:
      with DMDatabase(**dm_config) as db:
        conditions = ['"userid" = ?', '"FUNCTION_TYPE" IS NOT NULL']
        params = [user_id]
        
        if start_date:
          conditions.append('"createtime" >= ?')
          params.append(start_date)
        
        if end_date:
          conditions.append('"createtime" <= ?')
          params.append(end_date)
        
        where_clause = ' AND '.join(conditions)
        
        sql = f"""
                SELECT "FUNCTION_TYPE", "FUNCTION_NAME",
                       COUNT(*) as call_count,
                       AVG("DURATION_MS") as avg_duration,
                       MAX("createtime") as last_used
                FROM "SX_AIPLATFORM"."{table_name}"
                WHERE {where_clause}
                GROUP BY "FUNCTION_TYPE", "FUNCTION_NAME"
                ORDER BY call_count DESC
                """
        
        result = db.execute_query(sql, params)
        return result
    
    except Exception as e:
      logger.error(f"查询用户功能统计失败: {e}")
      return []