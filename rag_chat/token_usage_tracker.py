"""
Token使用量追踪器

基于LangChain回调机制的全局token使用统计系统，支持按agent和模型分类统计。
"""

import threading
from typing import Dict, Any, Optional, List
from collections import defaultdict
from langchain_core.callbacks import BaseCallbackHandler
from langchain_core.outputs import LLMResult
from logger import logger


class TokenUsageTracker:
    """全局token使用量追踪器"""
    
    def __init__(self):
        """初始化追踪器"""
        self._lock = threading.Lock()
        self.reset_stats()
    
    def reset_stats(self):
        """重置统计数据"""
        with self._lock:
            # 总体统计
            self.total_tokens = 0
            self.total_prompt_tokens = 0
            self.total_completion_tokens = 0
            
            # 按agent分类统计
            self.agent_stats = defaultdict(lambda: {"prompt_tokens": 0, "completion_tokens": 0, "total_tokens": 0})
            
            # 按模型分类统计
            self.model_stats = defaultdict(lambda: {"prompt_tokens": 0, "completion_tokens": 0, "total_tokens": 0})
            
            # 调用次数统计
            self.call_count = 0
            self.agent_calls = defaultdict(int)
            self.model_calls = defaultdict(int)
    
    def add_usage(self, prompt_tokens: int, completion_tokens: int, 
                  agent_name: str = "unknown", model_name: str = "unknown"):
        """添加token使用量记录
        
        Args:
            prompt_tokens: 输入token数量
            completion_tokens: 输出token数量
            agent_name: Agent名称
            model_name: 模型名称
        """
        total = prompt_tokens + completion_tokens
        
        with self._lock:
            # 更新总体统计
            self.total_prompt_tokens += prompt_tokens
            self.total_completion_tokens += completion_tokens
            self.total_tokens += total
            self.call_count += 1
            
            # 更新agent统计
            self.agent_stats[agent_name]["prompt_tokens"] += prompt_tokens
            self.agent_stats[agent_name]["completion_tokens"] += completion_tokens
            self.agent_stats[agent_name]["total_tokens"] += total
            self.agent_calls[agent_name] += 1
            
            # 更新模型统计
            self.model_stats[model_name]["prompt_tokens"] += prompt_tokens
            self.model_stats[model_name]["completion_tokens"] += completion_tokens
            self.model_stats[model_name]["total_tokens"] += total
            self.model_calls[model_name] += 1
            
            logger.info(f"✅ Token统计更新成功: Agent={agent_name}, Model={model_name}, "
                       f"Prompt={prompt_tokens}, Completion={completion_tokens}, Total={total}")
            logger.info(f"   当前累计: total_tokens={self.total_tokens}, call_count={self.call_count}")
    
    def get_stats(self) -> Dict[str, Any]:
        """获取完整的统计数据
        
        Returns:
            包含所有统计信息的字典
        """
        with self._lock:
            # 添加调试信息
            logger.info(f"获取token统计: total_tokens={self.total_tokens}, "
                       f"prompt_tokens={self.total_prompt_tokens}, "
                       f"completion_tokens={self.total_completion_tokens}, "
                       f"call_count={self.call_count}")
            logger.info(f"Agent统计详情: {dict(self.agent_stats)}")
            logger.info(f"Model统计详情: {dict(self.model_stats)}")
            
            stats = {
                "total_tokens": self.total_tokens,
                "prompt_tokens": self.total_prompt_tokens,
                "completion_tokens": self.total_completion_tokens,
                "total_calls": self.call_count,
                "agent_breakdown": dict(self.agent_stats),
                "model_breakdown": dict(self.model_stats),
                "call_breakdown": {
                    "by_agent": dict(self.agent_calls),
                    "by_model": dict(self.model_calls)
                }
            }
            
            return stats


class TokenUsageCallback(BaseCallbackHandler):
    """LangChain token使用量回调处理器"""
    
    def __init__(self, tracker: TokenUsageTracker, agent_name: str = "unknown", model_name: str = "unknown"):
        """初始化回调处理器
        
        Args:
            tracker: 全局token追踪器实例
            agent_name: 当前Agent名称
            model_name: 当前模型名称
        """
        super().__init__()
        self.tracker = tracker
        self.agent_name = agent_name
        self.model_name = model_name
    
    def on_llm_end(self, response: LLMResult, **kwargs: Any) -> None:
        """LLM调用结束时的回调"""
        try:
            logger.info(f"Token回调触发: {self.agent_name} -> {self.model_name}")
            logger.info(f"Response结构: llm_output={response.llm_output}")
            logger.info(f"Response完整信息: {response}")
            
            # 多种方式尝试获取token使用信息
            prompt_tokens = 0
            completion_tokens = 0
            
            # 方式1: 检查llm_output中的token_usage
            if response.llm_output and "token_usage" in response.llm_output:
                usage = response.llm_output["token_usage"]
                if usage is not None:
                    prompt_tokens = usage.get("prompt_tokens", 0)
                    completion_tokens = usage.get("completion_tokens", 0)
                    logger.info(f"从llm_output获取token: {prompt_tokens}+{completion_tokens}")
                else:
                    logger.info("llm_output中的token_usage为None")
                
            # 方式2: 检查generations中的token信息
            elif hasattr(response, 'generations') and response.generations:
                for generation_list in response.generations:
                    for generation in generation_list:
                        if hasattr(generation.message, 'usage_metadata') and generation.message.usage_metadata:
                            usage = generation.message.usage_metadata
                            if usage is not None:
                                prompt_tokens += usage.get("input_tokens", 0)
                                completion_tokens += usage.get("output_tokens", 0)
                                logger.info(f"从usage_metadata获取token: {prompt_tokens}+{completion_tokens}")
                                break
                        
                        if hasattr(generation, 'generation_info') and generation.generation_info:
                            if 'token_usage' in generation.generation_info:
                                usage = generation.generation_info['token_usage']
                                if usage is not None:
                                    prompt_tokens += usage.get("prompt_tokens", 0)
                                    completion_tokens += usage.get("completion_tokens", 0)
                                    logger.info(f"从generation_info获取token: {prompt_tokens}+{completion_tokens}")
                                    break
                    
            # 如果获取到了token信息，记录到追踪器
            if prompt_tokens > 0 or completion_tokens > 0:
                self.tracker.add_usage(
                    prompt_tokens=prompt_tokens,
                    completion_tokens=completion_tokens,
                    agent_name=self.agent_name,
                    model_name=self.model_name
                )
                
                logger.info(f"Token统计成功: {self.agent_name} -> {self.model_name}, "
                           f"Token使用: {prompt_tokens}+{completion_tokens}={prompt_tokens+completion_tokens}")
            else:
                logger.warning(f"未能获取到token信息: {self.agent_name} -> {self.model_name}")
                
        except Exception as e:
            logger.error(f"Token使用统计回调处理失败: {e}")
            import traceback
            logger.error(f"错误详情: {traceback.format_exc()}")


# 全局token追踪器实例
global_token_tracker = TokenUsageTracker()


def create_token_callback(agent_name: str, model_name: str) -> TokenUsageCallback:
    """创建token使用量回调处理器
    
    Args:
        agent_name: Agent名称
        model_name: 模型名称
        
    Returns:
        TokenUsageCallback实例
    """
    # 尝试获取全局的primary tracker实例
    import sys
    primary_tracker = getattr(sys.modules.get('rag_chat.multi_agent_service'), 'global_token_tracker', None)
    
    if primary_tracker and not hasattr(primary_tracker, 'get_stats'):
        # 如果是DummyTracker，使用本地实例
        primary_tracker = None
        
    tracker = primary_tracker if primary_tracker else global_token_tracker
    logger.info(f"Token回调使用tracker: {type(tracker).__name__} (来源: {'multi_agent_service' if primary_tracker else 'local'})")
    
    return TokenUsageCallback(tracker, agent_name, model_name)


def get_session_token_stats() -> Dict[str, Any]:
    """获取当前会话的token统计
    
    Returns:
        当前会话的完整token统计数据
    """
    return global_token_tracker.get_stats()


def reset_session_token_stats():
    """重置当前会话的token统计"""
    global_token_tracker.reset_stats()
    logger.info("会话token统计已重置")


def format_token_stats(stats: Dict[str, Any]) -> str:
    """格式化token统计信息为易读文本
    
    Args:
        stats: token统计数据
        
    Returns:
        格式化的统计文本
    """
    if not stats or stats["total_tokens"] == 0:
        return "本次对话无token消耗"
    
    lines = []
    lines.append(f"📊 **Token消耗统计**")
    lines.append(f"🔸 总消耗: {stats['total_tokens']:,} tokens")
    lines.append(f"🔸 输入: {stats['prompt_tokens']:,} tokens")
    lines.append(f"🔸 输出: {stats['completion_tokens']:,} tokens")
    lines.append(f"🔸 调用次数: {stats['total_calls']} 次")
    
    # Agent分类统计
    if stats["agent_breakdown"]:
        lines.append(f"\n📋 **按Agent分类**:")
        for agent, data in stats["agent_breakdown"].items():
            if data["total_tokens"] > 0:
                lines.append(f"• {agent}: {data['total_tokens']:,} tokens "
                           f"({data['prompt_tokens']:,}+{data['completion_tokens']:,})")
    
    # 模型分类统计  
    if stats["model_breakdown"]:
        lines.append(f"\n🤖 **按模型分类**:")
        for model, data in stats["model_breakdown"].items():
            if data["total_tokens"] > 0:
                lines.append(f"• {model}: {data['total_tokens']:,} tokens "
                           f"({data['prompt_tokens']:,}+{data['completion_tokens']:,})")
    
    return "\n".join(lines)