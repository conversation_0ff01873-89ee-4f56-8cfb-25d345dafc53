import json
from html import unescape
from wsgiref.validate import validator

from fastapi import FastAPI, HTTPException, APIRouter,Request
from pydantic import BaseModel
from typing import Optional, Dict, Any, List, Tuple, TypeVar, Type
from dotenv import load_dotenv
import os
from logger import logger
from dm_util import DMDatabase

# FastAPI 应用
router = APIRouter()

# 数据库配置
load_dotenv()
dm_config = {
    'host': os.getenv('DM_HOST'),
    'port': int(os.getenv('DM_PORT')),
    'user': os.getenv('DM_USER'),
    'password': os.getenv('DM_PASSWORD'),
    'database': os.getenv('DM_DB'),
    'auto_commit': False
}

# 统一响应模型
class ResponseModel(BaseModel):
    code: int = 0
    msg: str = ""
    data: Optional[Dict] = None

# 请求模型
class BaseSQLRequest(BaseModel):
    sql: str
    params: Optional[List[Any]] = None

class QueryRequest(BaseSQLRequest):
    pass

class UpdateRequest(BaseSQLRequest):
    pass

# 类型变量用于泛型
T = TypeVar('T', bound=BaseSQLRequest)


# JSON 处理工具类
class JSONUnescaper:
    @staticmethod
    def unescape_json(raw_json: str) -> dict:
        """反转义整个 JSON 字符串并解析为字典"""
        # 1. 反转义整个 JSON 字符串
        unescaped_str = unescape(raw_json)

        # 2. 解析为字典
        try:
            return json.loads(unescaped_str)
        except json.JSONDecodeError as e:
            raise ValueError(f"无效的 JSON 格式: {str(e)}")

    @staticmethod
    def parse_to_request(raw_json: str, request_class: Type[T]) -> T:
        """
        通用方法：处理 JSON 并转换为指定的请求对象

        Args:
            raw_json: 原始 JSON 字符串
            request_class: 要转换的请求类（QueryRequest 或 UpdateRequest）

        Returns:
            指定类型的请求对象
        """
        # 处理 JSON 转义
        data = JSONUnescaper.unescape_json(raw_json)

        # 验证必要字段
        if 'sql' not in data:
            raise ValueError("缺少必需的 'sql' 字段")

        # 创建请求对象
        try:
            return request_class(
                sql=data.get('sql'),
                params=data.get('params', [])
            )
        except Exception as e:
            raise ValueError(f"创建请求对象失败: {str(e)}")


# 查询接口
@router.post("/jiliang/query_endpoint", response_model=ResponseModel)
async def query_endpoint(request: Request) -> ResponseModel:
    try:
        # 1. 读取原始请求体
        raw_body = await request.body()
        raw_json = raw_body.decode('utf-8')

        # 2. 处理转义并转换为请求对象
        query_request = JSONUnescaper.parse_to_request(raw_json, QueryRequest)

        # 3. 执行数据库操作
        with DMDatabase(**dm_config) as db:
            # 将列表参数转换为元组
            params_tuple = tuple(query_request.params) if query_request.params else None

            logger.info(f"查询语句：{query_request.sql}")
            logger.info(f"查询参数：{params_tuple}")

            result = db.execute_query(query_request.sql, params_tuple)
            return ResponseModel(
                code=0,
                msg="查询成功",
                data={"results": result}
            )
    except Exception as e:
        return ResponseModel(
            code=1,
            msg=f"查询失败: {str(e)}",
            data={}
        )

# 更新接口
@router.post("/jiliang/update_endpoint", response_model=ResponseModel)
async def update_endpoint(request: Request) -> ResponseModel:
    try:
        # 1. 读取原始请求体
        raw_body = await request.body()
        raw_json = raw_body.decode('utf-8')

        # 2. 处理转义并转换为请求对象
        query_request = JSONUnescaper.parse_to_request(raw_json, QueryRequest)

        # 3. 执行数据库操作
        with DMDatabase(**dm_config) as db:
            # 将列表参数转换为元组
            params_tuple = tuple(query_request.params) if query_request.params else None

            logger.info(f"查询语句：{query_request.sql}")
            logger.info(f"查询参数：{params_tuple}")

            rowcount = db.execute_update(query_request.sql, params_tuple)
            db.commit()  # 显式提交事务
            return ResponseModel(
                code=0,
                msg="更新成功",
                data={"rows_affected": rowcount}
            )
    except Exception as e:
        # 尝试回滚事务
        try:
            if 'db' in locals():
                db.rollback()
        except:
            pass
        return ResponseModel(
            code=1,
            msg=f"更新失败: {str(e)}",
            data={}
        )

# 事务管理接口
@router.post("/jiliang/commit", response_model=ResponseModel)
async def commit_transaction() -> ResponseModel:
    try:
        with DMDatabase(**dm_config) as db:
            db.commit()
        return ResponseModel(
            code=0,
            msg="事务提交成功",
            data={}
        )
    except Exception as e:
        return ResponseModel(
            code=1,
            msg=f"事务提交失败: {str(e)}",
            data={}
        )

@router.post("/jiliang/rollback", response_model=ResponseModel)
async def rollback_transaction() -> ResponseModel:
    try:
        with DMDatabase(**dm_config) as db:
            db.rollback()
        return ResponseModel(
            code=0,
            msg="事务回滚成功",
            data={}
        )
    except Exception as e:
        return ResponseModel(
            code=1,
            msg=f"事务回滚失败: {str(e)}",
            data={}
        )

# if __name__ == "__main__":
#     import uvicorn
#     uvicorn.run(app, host="0.0.0.0", port=8000)
