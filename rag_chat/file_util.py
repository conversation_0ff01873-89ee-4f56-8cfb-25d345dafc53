import hashlib
import os
import ssl
import aiohttp
import json
from file_readerV2 import logger

ssl_context = ssl.create_default_context()
ssl_context.check_hostname = False
ssl_context.verify_mode = ssl.CERT_NONE

# 添加文件服务器地址
FILE_SERVER_URL = "http://*************:19111"


def md5(text: str) -> str:
  m = hashlib.md5()
  m.update(text.encode('utf-8'))
  return m.hexdigest()


async def upload_file(file_bytes: bytes, md5: str, file_name: str, bucketName: str = None,
                      headers: dict = None) -> int:
  """
  上传文件到文件存储服务
  """
  file_url = FILE_SERVER_URL
  
  if headers is None:
    headers = {}
  
  data = aiohttp.FormData()
  data.add_field('file',
                 file_bytes,
                 filename=file_name,
                 content_type='application/octet-stream')
  data.add_field('md5', md5)
  data.add_field('fileName', file_name)
  
  if bucketName:
    upload_url = f'{file_url}/file/storage/upload/{bucketName}'
  else:
    upload_url = f'{file_url}/file/storage/upload'
  
  logger.info(f'开始上传文件: {file_name} 到 {upload_url}, headers: {headers}')
  
  async with aiohttp.ClientSession(connector=aiohttp.TCPConnector(ssl=ssl_context)) as session:
    async with session.post(upload_url,
                            data=data,
                            headers=headers) as response:
      logger.info(f'文件上传状态码: {response.status}')
      
      response_text = await response.text()
      logger.info(f'上传响应原始内容: {response_text}')
      
      if response.status != 200:
        logger.error(f'上传文件失败: 状态码 {response.status}, 响应: {response_text}')
        raise Exception(f'上传文件失败: {response.status} {response_text}')
      
      try:
        result = await response.json()
        logger.info(f'上传响应JSON: {result}')
        
        # 更灵活的响应解析
        file_id = None
        if isinstance(result, dict):
          if 'data' in result and isinstance(result['data'], dict) and 'id' in result['data']:
            file_id = result['data']['id']
          elif 'data' in result and isinstance(result['data'], (int, str)):
            file_id = result['data']
          elif 'id' in result:
            file_id = result['id']
          elif 'fileId' in result:
            file_id = result['fileId']
          elif 'file_id' in result:
            file_id = result['file_id']
        
        if file_id is None:
          logger.error(f'文件上传响应中未找到文件ID: {result}')
          raise Exception(f'文件上传响应格式异常，未找到文件ID: {result}')
        
        logger.info(f'文件上传完成: {file_name}, 文件ID: {file_id}')
        
        return int(file_id) if isinstance(file_id, str) and file_id.isdigit() else file_id
      
      except json.JSONDecodeError as e:
        logger.error(f'解析上传响应JSON失败: {e}, 响应内容: {response_text}')
        # 检查是否是纯文本的文件ID
        try:
          file_id = int(response_text.strip())
          logger.info(f'解析为纯文本文件ID: {file_id}')
          return file_id
        except ValueError:
          raise Exception(f'响应不是有效的JSON也不是数字ID: {response_text}')
      except Exception as e:
        logger.error(f'处理上传响应时出错: {e}, 响应内容: {response_text}')
        raise Exception(f'处理上传响应失败: {e}')

async def download_file(file_id: int, headers: dict = None) -> str:
  """
  从文件存储服务下载文件

  Args:
      file_id: 文件id
      headers: 请求头（可选）

  Returns:
      str: 下载文件的本地路径
  """
  if headers is None:
    headers = {}
  
  logger.info(f'开始下载文件: {file_id}')
  
  async with aiohttp.ClientSession(connector=aiohttp.TCPConnector(ssl=ssl_context)) as session:
    async with session.get(f'{FILE_SERVER_URL}/file/storage/download/{file_id}', headers=headers) as response:
      logger.info(f'响应头中的文件名称: {response.headers.get("Content-Disposition")}')
      file_extension = "."
      parts = response.headers.get("Content-Disposition", "").split('=')
      if len(parts) > 1:
        file_name = parts[1]
        # 提取文件扩展名
        file_extension += file_name.split('.')[-1]
        logger.info(f"文件格式是: {file_extension}")
      else:
        raise Exception(f'下载文件失败: 未获取到文件格式')
      
      logger.info(f'文件下载状态码: {response.status}')
      # 判断状态码
      if response.status != 200:
        raise Exception(f'下载文件失败: {response.status} {await response.text()}')
      
      folder_path = 'temp'
      # 检查文件夹是否存在
      if not os.path.exists(folder_path):
        os.makedirs(folder_path)
      
      full_path = f'temp/{file_id}{file_extension}'
      with open(full_path, 'wb') as f:
        f.write(await response.read())
      
      logger.info(f'文件下载完成: {full_path}, 文件大小: {os.path.getsize(full_path)}')
      return full_path