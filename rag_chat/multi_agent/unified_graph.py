"""
统一的多智能体问答图系统

构建一个完整的LangGraph，集成意图分析、专门化Agent和监督者协调功能。
支持直接调用graph.astream进行流式问答。
"""

import re
from typing import Dict, Any, List, Optional, Literal, Annotated
from langgraph.graph import StateGraph, START, END
from langgraph.graph.message import add_messages, REMOVE_ALL_MESSAGES
from langgraph_supervisor import create_supervisor
from langgraph_swarm import create_swarm, create_handoff_tool
from langchain_core.messages import BaseMessage, HumanMessage, AIMessage, SystemMessage
from langchain_core.messages.modifier import RemoveMessage
from langchain_openai import ChatOpenAI
from langchain_deepseek import ChatDeepSeek
from pydantic import BaseModel, Field
from pydantic import SecretStr
import os
import asyncio
from .intent_analyzer import create_intent_analyzer
import sys
sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))
import typing_compat
from logger import logger
from jiliang_chat_prompt import (
    INTENT_ANALYZER_PROMPT,
    SUPERVISOR_AGENT_PROMPT,
    SIMPLE_CHAT_PROMPT
)
from langgraph.types import RetryPolicy
from .agents import create_simple_chat_agent, create_knowledge_agent, create_mcp_agent, create_multimodal_agent, create_custom_rag_agent
# 导入token统计功能
sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))
from token_usage_tracker import create_token_callback
from llm_pool import get_llm_from_pool

class UnifiedQAState(BaseModel):
    """统一问答状态"""
    
    # 核心消息流
    messages: Annotated[List[BaseMessage], add_messages] = Field(default_factory=list)
    
    # 当前问题
    current_question: str = Field(default="")
    
    # 用户选择信息
    mcp_ids: List[str] = Field(default_factory=list)
    extral_rag_ids: List[str] = Field(default_factory=list)
    file_uploads: list = Field(default_factory=list)
    
    # 意图分析结果
    processing_mode: Optional[str] = Field(default=None)
    primary_agent: Optional[str] = Field(default=None)
    secondary_agents: List[str] = Field(default_factory=list)
    confidence: float = Field(default=0.0)
    
    # 执行状态
    current_step: str = Field(default="intent_analysis")
    execution_status: str = Field(default="running")
    
    # Agent结果存储
    agent_results: Dict[str, Any] = Field(default_factory=dict)
    
    # 元数据
    model_desc: str = Field(default="DeepSeek")
    thread_id: str = Field(default="")
    extra_headers: Dict[str, Any] = Field(default_factory=dict)
    
    # 全局上下文总结状态（用于LangMem全局上下文管理）
    global_running_summary: Optional[Any] = Field(default=None)  # RunningSummary类型，但防止导入错误使用Any


class UnifiedMultiAgentGraph:
    """统一的多智能体问答图
    
    一个完整的LangGraph，包含意图分析、Agent选择、监督者协调等所有功能。
    """
    
    def __init__(self, 
                 checkpointer: Any = None,
                 mcp_router: Any = None,
                 mcp_simple_router: Any = None,
                 enable_global_context_summarization: bool = True):
        """初始化统一图
        
        Args:
            checkpointer: 检查点保存器
            mcp_router: MCP RAG路由器  
            mcp_simple_router: MCP简单路由器
            enable_global_context_summarization: 是否启用全局上下文总结
        """
        self.checkpointer = checkpointer
        self.mcp_router = mcp_router
        self.mcp_simple_router = mcp_simple_router
        self.enable_global_context_summarization = enable_global_context_summarization
        self.graph = None
        
        # 初始化全局上下文总结器
        self.global_context_summarizer = None
        if self.enable_global_context_summarization:
            try:
                # 使用更高的token阈值进行全局上下文管理
                from context_summarizer import ContextSummarizer, SummarizationConfig
                global_config = SummarizationConfig.from_env()
                # 全局上下文管理使用更高的阈值，避免与Agent内部压缩冲突
                global_config.max_tokens_before_summary = max(20000, global_config.max_tokens_before_summary * 2)
                global_config.max_tokens = max(25000, global_config.max_tokens * 2)
                self.global_context_summarizer = ContextSummarizer(global_config)
                logger.info(f"统一图全局上下文总结功能已启用，阈值: {global_config.max_tokens_before_summary}")
            except Exception as e:
                logger.warning(f"统一图全局上下文总结器初始化失败: {e}")
                self.enable_global_context_summarization = False
        
    def build_graph(self):
        """构建统一的多智能体图"""
        
        try:
            # 创建状态图
            builder = StateGraph(UnifiedQAState)
            
            # 添加消息清理节点作为入口 - 确保每次处理都从干净的上下文开始
            builder.add_node("message_cleaner", self._message_cleaner_node)
            
            # 设置消息清理节点为入口点
            builder.set_entry_point("message_cleaner")
            
            # 添加其他节点
            if self.enable_global_context_summarization and self.global_context_summarizer:
                # 启用全局上下文总结时，添加全局总结节点
                builder.add_node("global_context_summarization", self._global_context_summarization_node)
                builder.add_node("intent_analyzer", self._intent_analyzer_node, retry_policy=RetryPolicy(max_attempts=5))
                
                # 消息清理后进入全局上下文总结
                builder.add_edge("message_cleaner", "global_context_summarization")
                
                # 全局上下文总结后进入意图分析
                builder.add_edge("global_context_summarization", "intent_analyzer")
            else:
                # 未启用全局上下文总结时，消息清理后直接进入意图分析
                builder.add_node("intent_analyzer", self._intent_analyzer_node, retry_policy=RetryPolicy(max_attempts=5))
                
                # 消息清理后进入意图分析
                builder.add_edge("message_cleaner", "intent_analyzer")
            
            builder.add_node("simple_chat", self._simple_chat_node, retry_policy=RetryPolicy(max_attempts=5))
            builder.add_node("knowledge_agent", self._knowledge_agent_node, retry_policy=RetryPolicy(max_attempts=5))
            builder.add_node("mcp_agent", self._mcp_agent_node, retry_policy=RetryPolicy(max_attempts=5))
            builder.add_node("multimodal_agent", self._multimodal_agent_node, retry_policy=RetryPolicy(max_attempts=5))
            builder.add_node("custom_rag_agent", self._custom_rag_agent_node, retry_policy=RetryPolicy(max_attempts=5))
            builder.add_node("supervisor", self._supervisor_node, retry_policy=RetryPolicy(max_attempts=5))
            builder.add_node("parallel_coordinator", self._parallel_coordinator_node, retry_policy=RetryPolicy(max_attempts=5))
            
            # 意图分析后的路由
            builder.add_conditional_edges(
                "intent_analyzer",
                self._route_after_intent_analysis,
                {
                    "simple_chat": "simple_chat",
                    "knowledge_agent": "knowledge_agent",
                    "mcp_agent": "mcp_agent", 
                    "multimodal_agent": "multimodal_agent",
                    "custom_rag_agent": "custom_rag_agent",
                    "supervisor": "supervisor",
                    "parallel_coordinator": "parallel_coordinator"
                }
            )
            
            # 所有Agent节点直接结束（消息清理在入口处已完成）
            builder.add_edge("simple_chat", END)
            builder.add_edge("knowledge_agent", END)
            builder.add_edge("mcp_agent", END)
            builder.add_edge("multimodal_agent", END)
            builder.add_edge("custom_rag_agent", END)
            builder.add_edge("supervisor", END)
            builder.add_edge("parallel_coordinator", END)
            
            # 编译图
            self.graph = builder.compile(checkpointer=self.checkpointer)
            
            logger.info("统一多智能体图构建完成")
            return self.graph
            
        except Exception as e:
            logger.error(f"构建统一图失败: {e}")
            raise
    
    async def _global_context_summarization_node(self, state: UnifiedQAState) -> dict:
        """全局上下文总结节点：在系统入口处进行全局上下文管理"""
        try:
            if not self.enable_global_context_summarization or not self.global_context_summarizer:
                # 未启用或初始化失败，直接返回空更新
                return {}
            
            messages = state.messages
            if not messages:
                return {}
            
            # 从状态中获取全局运行总结
            global_running_summary = state.global_running_summary
            
            # 检查是否需要进行全局总结
            if not self.global_context_summarizer.should_summarize(messages):
                return {}
            
            logger.info(f"统一图开始全局上下文总结：{len(messages)}条消息")
            
            # 执行全局总结
            processed_messages, updated_summary = self.global_context_summarizer.summarize_messages_with_state(
                messages, global_running_summary
            )
            
            # 检查是否实际进行了压缩
            if len(processed_messages) >= len(messages):
                # 没有进行实际的压缩，返回空更新
                return {}
            
            logger.info(f"统一图全局上下文总结完成：{len(messages)} -> {len(processed_messages)}条消息")
            
            # 构建状态更新：使用RemoveMessage移除所有现有消息，然后添加处理后的消息
            state_update = {
                "messages": [RemoveMessage(id=REMOVE_ALL_MESSAGES)] + processed_messages
            }
            
            # 如果有更新的全局运行总结，也保存到状态中
            if updated_summary != global_running_summary:
                state_update["global_running_summary"] = updated_summary
                logger.debug("统一图更新了全局运行总结状态")
            
            return state_update
            
        except Exception as e:
            logger.error(f"统一图全局上下文总结失败: {e}")
            # 发生错误时返回空更新，不影响正常流程
            return {}
    
    async def _intent_analyzer_node(self, state: UnifiedQAState) -> UnifiedQAState:
        """意图分析节点（复用原有逻辑）"""
        
        try:
            logger.info("执行文本模式意图分析")
            
            # 创建意图分析LLM
            intent_model = os.getenv("INTENT_MODEL", "qwen2---5-72b-goxmbugy")
            intent_llm = await create_intent_analyzer(
                self._create_llm(temperature=0.1, extra_headers=state.extra_headers, model_name=intent_model, agent_name="intent_analyzer")
            )
            
            # 构建用户上下文
            user_context = self._build_user_context(state)
            
            result = await intent_llm.analyze_intent(
                state.current_question, 
                state.mcp_ids, 
                state.extral_rag_ids, 
                state.file_uploads,
                user_context
            )
            
            # 解析结果
            state.processing_mode = result.processing_mode
            state.primary_agent = result.primary_agent
            state.secondary_agents = result.secondary_agents
            state.confidence = result.confidence
            
            logger.info(f"文本模式意图分析完成: {state.processing_mode}/{state.primary_agent}")
            
            return state
            
        except Exception as e:
            logger.error(f"文本模式意图分析失败: {e}")
            # 使用简单fallback
            state.processing_mode = "direct_mode"
            state.primary_agent = "simple_chat"
            state.confidence = 0.5
            return state
    
    def _route_after_intent_analysis(self, state: UnifiedQAState) -> str:
        """意图分析后的路由决策"""
        
        # 确保 primary_agent 不为 None
        if state.primary_agent is None:
            state.primary_agent = "simple_chat"
        
        # 根据处理模式和主要Agent决定下一个节点
        if state.processing_mode == "direct_mode":
            return state.primary_agent
        elif state.processing_mode == "supervisor_mode":
            return "supervisor"
        elif state.processing_mode == "swarm_mode":
            return "parallel_coordinator"
        else:
            return state.primary_agent
    
    async def _simple_chat_node(self, state: UnifiedQAState) -> UnifiedQAState:
        """简单问答节点"""
        
        try:
            logger.info("执行简单问答Agent")
            
            # 创建简单问答Agent
            chat_model = os.getenv("CHAT_MODEL", "qwen2---5-72b-goxmbugy")
            llm = self._create_llm(temperature=0.7, extra_headers=state.extra_headers, model_name=chat_model, agent_name="simple_chat")
            agent = await create_simple_chat_agent(
                llm=llm,
                model_desc=state.model_desc,
                checkpointer=self.checkpointer,
                enable_context_summarization=True
            )
            
            # 执行Agent
            config = self._build_agent_config(state, "simple_chat")
            result = await agent.ainvoke(
                {"messages": state.messages},
                config=config
            )
            
            # 更新状态
            new_messages = result.get("messages", [])
            state.messages.extend(new_messages)
            state.agent_results["simple_chat"] = result
            state.execution_status = "completed"
            
            logger.info("简单问答Agent执行完成")
            return state
            
        except Exception as e:
            logger.error(f"简单问答Agent执行失败: {e}")
            error_msg = AIMessage(content=f"抱歉，处理您的问题时遇到了问题：{str(e)}")
            state.messages.append(error_msg)
            state.execution_status = "failed"
            return state
    
    async def _knowledge_agent_node(self, state: UnifiedQAState) -> UnifiedQAState:
        """知识专家Agent节点"""
        
        try:
            logger.info("执行知识专家Agent")
            
            # 创建知识Agent
            knowledge_model = os.getenv("KNOWLEDGE_MODEL", "qwen2---5-72b-goxmbugy")
            llm = self._create_llm(extra_headers=state.extra_headers, model_name=knowledge_model, agent_name="knowledge_agent")
            agent = await create_knowledge_agent(
                llm=llm,
                model_desc=state.model_desc,
                checkpointer=self.checkpointer,
                enable_context_summarization=True
            )
            
            # 执行Agent
            config = self._build_agent_config(state, "knowledge")
            result = await agent.ainvoke(
                {"messages": state.messages},
                config=config
            )
            
            # 更新状态
            new_messages = result.get("messages", [])
            state.messages.extend(new_messages)
            state.agent_results["knowledge_agent"] = result
            state.execution_status = "completed"
            
            logger.info("知识专家Agent执行完成")
            return state
            
        except Exception as e:
            logger.error(f"知识专家Agent执行失败: {e}")
            error_msg = AIMessage(content=f"知识查询时遇到问题：{str(e)}")
            state.messages.append(error_msg)
            state.execution_status = "failed"
            return state
    
    async def _mcp_agent_node(self, state: UnifiedQAState) -> UnifiedQAState:
        """MCP工具专家Agent节点"""
        
        try:
            logger.info("执行MCP工具专家Agent")
            
            # 创建MCP Agent
            tool_model = os.getenv("TOOL_MODEL", "qwen2---5-72b-goxmbugy")
            llm = self._create_llm(extra_headers=state.extra_headers, model_name=tool_model, agent_name="mcp_agent")
            agent = await create_mcp_agent(
                llm=llm,
                question=state.current_question,
                mcp_ids=state.mcp_ids,
                model_desc=state.model_desc,
                checkpointer=self.checkpointer,
                mcp_router=self.mcp_router,
                mcp_simple_router=self.mcp_simple_router,
                enable_context_summarization=True,
                api_key=getattr(state.extra_headers, 'api_key', None),
                username=getattr(state.extra_headers, 'username', None)
            )
            
            # 执行Agent
            config = self._build_agent_config(state, "mcp")
            result = await agent.ainvoke(
                {"messages": state.messages},
                config=config
            )
            
            # 更新状态
            new_messages = result.get("messages", [])
            state.messages.extend(new_messages)
            state.agent_results["mcp_agent"] = result
            state.execution_status = "completed"
            
            logger.info("MCP工具专家Agent执行完成")
            return state
            
        except Exception as e:
            logger.error(f"MCP工具专家Agent执行失败: {e}")
            error_msg = AIMessage(content=f"工具调用时遇到问题：{str(e)}")
            state.messages.append(error_msg)
            state.execution_status = "failed"
            return state
    
    async def _multimodal_agent_node(self, state: UnifiedQAState) -> UnifiedQAState:
        """多模态专家Agent节点"""
        
        try:
            logger.info("执行多模态专家Agent")
            
            # 创建多模态Agent
            multimodal_model = os.getenv("MULTIMODAL_MODEL", "qwen2-5-vl-72b")
            llm = self._create_llm(extra_headers=state.extra_headers, model_name=multimodal_model, agent_name="multimodal_agent")
            agent = await create_multimodal_agent(
                llm=llm,
                model_desc=state.model_desc,
                checkpointer=self.checkpointer,
                enable_context_summarization=True
            )
            
            # 执行Agent
            config = self._build_agent_config(state, "multimodal")
            result = await agent.ainvoke(
                {"messages": state.messages},
                config=config
            )
            
            # 更新状态
            new_messages = result.get("messages", [])
            state.messages.extend(new_messages)
            state.agent_results["multimodal_agent"] = result
            state.execution_status = "completed"
            
            logger.info("多模态专家Agent执行完成")
            return state
            
        except Exception as e:
            logger.error(f"多模态专家Agent执行失败: {e}")
            error_msg = AIMessage(content=f"文件处理时遇到问题：{str(e)}")
            state.messages.append(error_msg)
            state.execution_status = "failed"
            return state
    
    async def _custom_rag_agent_node(self, state: UnifiedQAState) -> UnifiedQAState:
        """自定义RAG知识库专家Agent节点"""
        
        try:
            logger.info("执行自定义RAG知识库专家Agent")
            
            # 创建自定义RAG Agent
            knowledge_model = os.getenv("KNOWLEDGE_MODEL", "qwen2---5-72b-goxmbugy")
            llm = self._create_llm(extra_headers=state.extra_headers, model_name=knowledge_model, agent_name="custom_rag_agent")
            agent = await create_custom_rag_agent(
                llm=llm,
                custom_rag_ids=state.extral_rag_ids,
                model_desc=state.model_desc,
                checkpointer=self.checkpointer,
                enable_context_summarization=True
            )
            
            # 执行Agent
            config = self._build_agent_config(state, "custom_rag")
            result = await agent.ainvoke(
                {"messages": state.messages},
                config=config
            )
            
            # 更新状态
            new_messages = result.get("messages", [])
            state.messages.extend(new_messages)
            state.agent_results["custom_rag_agent"] = result
            state.execution_status = "completed"
            
            logger.info("自定义RAG知识库专家Agent执行完成")
            return state
            
        except Exception as e:
            logger.error(f"自定义RAG知识库专家Agent执行失败: {e}")
            error_msg = AIMessage(content=f"自定义知识库查询时遇到问题：{str(e)}")
            state.messages.append(error_msg)
            state.execution_status = "failed"
            return state
    
    async def _supervisor_node(self, state: UnifiedQAState) -> UnifiedQAState:
        """监督者协调节点"""
        
        try:
            logger.info("执行监督者协调模式")
            
            # 创建需要协调的Agent
            agents = []
            
            # 主要Agent
            primary_agent = await self._create_single_agent(state.primary_agent, state)
            agents.append(primary_agent)
            
            # 次要Agent
            for agent_type in state.secondary_agents:
                if agent_type != state.primary_agent:
                    agent = await self._create_single_agent(agent_type, state)
                    agents.append(agent)
            
            # 创建监督者
            supervisor_model = os.getenv("SUPERVISOR_MODEL", "qwq32b")
            llm = self._create_llm(extra_headers=state.extra_headers, model_name=supervisor_model, is_reasoning=True, agent_name="supervisor")
            supervisor_prompt = SUPERVISOR_AGENT_PROMPT.format(model_desc=state.model_desc)
            
            full_agent_descriptions = {
            "knowledge_agent": """### knowledge_agent (知识专家)
- **专长**: 三峡集团相关知识查询和业务问答
- **何时使用**: 查询涉及三峡、水电、能源、工程等专业领域
- **工具能力**: 权威知识库检索、专业内容分析""",
            "mcp_agent": """### mcp_agent (工具专家)  
- **专长**: 外部工具调用和复杂计算分析
- **何时使用**: 需要实时数据、API调用、计算处理
- **工具能力**: 动态MCP工具集、外部服务集成""",
            "multimodal_agent": """### multimodal_agent (多模态专家)
- **专长**: 文件和图像处理分析
- **何时使用**: 文档分析、图片识别、多媒体内容处理
- **工具能力**: OCR识别、文档解析、图像分析""",
            "simple_chat": """### simple_chat (通用助手)
- **专长**: 一般性对话和简单问答
- **何时使用**: 不需要专业工具的日常对话
- **工具能力**: 基础知识问答、对话交流""",
            "custom_rag_agent": """### custom_rag_agent (自定义知识库专家)
- **专长**: 处理用户指定的自定义知识库查询和回答
- **何时使用**: 用户提供了额外知识库ID
- **工具能力**: 用户自定义知识库检索
"""
        }
        
            # 只包含当前可用Agent的描述
            available_agent_descriptions = []
            for agent in agents:
                name = agent.name
                if name in full_agent_descriptions:
                    available_agent_descriptions.append(full_agent_descriptions[name])
                else:
                    available_agent_descriptions.append(f"### {name}\n- **专长**: 专门化Agent\n- **何时使用**: 处理{name}相关任务")

            supervisor_prompt = f"""
{supervisor_prompt}

## 当前可用的专业Agent
{chr(10).join(available_agent_descriptions)}
"""

            # 使用监督者模式
            supervisor_workflow = create_supervisor(
                agents=agents,
                model=llm,
                prompt=supervisor_prompt,
                output_mode="last_message",
                include_agent_name="inline"
            )
            
            # 编译并执行
            supervisor_graph = supervisor_workflow.compile(checkpointer=self.checkpointer)

            # save_graph_image(supervisor_graph, "unified_multi_agent_graph.mmd")
            
            config = self._build_agent_config(state, "supervisor")
            result = await supervisor_graph.ainvoke(
                {"messages": state.messages},
                config=config
            )
            
            # 更新状态
            if result and "messages" in result:
                new_messages = result.get("messages", [])
                # 只添加新的消息，避免重复
                existing_count = len(state.messages)
                if len(new_messages) > existing_count:
                    state.messages.extend(new_messages[existing_count:])
            
            state.agent_results["supervisor"] = result
            state.execution_status = "completed"
            
            logger.info("监督者协调完成")
            return state
            
        except Exception as e:
            logger.error(f"监督者协调失败: {e}")
            error_msg = AIMessage(content=f"任务协调时遇到问题：{str(e)}")
            state.messages.append(error_msg)
            state.execution_status = "failed"
            return state
    
    async def _parallel_coordinator_node(self, state: UnifiedQAState) -> UnifiedQAState:
        """并行协调节点"""
        
        try:
            logger.info("执行LangGraph Swarm智能并行协调模式")
            
            # 准备需要协调的Agent列表
            agents_to_run = [state.primary_agent] + state.secondary_agents
            swarm_agents = []
            
            # 创建Agent
            for agent_type in agents_to_run:
                # 创建handoff工具列表，允许Agent相互协调
                handoff_tools = []
                for other_agent in agents_to_run:
                    if other_agent != agent_type:
                        handoff_tool = create_handoff_tool(
                            agent_name=other_agent,
                            description=f"将任务转交给{self._get_agent_description(other_agent)}处理相关问题"
                        )
                        handoff_tools.append(handoff_tool)
                
                # 创建增强的Agent（包含handoff工具）
                enhanced_agent = await self._create_single_agent(
                    agent_type, state, handoff_tools
                )
                swarm_agents.append(enhanced_agent)
            
            # 创建Swarm工作流
            swarm_workflow = create_swarm(
                agents=swarm_agents,
                default_active_agent=state.primary_agent
            )
            
            # 编译Swarm图，支持checkpointer
            swarm_graph = swarm_workflow.compile(checkpointer=self.checkpointer)
            
            # 执行Swarm协调
            config = self._build_agent_config(state, "swarm")
            result = await swarm_graph.ainvoke(
                {"messages": state.messages},
                config=config
            )
            
            # 更新状态
            new_messages = result.get("messages", [])
            state.messages.extend(new_messages)
            state.agent_results["swarm_coordinator"] = result
            state.execution_status = "completed"
            
            logger.info(f"LangGraph Swarm智能协调完成，协调了{len(swarm_agents)}个Agent")
            return state
            
        except Exception as e:
            logger.error(f"LangGraph Swarm智能协调失败: {e}")
            error_msg = AIMessage(content=f"Swarm智能协调时遇到问题：{str(e)}")
            state.messages.append(error_msg)
            state.execution_status = "failed"
            return state
    
    def _is_real_human_message(self, msg) -> bool:
        """判断是否为真正的用户消息（排除工具执行结果以HumanMessage形式输出的情况）"""
        if not isinstance(msg, HumanMessage):
            return False
        
        # 检查环境变量，如果启用了ToolMessage，则所有HumanMessage都是真正的用户消息
        enable_toolmessage = os.getenv("ENABLE_TOOLMESSAGE", "true").lower() == "true"
        if enable_toolmessage and isinstance(msg, HumanMessage):
            return True
        
        if msg.additional_kwargs.get("is_tool_result", False):
            return False
        
        return True
    
    async def _message_cleaner_node(self, state: UnifiedQAState) -> UnifiedQAState:
        """消息清理节点：只保留每轮对话的真实用户输入和最后一条AI输出消息"""
        try:
            logger.info("执行消息清理，只保留真实用户输入和最后一条AI输出")
            
            messages = state.messages
            if len(messages) < 3:
                logger.info("消息太少，无需清理")
                return {}
            
            # 简单清理逻辑：识别对话轮次，每轮只保留真实用户输入+最后AI输出
            cleaned_messages = []
            current_turn_messages = []
            
            for msg in messages:
                if self._is_real_human_message(msg):
                    # 遇到真实用户消息，处理上一轮的消息
                    if current_turn_messages:
                        cleaned_messages.extend(self._extract_turn_key_messages(current_turn_messages))
                    
                    # 开始新的一轮
                    current_turn_messages = [msg]
                else:
                    # 收集当前轮的其他消息（包括AI消息和伪装的HumanMessage）
                    current_turn_messages.append(msg)
            
            # 处理最后一轮
            if current_turn_messages:
                cleaned_messages.extend(self._extract_turn_key_messages(current_turn_messages))
            
            if len(cleaned_messages) == len(messages):
                logger.info("没有找到需要清理的中间消息")
                return {}
            
            # 使用LangGraph的RemoveMessage机制进行消息更新            
            state_update = {
                "messages": [RemoveMessage(id=REMOVE_ALL_MESSAGES)] + cleaned_messages
            }
            
            filtered_count = len(messages) - len(cleaned_messages)
            logger.info(f"消息清理完成：原始 {len(messages)} 条消息 -> 清理后 {len(cleaned_messages)} 条消息")
            logger.info(f"过滤了 {filtered_count} 条中间执行消息")
            
            return state_update
            
        except Exception as e:
            logger.error(f"消息清理失败: {e}")
            # 清理失败时返回空字典，不影响正常流程
            return {}
    
    def _extract_turn_key_messages(self, turn_messages):
        """从一轮对话中提取关键消息：真实用户输入 + 最后一条AI输出"""
        if not turn_messages:
            return []
        
        key_messages = []
        
        # 第一条消息应该是真实用户输入
        if turn_messages and self._is_real_human_message(turn_messages[0]):
            key_messages.append(turn_messages[0])
        
        # 找到最后一条AI消息（忽略所有伪装的HumanMessage工具结果等）
        last_ai_msg = None
        for msg in reversed(turn_messages):
            if isinstance(msg, AIMessage):
                msg.content = self._extract_clean_content(msg)
                last_ai_msg = msg
                break
        
        if last_ai_msg:
            key_messages.append(last_ai_msg)
        
        return key_messages
    
    def _analyze_conversation_structure(self, messages):
        """分析对话结构，识别每轮对话的边界和关键消息"""
        turns = []
        current_turn = {"user_msg": None, "ai_messages": [], "tool_results": [], "start_idx": 0}
        
        for i, msg in enumerate(messages):
            if self._is_real_human_message(msg):
                # 发现新的用户消息，开始新的对话轮次
                if current_turn["user_msg"] is not None:
                    # 结束当前轮次
                    current_turn["end_idx"] = i - 1
                    turns.append(current_turn)
                    
                # 开始新轮次
                current_turn = {
                    "user_msg": (i, msg),
                    "ai_messages": [],
                    "tool_results": [],
                    "start_idx": i
                }
                
            elif isinstance(msg, AIMessage):
                # AI消息：需要分析是否为最终回答
                message_type = self._classify_ai_message(msg)
                current_turn["ai_messages"].append((i, msg, message_type))
                
            elif isinstance(msg, HumanMessage):
                # 工具结果或状态报告消息
                if self._is_tool_result_or_status(msg):
                    current_turn["tool_results"].append((i, msg))
        
        # 处理最后一轮
        if current_turn["user_msg"] is not None:
            current_turn["end_idx"] = len(messages) - 1
            turns.append(current_turn)
            
        return turns
    
    def _classify_ai_message(self, msg: AIMessage):
        """分类AI消息类型"""
        # 使用通用的内容提取逻辑
        content = self._extract_content_text(msg.content)
        
        # 1. Supervisor决策消息
        if hasattr(msg, 'name') and msg.name == 'supervisor':
            if '[TASK_COMPLETE]' in content:
                return 'supervisor_final'
            return 'supervisor_decision'
        
        # 2. Swarm内部转交消息
        if any(pattern in content for pattern in [
            '【', '的处理结果】', '【转交给当前Agent的任务】',
            '请基于以上', '的处理结果和转交任务，继续完成用户的需求'
        ]):
            return 'swarm_internal'
        
        # 3. ReAct中间思考和工具调用
        if any(pattern in content for pattern in [
            '[TOOL_CALL]', '<think>', '</think>', '[HANDOFF]'
        ]):
            # 检查是否同时包含最终回答内容
            if self._has_substantial_final_content(content):
                return 'mixed_with_final'
            return 'react_intermediate'
        
        # 4. 纯粹的最终回答
        return 'final_answer'
    
    def _has_substantial_final_content(self, content):
        """检查内容是否包含实质性的最终回答"""
        # 使用通用的内容提取逻辑
        content_text = self._extract_content_text(content)
            
        # 移除所有工具调用和思考标记
        clean_content = content_text
        patterns_to_remove = [
            r'\[TOOL_CALL\].*?\[/TOOL_CALL\]',
            r'<think>.*?</think>',
            r'<think>.*',
            r'.*</think>',
            r'\[HANDOFF\].*?\[/HANDOFF\]',
            r'## name\n\w+\n\n## content\n'
        ]
        
        for pattern in patterns_to_remove:
            clean_content = re.sub(pattern, '', clean_content, flags=re.DOTALL)
        
        # 检查剩余内容是否足够实质性（超过50个字符且包含有意义的句子）
        clean_content = clean_content.strip()
        return len(clean_content) > 50 and ('。' in clean_content or '！' in clean_content or '？' in clean_content or len(clean_content) > 100)
    
    def _is_tool_result_or_status(self, msg):
        """检查是否为工具结果或状态报告消息"""
        if isinstance(msg, HumanMessage):
            additional_kwargs = getattr(msg, 'additional_kwargs', {})
            return (additional_kwargs.get('is_tool_result') == True or 
                    additional_kwargs.get('message_type') == 'agent_status_report')
        return False
    
    def _filter_messages_intelligently(self, conversation_turns):
        """智能过滤消息，保留每轮对话的关键消息"""
        cleaned_messages = []
        
        for turn in conversation_turns:
            # 1. 始终保留用户消息
            if turn["user_msg"]:
                cleaned_messages.append(turn["user_msg"][1])
            
            # 2. 智能选择AI回答
            final_ai_msg = self._select_best_ai_response(turn["ai_messages"])
            if final_ai_msg:
                cleaned_messages.append(final_ai_msg)
        
        return cleaned_messages
    
    def _select_best_ai_response(self, ai_messages):
        """从一轮对话的多个AI消息中选择最佳的最终回答"""
        if not ai_messages:
            return None
        
        # 优先级排序：
        # 1. supervisor_final (包含[TASK_COMPLETE]的supervisor消息)
        # 2. final_answer (纯粹的最终回答)
        # 3. mixed_with_final (包含工具调用但也有最终回答的消息)
        # 4. supervisor_decision (supervisor决策消息)
        # 最后考虑：react_intermediate (ReAct中间消息)
        
        priority_order = {
            'supervisor_final': 1,
            'final_answer': 2, 
            'mixed_with_final': 3,
            'supervisor_decision': 4,
            'react_intermediate': 5,
            'swarm_internal': 6  # 最低优先级，通常应该被过滤
        }
        
        # 按优先级和位置排序（同优先级选择最后出现的）
        sorted_messages = sorted(ai_messages, 
                               key=lambda x: (priority_order.get(x[2], 99), x[0]))
        
        # 选择最高优先级的消息
        best_msg = sorted_messages[0]
        
        # 如果是swarm_internal类型，尝试查找更好的替代
        if best_msg[2] == 'swarm_internal' and len(sorted_messages) > 1:
            for _, msg, msg_type in sorted_messages[1:]:
                if msg_type != 'swarm_internal':
                    return msg
        
        return best_msg[1]
    
    def _extract_content_text(self, content):
        """通用的内容文本提取方法，处理字符串和列表格式"""
        if isinstance(content, list):
            text_parts = []
            for item in content:
                if isinstance(item, dict) and item.get('type') == 'text':
                    text_parts.append(item.get('text', ''))
                elif isinstance(item, str):
                    text_parts.append(item)
            return " ".join(text_parts)
        elif isinstance(content, str):
            return content
        else:
            return str(content)
    
    async def _create_single_agent(self, agent_type: str, state: UnifiedQAState, handoff_tools: Optional[List[Any]] = None) -> Any:
        """创建单个Agent实例"""
        
        # 根据agent类型选择模型
        if agent_type in ["knowledge_agent", "custom_rag_agent"]:
            model_name = os.getenv("KNOWLEDGE_MODEL", "qwen2---5-72b-goxmbugy")
        elif agent_type == "mcp_agent":
            model_name = os.getenv("TOOL_MODEL", "qwen2---5-72b-goxmbugy")
        elif agent_type == "multimodal_agent":
            model_name = os.getenv("MULTIMODAL_MODEL", "qwen2-5-vl-72b")
        else:
            model_name = os.getenv("CHAT_MODEL", "qwen2---5-72b-goxmbugy")

        llm = self._create_llm(extra_headers=state.extra_headers, model_name=model_name, is_reasoning=False, agent_name=agent_type)
        
        # 如果 handoff_tools 为 None，使用空列表
        if handoff_tools is None:
            handoff_tools = []
        
        if agent_type == "knowledge_agent":
            return await create_knowledge_agent(
                llm=llm,
                model_desc=state.model_desc,
                checkpointer=self.checkpointer,
                handoff_tools=handoff_tools,
                enable_context_summarization=True
            )
        
        elif agent_type == "simple_chat":
            return await create_simple_chat_agent(
                llm=llm,
                model_desc=state.model_desc,
                checkpointer=self.checkpointer,
                handoff_tools=handoff_tools,
                enable_context_summarization=True
            )
            
        elif agent_type == "mcp_agent":
            return await create_mcp_agent(
                llm=llm,
                question=state.current_question,
                mcp_ids=state.mcp_ids,
                model_desc=state.model_desc,
                checkpointer=self.checkpointer,
                mcp_router=self.mcp_router,
                mcp_simple_router=self.mcp_simple_router,
                handoff_tools=handoff_tools,
                enable_context_summarization=True,
                api_key=getattr(state.extra_headers, 'api_key', None),
                username=getattr(state.extra_headers, 'username', None)
            )
            
        elif agent_type == "multimodal_agent":
            return await create_multimodal_agent(
                llm=llm,
                model_desc=state.model_desc,
                checkpointer=self.checkpointer,
                handoff_tools=handoff_tools,
                enable_context_summarization=True
            )
        elif agent_type == "custom_rag_agent":
            return await create_custom_rag_agent(
                llm=llm,
                model_desc=state.model_desc,
                checkpointer=self.checkpointer,
                handoff_tools=handoff_tools,
                enable_context_summarization=True,
                custom_rag_ids=state.extral_rag_ids or []
            )
            
        else:
            raise ValueError(f"未知Agent类型: {agent_type}")
    
    def _create_llm(self, 
                    temperature: float = 0.6, 
                    extra_headers: Optional[Dict[str, Any]] = None,
                    model_name: str = "",
                    is_reasoning: bool = False,
                    agent_name: str = "unknown"):
        """使用连接池创建LLM实例，优化资源利用"""
        if extra_headers is None:
            extra_headers = {}
        
        # 确定最终的模型名称    
        final_model_name = model_name if model_name else str(os.getenv("OPENAI_MODEL"))
        
        # 创建token统计回调
        token_callback = create_token_callback(agent_name, final_model_name)
        logger.debug(f"为{agent_name}创建token回调，模型: {final_model_name}")
        
        # 准备回调列表
        callbacks = [token_callback]
        
        # 使用连接池获取LLM实例
        llm = get_llm_from_pool(
            model_name=final_model_name,
            temperature=temperature,
            extra_headers=extra_headers,
            is_reasoning=is_reasoning,
            agent_name=agent_name,
            callbacks=callbacks
        )
        
        logger.debug(f"从连接池获取LLM实例: {agent_name} -> {final_model_name}")
        return llm
        
    
    def _build_user_context(self, state: UnifiedQAState) -> Dict[str, Any]:
        """构建清晰的用户对话上下文信息，基于智能消息分析"""
        
        if not state.messages or len(state.messages) < 3:
            return {}
        
        # 使用智能消息分析来构建上下文
        historical_messages = state.messages[:-1]  # 排除当前消息
        conversation_turns = self._analyze_conversation_structure(historical_messages)
        
        if len(conversation_turns) < 1:
            return {}
        
        # 构建格式化的对话历史
        formatted_conversations = []
        
        # 取最近的5轮对话作为上下文
        recent_turns = conversation_turns[-5:] if len(conversation_turns) >= 5 else conversation_turns
        
        for turn_idx, turn in enumerate(recent_turns):
            # 提取用户消息
            user_content = ""
            if turn["user_msg"]:
                user_content = self._extract_clean_content(turn["user_msg"][1])
            
            # 提取AI最终回答
            ai_content = ""
            best_ai_msg = self._select_best_ai_response(turn["ai_messages"])
            if best_ai_msg:
                ai_content = self._extract_clean_content(best_ai_msg)
                # 截断过长的回答但保留关键信息
                if len(ai_content) > 300:
                    ai_content = ai_content[:250] + "...[内容已截断]"
            
            # 只有当有有效内容时才添加到历史中
            if user_content and ai_content:
                formatted_conversations.append({
                    "turn": turn_idx + 1,
                    "user": user_content,
                    "assistant": ai_content
                })
        
        if not formatted_conversations:
            return {}
        
        # 构建结构化的对话历史文本，便于LLM理解
        formatted_history = []
        for i, turn in enumerate(formatted_conversations):
            turn_number = i + 1
            formatted_turn = f"📝 对话轮次 {turn_number}:\n👤 用户: {turn['user']}\n🤖 助手: {turn['assistant']}"
            formatted_history.append(formatted_turn)
        
        session_history = "\n\n".join(formatted_history)
        
        user_context = {
            "session_history": session_history,
            "conversation_count": len(formatted_conversations),
            "total_historical_turns": len(conversation_turns)
        }
        
        logger.info(f"构建智能用户上下文: 包含{len(formatted_conversations)}轮清晰的对话历史，总共{len(conversation_turns)}轮原始对话")
        
        return user_context
    
    def _extract_clean_content(self, msg):
        """从消息中提取干净的显示内容"""
        if isinstance(msg, HumanMessage):
            return self._extract_message_text(msg)
        elif isinstance(msg, AIMessage):
            # 使用通用的内容提取逻辑
            content = self._extract_content_text(msg.content)
            
            # 清理AI消息中的特殊标记，但保留有用内容
            clean_content = content
            
            # 移除工具调用标记
            clean_content = re.sub(r'\[TOOL_CALL\].*?\[/TOOL_CALL\]', '', clean_content, flags=re.DOTALL)
            clean_content = re.sub(r'<think>.*?</think>', '', clean_content, flags=re.DOTALL)
            clean_content = re.sub(r'<think>.*', '', clean_content, flags=re.DOTALL)
            clean_content = re.sub(r'.*</think>', '', clean_content, flags=re.DOTALL)
            clean_content = re.sub(r'\[HANDOFF\].*?\[/HANDOFF\]', '', clean_content, flags=re.DOTALL)
            
            # 移除任务完成标记（更全面的匹配）
            clean_content = re.sub(r'\[TASK_COMPLETE\]\s*', '', clean_content)
            clean_content = re.sub(r'\[/TASK_COMPLETE\]\s*', '', clean_content)
            
            # 移除agent格式标记（更灵活的匹配）
            # 匹配 "## name\n任意agent名称\n可能的空格和换行\n## content\n"
            clean_content = re.sub(r'##\s*name\s*\n\s*\w+\s*\n+\s*##\s*content\s*\n+', '', clean_content, flags=re.DOTALL)
            
            # 移除可能残留的单独的 ## name 或 ## content 行
            clean_content = re.sub(r'##\s*name\s*\n', '', clean_content)
            clean_content = re.sub(r'##\s*content\s*\n', '', clean_content)
            
            # 移除agent名称行（如果agent格式标记被部分清理）
            clean_content = re.sub(r'^\s*(knowledge_agent|mcp_agent|multimodal_agent|simple_chat|custom_rag_agent|supervisor)\s*\n', '', clean_content, flags=re.MULTILINE)
            
            # 移除多余的空行和空格
            clean_content = re.sub(r'\n\s*\n', '\n', clean_content)
            clean_content = clean_content.strip()
            
            return clean_content if clean_content else "[无有效内容]"
        else:
            return str(msg.content) if hasattr(msg, 'content') else str(msg)
    
    def _extract_content_text(self, content):
        """通用的内容文本提取方法，处理字符串和列表格式"""
        if isinstance(content, list):
            text_parts = []
            for item in content:
                if isinstance(item, dict) and item.get('type') == 'text':
                    text_parts.append(item.get('text', ''))
                elif isinstance(item, str):
                    text_parts.append(item)
            return " ".join(text_parts)
        elif isinstance(content, str):
            return content
        else:
            return str(content)

    def _extract_message_text(self, msg: HumanMessage) -> str:
        """从消息中提取文本内容，处理多种格式"""
        if isinstance(msg.content, list):
            text_parts = []
            for item in msg.content:
                if isinstance(item, dict) and item.get('type') == 'text':
                    text_parts.append(item.get('text', ''))
                elif isinstance(item, str):
                    text_parts.append(item)
            return " ".join(text_parts)
        else:
            # content是字符串类型
            return str(msg.content)
    
    def _get_agent_description(self, agent_type: str) -> str:
        """获取Agent的描述信息"""
        descriptions = {
            "simple_chat": "简单问答助手",
            "knowledge_agent": "三峡集团知识专家",
            "mcp_agent": "外部工具专家",
            "multimodal_agent": "多模态处理专家",
            "custom_rag_agent": "自定义知识库专家"
        }
        return descriptions.get(agent_type, agent_type)

    
    def _build_agent_config(self, state: UnifiedQAState, suffix: str = "") -> Dict[str, Any]:
        """构建Agent配置"""
        thread_id = state.thread_id or "default"
        if suffix:
            thread_id = f"{thread_id}_{suffix}"
        
        return {
            "configurable": {
                "thread_id": thread_id,
                "checkpoint_ns": "unified_multi_agent"
            }
        }


def create_unified_multi_agent_graph(
    checkpointer: Any = None,
    mcp_router: Any = None,
    mcp_simple_router: Any = None,
    enable_global_context_summarization: bool = True
):
    """创建统一的多智能体问答图
    
    Args:
        checkpointer: 检查点保存器
        mcp_router: MCP RAG路由器
        mcp_simple_router: MCP简单路由器
        enable_global_context_summarization: 是否启用全局上下文总结
        
    Returns:
        StateGraph: 编译好的图实例
    """
    unified_graph = UnifiedMultiAgentGraph(
        checkpointer=checkpointer,
        mcp_router=mcp_router,
        mcp_simple_router=mcp_simple_router,
        enable_global_context_summarization=enable_global_context_summarization
    )
    
    graph = unified_graph.build_graph()
    logger.info("统一多智能体问答图创建完成")

    save_graph_image(graph, "unified_multi_agent_graph.mmd")
    
    return graph

def save_graph_image(app, filename):
    """Save graph visualization as an image file."""
    try:
        mermaid_code = app.get_graph(xray=1).draw_mermaid()
        with open(filename, "w", encoding="utf-8") as f:
            f.write(mermaid_code)
        print(f"工作流Mermaid代码已保存到 {filename}")
        print("请使用以下方式查看图形:")
        print("1. 在线: 访问 https://mermaid.live/ 并粘贴文件内容")
        print("2. VS Code: 安装Mermaid扩展并打开.mmd文件")
        print("3. 其他Mermaid查看器或编辑器")
    except Exception as e:
        print(f"无法保存工作流图: {e}")