"""
智能意图分析器

基于LLM快速准确地分析用户查询，决定最佳的多智能体处理策略。
"""

import os
from typing import Dict, Any, List, Literal, Optional
from pydantic import BaseModel, Field
from langchain_openai import ChatOpenAI
from langchain_core.messages import SystemMessage, HumanMessage
from .utils.structured_output_helper import create_safe_structured_output

import sys
sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))
from logger import logger
from jiliang_chat_prompt import INTENT_ANALYZER_PROMPT, INTENT_ANALYZER_USER_PROMPT


class IntentAnalysisResult(BaseModel):
    """意图分析结果"""
    
    # 处理模式
    processing_mode: Literal["direct_mode", "supervisor_mode", "swarm_mode"] = Field(
        description="最佳处理模式"
    )
    
    # 主要Agent
    primary_agent: Literal["simple_chat", "knowledge_agent", "mcp_agent", "multimodal_agent", "custom_rag_agent"] = Field(
        description="主要处理Agent"
    )
    
    # 协作Agent列表
    secondary_agents: List[str] = Field(
        description="需要协作的其他Agent",
        default_factory=list
    )
    
    # 复杂度评估
    complexity: Literal["simple", "medium", "complex"] = Field(
        description="任务复杂度"
    )
    
    # 分析置信度
    confidence: float = Field(
        description="分析结果置信度",
        ge=0.0,
        le=1.0
    )
    
    # 推理过程
    reasoning: str = Field(
        description="决策推理过程"
    )
    
    # 预估执行时间
    estimated_duration: Optional[int] = Field(
        description="预估执行时间（秒）",
        default=None
    )


class IntentAnalyzer:
    """智能意图分析器
    
    使用LLM快速分析用户意图，确定最佳的多智能体处理策略。
    专为高效响应而优化。
    """
    
    def __init__(self, llm: ChatOpenAI):
        """初始化意图分析器
        
        Args:
            llm: ChatOpenAI模型实例，建议使用快速模型
        """
        self.structured_output_helper = create_safe_structured_output(
            max_repair_attempts=5,      
            max_llm_retries=3,          
            enable_logging=True,        
            timeout_seconds=120000         
        )
        self.llm = llm
        
    async def analyze_intent(
        self,
        question: str,
        mcp_ids: Optional[List[str]] = None,
        extral_rag_ids: Optional[List[str]] = None,
        file_uploads: list = [],
        user_context: Optional[Dict[str, Any]] = None
    ) -> IntentAnalysisResult:
        """分析用户意图并确定处理策略
        
        Args:
            question: 用户问题
            mcp_ids: 用户选择的MCP工具ID列表
            extral_rag_ids: 额外的RAG知识库ID列表
            file_uploads: 文件上传列表
            user_context: 用户上下文信息
            
        Returns:
            IntentAnalysisResult: 意图分析结果
        """
        try:
            # 构建上下文信息
            context_info = self._build_context_info(
                mcp_ids, extral_rag_ids, file_uploads, user_context
            )
            
            # 构建分析提示词
            user_message = self._build_analysis_prompt(question, context_info)
            
            # 快速LLM分析
            messages = [
                SystemMessage(content=INTENT_ANALYZER_PROMPT),
                HumanMessage(content=user_message)
            ]
            
            result = await self.structured_output_helper.safe_ainvoke(
                self.llm,
                IntentAnalysisResult,
                messages,
                config=None
            )
            
            # 后处理优化结果
            result = self._post_process_result(
                result["parsed"], mcp_ids, extral_rag_ids, file_uploads
            )
            
            logger.info(
                f"意图分析完成: '{question[:30]}...' -> "
                f"{result.processing_mode}/{result.primary_agent} "
                f"(置信度: {result.confidence:.2f})"
            )
            
            return result
            
        except Exception as e:
            logger.error(f"意图分析失败: {e}")
            return self._get_fallback_result(mcp_ids, extral_rag_ids, file_uploads)
    
    def _build_context_info(
        self,
        mcp_ids: Optional[List[str]],
        extral_rag_ids: Optional[List[str]],
        file_uploads: list,
        user_context: Optional[Dict[str, Any]]
    ) -> str:
        """构建上下文信息字符串"""
        
        context_parts = []
        
        # 用户选择信息
        if mcp_ids:
            context_parts.append(f"用户选择了MCP工具")
        
        if extral_rag_ids:
            context_parts.append(f"指定了额外知识库")
        
        if file_uploads:
            file_name_list = [f.filename for f in file_uploads]
            context_parts.append("用户上传了文件需要处理: " + ", ".join(file_name_list))
        
        # 用户上下文
        if user_context:
            session_history = f"和用户的历史对话信息: \n{user_context.get('session_history', '无')} \n"
            user_preferences = f"用户的偏好信息: \n{user_context.get('user_preferences', '无')} \n"
            context_parts.append(session_history)
            context_parts.append(user_preferences)
        
        if not context_parts:
            context_parts.append("标准查询，无特殊上下文")
        
        return "\n---------------\n".join(context_parts)
    
    def _build_analysis_prompt(self, question: str, context_info: str) -> str:
        """构建分析提示词"""
        
        return INTENT_ANALYZER_USER_PROMPT.format(
            question=question,
            context_info=context_info
        )
    
    def _post_process_result(
        self,
        result: IntentAnalysisResult,
        mcp_ids: Optional[List[str]],
        extral_rag_ids: Optional[List[str]],
        file_uploads: list
    ) -> IntentAnalysisResult:
        """后处理和优化分析结果"""
        
        # 强制规则：用户明确选择优先级最高
        if extral_rag_ids and result.primary_agent != "custom_rag_agent":
            logger.info("根据用户extral_rag_ids选择调整为custom_rag_agent")
            # 保留原有的primary_agent作为secondary
            if result.primary_agent not in result.secondary_agents:
                result.secondary_agents.insert(0, result.primary_agent)
            result.primary_agent = "custom_rag_agent"
            result.confidence = min(result.confidence + 0.1, 1.0)
        
        elif mcp_ids and result.primary_agent != "mcp_agent":
            logger.info("根据用户MCP选择调整为mcp_agent")
            # 保留原有的primary_agent作为secondary
            if result.primary_agent not in result.secondary_agents:
                result.secondary_agents.insert(0, result.primary_agent)
            result.primary_agent = "mcp_agent"
            result.confidence = min(result.confidence + 0.1, 1.0)
        
        if file_uploads and result.primary_agent != "multimodal_agent":
            logger.info("根据文件上传调整为multimodal_agent")
            # 保留原有的primary_agent作为secondary  
            if result.primary_agent not in result.secondary_agents:
                result.secondary_agents.insert(0, result.primary_agent)
            result.primary_agent = "multimodal_agent"
            result.confidence = min(result.confidence + 0.1, 1.0)
        
        # 预估执行时间
        if result.processing_mode == "direct_mode":
            result.estimated_duration = 10
        elif result.processing_mode == "supervisor_mode":
            result.estimated_duration = 20
        else:  # swarm_mode
            result.estimated_duration = 15
        
        # 去重secondary_agents
        result.secondary_agents = list(dict.fromkeys(result.secondary_agents))
        
        return result
    
    def _get_fallback_result(
        self,
        mcp_ids: Optional[List[str]],
        extral_rag_ids: Optional[List[str]],
        file_uploads: list
    ) -> IntentAnalysisResult:
        """获取fallback分析结果（用于异常情况）"""
        
        # 根据用户选择确定fallback策略
        if extral_rag_ids:
            primary_agent = "custom_rag_agent"
        elif mcp_ids:
            primary_agent = "mcp_agent"
        elif file_uploads:
            primary_agent = "multimodal_agent"
        else:
            primary_agent = "simple_chat"
        
        return IntentAnalysisResult(
            processing_mode="direct_mode",
            primary_agent=primary_agent,
            secondary_agents=[],
            complexity="simple",
            confidence=0.6,
            reasoning="意图分析失败，使用fallback策略",
            estimated_duration=10
        )


async def create_intent_analyzer(llm: ChatOpenAI) -> IntentAnalyzer:
    """创建意图分析器实例
    
    Args:
        llm: ChatOpenAI模型实例
        
    Returns:
        IntentAnalyzer: 意图分析器实例
    """
    analyzer = IntentAnalyzer(llm)
    logger.info("智能意图分析器创建完成")
    return analyzer