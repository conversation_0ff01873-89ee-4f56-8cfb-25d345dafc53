"""
基于文本解析的统一多智能体图系统

支持文本模式和function calling模式的统一多智能体问答图，使用模式切换控制。
不依赖模型的function calling能力，通过结构化文本解析实现Agent协调。
"""

import re
import time
from typing import Dict, Any, List, Optional, Literal, Annotated
from langgraph.graph import StateGraph, START, END
from langgraph.graph.message import add_messages, REMOVE_ALL_MESSAGES
from langchain_core.messages import BaseMessage, HumanMessage, AIMessage, SystemMessage, ToolMessage
from langchain_core.messages.modifier import RemoveMessage
from langchain_openai import ChatOpenAI
from langchain_deepseek import ChatDeepSeek
from pydantic import BaseModel, Field
from pydantic import SecretStr
import os
import asyncio
from langgraph.checkpoint.memory import MemorySaver
from langgraph.types import RetryPolicy

from .text_supervisor_swarm_nodes import TextSupervisorSwarmNodes
from .cache_utils import _create_llm
from .cache_utils import AgentManager
from .unified_graph import UnifiedQAState

import sys
sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))
from logger import logger
from jiliang_chat_prompt import (
    INTENT_ANALYZER_PROMPT,
    SUPERVISOR_AGENT_PROMPT,
    SIMPLE_CHAT_PROMPT
)
import typing_compat
# 导入上下文总结功能
from context_summarizer import get_default_summarizer
try:
    from langmem.short_term import RunningSummary
except ImportError:
    RunningSummary = None

# 缓存
from langgraph.types import CachePolicy

class TextUnifiedQAState(UnifiedQAState):
    """扩展的统一问答状态，支持模式切换和上下文管理"""
    
    # 执行模式：function_call, text_parsing, 或 react_mode
    execution_mode: str = Field(default="react_mode")
    
    # 全局上下文总结状态（用于LangMem全局上下文管理）
    global_running_summary: Optional[Any] = Field(default=None)  # RunningSummary类型，但防止导入错误使用Any
    
    # 文本解析结果存储
    parsed_actions: List[Dict[str, Any]] = Field(default_factory=list)
    
    # ReAct模式配置
    max_iterations: int = Field(default=20)
    use_react_agents: bool = Field(default=True)
    
    # 消息过滤配置
    filter_agent_messages: bool = Field(default=True)  # True=只返回最后一条，False=返回全部
    
    # Agent名称包含方式：None=使用message.name，"inline"=内联到内容中
    include_agent_name: str = Field(default="inline")


class TextUnifiedMultiAgentGraph:
    """基于文本解析的统一多智能体问答图
    
    支持文本模式和function calling模式切换的完整LangGraph实现。
    """
    
    def __init__(self, 
                 checkpointer: Any = None,
                 mcp_router: Any = None,
                 mcp_simple_router: Any = None,
                 execution_mode: str = "react_mode",
                 filter_agent_messages: bool = False,
                 include_agent_name: str = "inline",
                 enable_global_context_summarization: bool = True):
        """初始化文本统一图
        
        Args:
            checkpointer: 检查点保存器
            mcp_router: MCP RAG路由器  
            mcp_simple_router: MCP简单路由器
            execution_mode: 执行模式 ("text_parsing", "function_call", 或 "react_mode")
            filter_agent_messages: 是否过滤Agent消息（True=只返回最后一条，False=返回全部）
            include_agent_name: Agent名称包含方式（None=使用message.name，"inline"=内联到内容中）
            enable_global_context_summarization: 是否启用全局上下文总结
        """
        self.checkpointer = checkpointer
        self.mcp_router = mcp_router
        self.mcp_simple_router = mcp_simple_router
        self.execution_mode = execution_mode
        self.filter_agent_messages = filter_agent_messages
        self.include_agent_name = include_agent_name
        self.enable_global_context_summarization = enable_global_context_summarization
        self.graph = None

        # 初始化Agent管理器
        self.agent_manager = AgentManager(
            checkpointer=checkpointer,
            mcp_router=mcp_router,
            mcp_simple_router=mcp_simple_router,
            execution_mode=execution_mode
        )
        
        # 异步初始化Agent管理器（在build_graph中调用）
        self._agent_initialization_task = None
        
        # 初始化全局上下文总结器
        self.global_context_summarizer = None
        if self.enable_global_context_summarization:
            try:
                # 使用更高的token阈值进行全局上下文管理
                from context_summarizer import ContextSummarizer, SummarizationConfig
                global_config = SummarizationConfig.from_env()
                # 全局上下文管理使用更高的阈值，避免与Agent内部压缩冲突
                global_config.max_tokens_before_summary = max(20000, global_config.max_tokens_before_summary * 2)
                global_config.max_tokens = max(25000, global_config.max_tokens * 2)
                self.global_context_summarizer = ContextSummarizer(global_config)
                logger.info(f"文本统一图全局上下文总结功能已启用，阈值: {global_config.max_tokens_before_summary}")
            except Exception as e:
                logger.warning(f"文本统一图全局上下文总结器初始化失败: {e}")
                self.enable_global_context_summarization = False
        
        # 初始化supervisor和swarm节点处理器
        self.supervisor_swarm_nodes = TextSupervisorSwarmNodes(
            mcp_router=mcp_router,
            mcp_simple_router=mcp_simple_router,
            checkpointer=checkpointer,
            agent_manager=self.agent_manager
        )
        
        logger.info(f"文本统一图初始化，执行模式: {execution_mode}")
        
    def build_graph(self):
        """构建基于文本解析的统一多智能体图"""
        
        try:
            # Agent管理器将在服务初始化时完成初始化，这里不需要创建异步任务
            # 移除异步任务创建逻辑，因为服务启动时会同步等待初始化完成
            
            # 创建状态图
            builder = StateGraph(TextUnifiedQAState)
            
            # 添加消息清理节点作为入口 - 确保每次处理都从干净的上下文开始
            builder.add_node("message_cleaner", self._message_cleaner_node)
            
            # 设置消息清理节点为入口点
            builder.set_entry_point("message_cleaner")
            
            # 添加其他节点
            if self.enable_global_context_summarization and self.global_context_summarizer:
                # 启用全局上下文总结时，添加全局总结节点
                builder.add_node("global_context_summarization", self._global_context_summarization_node)
                builder.add_node("mode_switcher", self._mode_switcher_node)
                
                # 消息清理后进入全局上下文总结
                builder.add_edge("message_cleaner", "global_context_summarization")
                
                # 全局上下文总结后进入模式切换
                builder.add_edge("global_context_summarization", "mode_switcher")
            else:
                # 未启用全局上下文总结时，消息清理后直接进入模式切换
                builder.add_node("mode_switcher", self._mode_switcher_node)
                
                # 消息清理后进入模式切换
                builder.add_edge("message_cleaner", "mode_switcher")
            
            # 添加其他节点
            builder.add_node("intent_analyzer", self._intent_analyzer_node, cache_policy=CachePolicy(ttl=3600), retry_policy=RetryPolicy(max_attempts=5))
            builder.add_node("text_simple_chat", self._text_simple_chat_node, retry_policy=RetryPolicy(max_attempts=5))
            builder.add_node("text_knowledge_agent", self._text_knowledge_agent_node, retry_policy=RetryPolicy(max_attempts=5))
            builder.add_node("text_mcp_agent", self._text_mcp_agent_node, retry_policy=RetryPolicy(max_attempts=5))
            builder.add_node("text_multimodal_agent", self._text_multimodal_agent_node, retry_policy=RetryPolicy(max_attempts=5))
            builder.add_node("text_custom_rag_agent", self._text_custom_rag_agent_node, retry_policy=RetryPolicy(max_attempts=5))
            builder.add_node("text_supervisor", self._text_supervisor_node, retry_policy=RetryPolicy(max_attempts=5))
            builder.add_node("text_parallel_coordinator", self._text_parallel_coordinator_node, retry_policy=RetryPolicy(max_attempts=5))
            
            # 模式切换后的路由
            builder.add_conditional_edges(
                "mode_switcher",
                self._route_after_mode_switch,
                {
                    "intent_analyzer": "intent_analyzer",
                    "fallback": "text_simple_chat"
                }
            )
            
            # 意图分析后的路由
            builder.add_conditional_edges(
                "intent_analyzer",
                self._route_after_intent_analysis,
                {
                    "text_simple_chat": "text_simple_chat",
                    "text_knowledge_agent": "text_knowledge_agent",
                    "text_mcp_agent": "text_mcp_agent",
                    "text_multimodal_agent": "text_multimodal_agent",
                    "text_custom_rag_agent": "text_custom_rag_agent",
                    "text_supervisor": "text_supervisor",
                    "text_parallel_coordinator": "text_parallel_coordinator"
                }
            )
            
            # 所有Agent节点直接结束（消息清理在入口处已完成）
            builder.add_edge("text_simple_chat", END)
            builder.add_edge("text_knowledge_agent", END)
            builder.add_edge("text_mcp_agent", END)
            builder.add_edge("text_multimodal_agent", END)
            builder.add_edge("text_custom_rag_agent", END)
            builder.add_edge("text_supervisor", END)
            builder.add_edge("text_parallel_coordinator", END)
            
            # 编译图
            self.graph = builder.compile(checkpointer=self.checkpointer)
            
            logger.info("基于文本解析的统一多智能体图构建完成")
            return self.graph
            
        except Exception as e:
            logger.error(f"构建文本统一图失败: {e}")
            raise
    
    async def _global_context_summarization_node(self, state: TextUnifiedQAState) -> dict:
        """全局上下文总结节点：在系统入口处进行全局上下文管理（优化版）"""
        try:
            # 快速检查：如果功能未启用，立即返回
            if not self.enable_global_context_summarization or not self.global_context_summarizer:
                logger.debug("全局上下文总结功能未启用，跳过")
                return {}
            
            messages = state.messages
            if not messages or len(messages) < 3:
                logger.debug("消息数量不足，跳过全局上下文总结")
                return {}
            
            # 快速token估算，避免调用重型方法
            estimated_tokens = sum(len(str(msg.content)) for msg in messages if hasattr(msg, 'content')) // 2
            if estimated_tokens < 8000:  # 远低于阈值，直接跳过
                logger.debug(f"估算token数({estimated_tokens})低于阈值，跳过全局上下文总结")
                return {}
            
            # 从状态中获取全局运行总结
            global_running_summary = state.global_running_summary
            
            # 精确检查是否需要进行全局总结
            if not self.global_context_summarizer.should_summarize(messages):
                logger.debug("全局上下文总结器判断不需要总结")
                return {}
            
            logger.info(f"文本统一图开始全局上下文总结：{len(messages)}条消息，估算tokens: {estimated_tokens}")
            
            # 执行全局总结
            processed_messages, updated_summary = self.global_context_summarizer.summarize_messages_with_state(
                messages, global_running_summary
            )
            
            # 检查是否实际进行了压缩
            if len(processed_messages) >= len(messages):
                # 没有进行实际的压缩，返回空更新
                logger.debug("全局上下文总结未产生实际压缩效果")
                return {}
            
            logger.info(f"文本统一图全局上下文总结完成：{len(messages)} -> {len(processed_messages)}条消息")
            
            # 构建状态更新：使用RemoveMessage移除所有现有消息，然后添加处理后的消息
            state_update = {
                "messages": [RemoveMessage(id=REMOVE_ALL_MESSAGES)] + processed_messages
            }
            
            # 如果有更新的全局运行总结，也保存到状态中
            if updated_summary != global_running_summary:
                state_update["global_running_summary"] = updated_summary  # type: ignore
                logger.debug("文本统一图更新了全局运行总结状态")
            
            return state_update
            
        except Exception as e:
            logger.error(f"文本统一图全局上下文总结失败: {e}")
            # 发生错误时返回空更新，不影响正常流程
            return {}
    
    async def _mode_switcher_node(self, state: TextUnifiedQAState) -> TextUnifiedQAState:
        """模式切换节点"""
        
        try:
            # 设置执行模式
            state.execution_mode = self.execution_mode
            # 设置消息过滤模式
            state.filter_agent_messages = self.filter_agent_messages
            
            # 从消息中提取问题
            if state.messages:
                last_human_msg = None
                for msg in reversed(state.messages):
                    if isinstance(msg, HumanMessage):
                        last_human_msg = msg
                        break
                
                if last_human_msg:
                    # 确保 current_question 是字符串类型
                    content = last_human_msg.content
                    if isinstance(content, list):
                        # 如果是列表格式，提取文本内容
                        text_parts = []
                        for item in content:
                            if isinstance(item, dict) and item.get('type') == 'text':
                                text_parts.append(item.get('text', ''))
                            elif isinstance(item, str):
                                text_parts.append(item)
                        state.current_question = ' '.join(text_parts)
                    elif isinstance(content, str):
                        state.current_question = content
                    else:
                        state.current_question = str(content)
            
            logger.info(f"模式切换完成: {state.execution_mode}")
            return state
            
        except Exception as e:
            logger.error(f"模式切换失败: {e}")
            state.execution_mode = "react_mode"  # 默认ReAct模式
            return state
    
    async def _route_after_mode_switch(self, state: TextUnifiedQAState) -> str:
        """模式切换后的路由决策"""
        
        if state.execution_mode in ["text_parsing", "react_mode"]:
            # 文本模式和ReAct模式都需要意图分析
            return "intent_analyzer"
        else:
            # function call模式的fallback
            return "fallback"
    
    async def _intent_analyzer_node(self, state: TextUnifiedQAState) -> TextUnifiedQAState:
        """意图分析节点（优化版：使用预缓存的Agent）"""
        
        try:
            logger.info("执行文本模式意图分析")
            
            # Agent初始化已在服务启动时完成，这里可以直接使用预缓存的Agent
            
            # 使用优化的AgentManager获取意图分析器
            intent_llm = await self.agent_manager.get_intent_analyzer()
            
            # 构建用户上下文
            user_context = self._build_user_context(state)
            
            result = await intent_llm.analyze_intent(
                state.current_question, 
                state.mcp_ids, 
                state.extral_rag_ids, 
                state.file_uploads,
                user_context
            )
            
            # 解析结果
            state.processing_mode = result.processing_mode
            state.primary_agent = result.primary_agent
            state.secondary_agents = result.secondary_agents
            state.confidence = result.confidence
            
            logger.info(f"文本模式意图分析完成: {state.processing_mode}/{state.primary_agent}")
            
            return state
            
        except Exception as e:
            logger.error(f"文本模式意图分析失败: {e}")
            # 使用简单fallback
            state.processing_mode = "direct_mode"
            state.primary_agent = "simple_chat"
            state.confidence = 0.5
            return state
    
    async def _route_after_intent_analysis(self, state: TextUnifiedQAState) -> str:
        """意图分析后的路由决策（完整版）"""
        
        # 确保 primary_agent 不为 None
        if state.primary_agent is None:
            state.primary_agent = "simple_chat"
        
        # 根据处理模式和主要Agent决定下一个节点
        if state.processing_mode == "direct_mode":
            # 直接模式：映射到对应的文本模式Agent
            agent_mapping = {
                "simple_chat": "text_simple_chat",
                "knowledge_agent": "text_knowledge_agent",
                "mcp_agent": "text_mcp_agent",
                "multimodal_agent": "text_multimodal_agent",
                "custom_rag_agent": "text_custom_rag_agent"
            }
            mapped_agent = agent_mapping.get(state.primary_agent, "text_simple_chat")
            logger.info(f"直接模式路由到文本模式Agent: {mapped_agent}")
            return mapped_agent
            
        elif state.processing_mode == "supervisor_mode":
            logger.info("监督者模式路由到文本监督者")
            return "text_supervisor"
            
        elif state.processing_mode == "swarm_mode":
            logger.info("并行模式路由到文本并行协调器")
            return "text_parallel_coordinator"
            
        else:
            # 默认直接模式
            agent_mapping = {
                "simple_chat": "text_simple_chat",
                "knowledge_agent": "text_knowledge_agent",
                "mcp_agent": "text_mcp_agent",
                "multimodal_agent": "text_multimodal_agent",
                "custom_rag_agent": "text_custom_rag_agent"
            }
            mapped_agent = agent_mapping.get(state.primary_agent, "text_simple_chat")
            logger.info(f"默认路由到文本模式Agent: {mapped_agent}")
            return mapped_agent
    
    async def _text_simple_chat_node(self, state: TextUnifiedQAState) -> TextUnifiedQAState:
        """文本模式简单问答节点"""

        try:
            logger.info("执行文本模式简单问答Agent")

            # Agent初始化已在服务启动时完成
            
            # 使用优化的AgentManager获取Agent（大多数情况使用预缓存）
            agent = await self.agent_manager.get_simple_chat_agent(
                model_desc=state.model_desc,  # 通常为空
                handoff_tools=[]  # 通常为空
            )

            # 执行Agent
            config = self._build_agent_config(state, "text_simple_chat")
            result = await agent.ainvoke(
                {"messages": state.messages},
                config=config
            )

            # 更新状态
            new_messages = result.get("messages", [])
            state.messages.extend(new_messages)
            state.agent_results["text_simple_chat"] = result
            state.execution_status = "completed"

            logger.info("文本模式简单问答Agent执行完成")
            return state

        except Exception as e:
            logger.error(f"文本模式简单问答Agent执行失败: {e}")
            error_msg = AIMessage(content=f"抱歉，处理您的问题时遇到了问题：{str(e)}")
            state.messages.append(error_msg)
            state.execution_status = "failed"
            return state
    
    async def _text_knowledge_agent_node(self, state: TextUnifiedQAState) -> TextUnifiedQAState:
        """文本模式知识专家Agent节点（支持ReAct模式）"""

        try:
            logger.info(f"执行文本模式知识专家Agent，模式: {state.execution_mode}")

            # Agent初始化已在服务启动时完成
            
            config = self._build_agent_config(state, "text_knowledge")

            # 使用优化的AgentManager获取知识agent（大多数情况使用预缓存）
            agent = await self.agent_manager.get_knowledge_agent(
                model_desc=state.model_desc,  # 通常为空
                handoff_tools=[],  # 通常为空
                max_iterations=state.max_iterations  # 通常为20
            )

            # 执行Agent
            result = await agent.ainvoke(
                {"messages": state.messages},
                config=config
            )
            logger.info("知识专家Agent执行完成")

            # 更新状态
            new_messages = result.get("messages", [])
            state.messages.extend(new_messages)
            state.agent_results["text_knowledge_agent"] = result
            state.execution_status = "completed"

            return state

        except Exception as e:
            logger.error(f"文本模式知识专家Agent执行失败: {e}")
            error_msg = AIMessage(content=f"知识查询时遇到问题：{str(e)}")
            state.messages.append(error_msg)
            state.execution_status = "failed"
            return state
    
    async def _text_mcp_agent_node(self, state: TextUnifiedQAState) -> TextUnifiedQAState:
        """文本模式MCP工具专家Agent节点（支持ReAct模式）"""

        try:
            logger.info(f"执行文本模式MCP工具专家Agent，模式: {state.execution_mode}")

            # 使用AgentManager获取Agent
            tool_model = os.getenv("TOOL_MODEL", "qwen2---5-72b-goxmbugy")
            llm = _create_llm(extra_headers=state.extra_headers, model_name=tool_model, agent_name="mcp_agent")
            config = self._build_agent_config(state, "text_mcp")

            # 使用AgentManager获取MCP agent
            agent = await self.agent_manager.get_mcp_agent(
                llm=llm,
                question=state.current_question,
                mcp_ids=state.mcp_ids,
                model_desc=state.model_desc,
                handoff_tools=[],
                max_iterations=state.max_iterations,
                api_key=getattr(state.extra_headers, 'api_key', None),
                username=getattr(state.extra_headers, 'username', None)
            )

            # 执行Agent
            result = await agent.ainvoke(
                {"messages": state.messages},
                config=config
            )
            logger.info("MCP工具专家Agent执行完成")

            # 更新状态
            new_messages = result.get("messages", [])
            state.messages.extend(new_messages)
            state.agent_results["text_mcp_agent"] = result
            state.execution_status = "completed"

            return state

        except Exception as e:
            logger.error(f"文本模式MCP工具专家Agent执行失败: {e}")
            error_msg = AIMessage(content=f"工具调用时遇到问题：{str(e)}")
            state.messages.append(error_msg)
            state.execution_status = "failed"
            return state
    
    async def _text_multimodal_agent_node(self, state: TextUnifiedQAState) -> TextUnifiedQAState:
        """文本模式多模态专家Agent节点（支持ReAct模式）"""

        try:
            logger.info(f"执行文本模式多模态专家Agent，模式: {state.execution_mode}")

            # 使用AgentManager获取Agent
            multimodal_model = os.getenv("MULTIMODAL_MODEL", "qwen2-5-vl-72b")
            llm = _create_llm(extra_headers=state.extra_headers, model_name=multimodal_model, agent_name="multimodal_agent")
            config = self._build_agent_config(state, "text_multimodal")

            # 使用AgentManager获取多模态agent
            agent = await self.agent_manager.get_multimodal_agent(
                llm=llm,
                model_desc=state.model_desc,
                handoff_tools=[],
                max_iterations=state.max_iterations,
                uploaded_files=state.file_uploads
            )

            # 执行Agent
            result = await agent.ainvoke(
                {"messages": state.messages},
                config=config
            )
            logger.info("多模态专家Agent执行完成")

            # 更新状态
            new_messages = result.get("messages", [])
            state.messages.extend(new_messages)
            state.agent_results["text_multimodal_agent"] = result
            state.execution_status = "completed"

            return state

        except Exception as e:
            logger.error(f"文本模式多模态专家Agent执行失败: {e}")
            error_msg = AIMessage(content=f"多模态处理时遇到问题：{str(e)}")
            state.messages.append(error_msg)
            state.execution_status = "failed"
            return state
    
    async def _text_custom_rag_agent_node(self, state: TextUnifiedQAState) -> TextUnifiedQAState:
        """文本模式自定义RAG专家Agent节点（支持ReAct模式）"""

        try:
            logger.info(f"执行文本模式自定义RAG专家Agent，模式: {state.execution_mode}")

            # 使用AgentManager获取Agent
            knowledge_model = os.getenv("KNOWLEDGE_MODEL", "qwen2---5-72b-goxmbugy")
            llm = _create_llm(extra_headers=state.extra_headers, model_name=knowledge_model, agent_name="custom_rag_agent")
            config = self._build_agent_config(state, "text_custom_rag")

            # 使用AgentManager获取自定义RAG agent
            agent = await self.agent_manager.get_custom_rag_agent(
                llm=llm,
                custom_rag_ids=state.extral_rag_ids or [],
                model_desc=state.model_desc,
                handoff_tools=[],
                max_iterations=state.max_iterations
            )

            # 执行Agent
            result = await agent.ainvoke(
                {"messages": state.messages},
                config=config
            )
            logger.info("自定义RAG专家Agent执行完成")

            # 更新状态
            new_messages = result.get("messages", [])
            state.messages.extend(new_messages)
            state.agent_results["text_custom_rag_agent"] = result
            state.execution_status = "completed"

            return state

        except Exception as e:
            logger.error(f"文本模式自定义RAG专家Agent执行失败: {e}")
            error_msg = AIMessage(content=f"自定义知识库查询时遇到问题：{str(e)}")
            state.messages.append(error_msg)
            state.execution_status = "failed"
            return state
    
    async def _text_supervisor_node(self, state: TextUnifiedQAState) -> TextUnifiedQAState:
        """文本模式监督者协调节点"""
        return await self.supervisor_swarm_nodes.text_supervisor_node(state)
    
    async def _text_parallel_coordinator_node(self, state: TextUnifiedQAState) -> TextUnifiedQAState:
        """文本模式并行协调节点（基于Swarm）"""
        return await self.supervisor_swarm_nodes.text_parallel_coordinator_node(state)
    
    def _is_real_human_message(self, msg) -> bool:
        """判断是否为真正的用户消息（排除工具执行结果以HumanMessage形式输出的情况）"""
        if not isinstance(msg, HumanMessage):
            return False
        
        # 检查环境变量，如果启用了ToolMessage，则所有HumanMessage都是真正的用户消息
        enable_toolmessage = os.getenv("ENABLE_TOOLMESSAGE", "false").lower() == "true"
        if enable_toolmessage and isinstance(msg, HumanMessage):
            return True
        elif str(msg.additional_kwargs.get("is_tool_result", '')).lower() == 'true':
            return False
        else:
            return True

    async def _message_cleaner_node(self, state: TextUnifiedQAState) -> dict:
        """消息清理节点：只保留每轮对话的真实用户输入和最后一条AI输出消息"""
        try:
            logger.info("执行消息清理，只保留真实用户输入和最后一条AI输出")
            
            messages = state.messages

            if len(messages) < 3:
                logger.info("消息太少，无需清理")
                return {}
            
            # 简单清理逻辑：识别对话轮次，每轮只保留真实用户输入+最后AI输出
            cleaned_messages = []
            current_turn_messages = []
            
            for msg in messages:
                if self._is_real_human_message(msg):
                    # 遇到真实用户消息，处理上一轮的消息
                    if current_turn_messages:
                        cleaned_messages.extend(self._extract_turn_key_messages(current_turn_messages))
                    
                    # 开始新的一轮
                    current_turn_messages = [msg]
                else:
                    # 收集当前轮的其他消息（包括AI消息和伪装的HumanMessage）
                    current_turn_messages.append(msg)
            
            # 处理最后一轮
            if current_turn_messages:
                cleaned_messages.extend(self._extract_turn_key_messages(current_turn_messages))
            
            if len(cleaned_messages) == len(messages):
                logger.info("没有找到需要清理的中间消息")
                return {}
            
            # 使用LangGraph的RemoveMessage机制进行消息更新            
            state_update = {
                "messages": [RemoveMessage(id=REMOVE_ALL_MESSAGES)] + cleaned_messages
            }
            
            filtered_count = len(messages) - len(cleaned_messages)
            logger.info(f"消息清理完成：原始 {len(messages)} 条消息 -> 清理后 {len(cleaned_messages)} 条消息")
            logger.info(f"过滤了 {filtered_count} 条中间执行消息")
            
            return state_update
            
        except Exception as e:
            logger.error(f"消息清理失败: {e}")
            # 清理失败时返回空字典，不影响正常流程
            return {}
    
    def _extract_turn_key_messages(self, turn_messages):
        """从一轮对话中提取关键消息：真实用户输入 + 最后一条AI输出"""
        if not turn_messages:
            return []
        
        key_messages = []
        
        # 第一条消息应该是真实用户输入
        if turn_messages and self._is_real_human_message(turn_messages[0]):
            key_messages.append(turn_messages[0])
        
        # 找到最后一条AI消息（忽略所有伪装的HumanMessage工具结果等）
        last_ai_msg = None
        for msg in reversed(turn_messages):
            if isinstance(msg, AIMessage):
                msg.content = self._extract_clean_content(msg)
                last_ai_msg = msg
                break
        
        if last_ai_msg:
            key_messages.append(last_ai_msg)
        
        return key_messages
    
    def _analyze_conversation_structure(self, messages):
        """分析对话结构，识别每轮对话的边界和关键消息"""
        turns = []
        current_turn = {"user_msg": None, "ai_messages": [], "tool_results": [], "start_idx": 0}
        
        for i, msg in enumerate(messages):
            if self._is_real_human_message(msg):
                # 发现新的用户消息，开始新的对话轮次
                if current_turn["user_msg"] is not None:
                    # 结束当前轮次
                    current_turn["end_idx"] = i - 1
                    turns.append(current_turn)
                    
                # 开始新轮次
                current_turn = {
                    "user_msg": (i, msg),
                    "ai_messages": [],
                    "tool_results": [],
                    "start_idx": i
                }
                
            elif isinstance(msg, AIMessage):
                # AI消息：需要分析是否为最终回答
                message_type = self._classify_ai_message(msg)
                current_turn["ai_messages"].append((i, msg, message_type))
                
            elif isinstance(msg, (HumanMessage, ToolMessage)):
                # 工具结果或状态报告消息
                if self._is_tool_result_or_status(msg):
                    current_turn["tool_results"].append((i, msg))
        
        # 处理最后一轮
        if current_turn["user_msg"] is not None:
            current_turn["end_idx"] = len(messages) - 1
            turns.append(current_turn)
            
        return turns
    
    def _classify_ai_message(self, msg: AIMessage):
        """分类AI消息类型"""
        # 使用通用的内容提取逻辑
        content = self._extract_content_text(msg.content)
        
        # 1. Supervisor决策消息
        if hasattr(msg, 'name') and msg.name == 'supervisor':
            if '[TASK_COMPLETE]' in content:
                return 'supervisor_final'
            return 'supervisor_decision'
        
        # 2. Swarm内部转交消息
        if any(pattern in content for pattern in [
            '【', '的处理结果】', '【转交给当前Agent的任务】',
            '请基于以上', '的处理结果和转交任务，继续完成用户的需求'
        ]):
            return 'swarm_internal'
        
        # 3. ReAct中间思考和工具调用
        if any(pattern in content for pattern in [
            '[TOOL_CALL]', '<think>', '</think>', '[HANDOFF]'
        ]):
            # 检查是否同时包含最终回答内容
            if self._has_substantial_final_content(content):
                return 'mixed_with_final'
            return 'react_intermediate'
        
        # 4. 纯粹的最终回答
        return 'final_answer'
    
    def _has_substantial_final_content(self, content):
        """检查内容是否包含实质性的最终回答"""
        # 使用通用的内容提取逻辑
        content_text = self._extract_content_text(content)
            
        # 移除所有工具调用和思考标记
        clean_content = content_text
        patterns_to_remove = [
            r'\[TOOL_CALL\].*?\[/TOOL_CALL\]',
            r'<think>.*?</think>',
            r'<think>.*',
            r'.*</think>',
            r'\[HANDOFF\].*?\[/HANDOFF\]',
            r'## name\n\w+\n\n## content\n'
        ]
        
        for pattern in patterns_to_remove:
            clean_content = re.sub(pattern, '', clean_content, flags=re.DOTALL)
        
        # 检查剩余内容是否足够实质性（超过50个字符且包含有意义的句子）
        clean_content = clean_content.strip()
        return len(clean_content) > 50 and ('。' in clean_content or '！' in clean_content or '？' in clean_content or len(clean_content) > 100)
    
    def _is_tool_result_or_status(self, msg):
        """检查是否为工具结果或状态报告消息"""
        if isinstance(msg, HumanMessage):
            additional_kwargs = getattr(msg, 'additional_kwargs', {})
            return (additional_kwargs.get('is_tool_result') == True or 
                    additional_kwargs.get('message_type') == 'agent_status_report')
        return False
    
    def _filter_messages_intelligently(self, conversation_turns):
        """智能过滤消息，保留每轮对话的关键消息"""
        cleaned_messages = []
        
        for turn in conversation_turns:
            # 1. 始终保留用户消息
            if turn["user_msg"]:
                cleaned_messages.append(turn["user_msg"][1])
            
            # 2. 智能选择AI回答
            final_ai_msg = self._select_best_ai_response(turn["ai_messages"])
            if final_ai_msg:
                cleaned_messages.append(final_ai_msg)
        
        return cleaned_messages
    
    def _select_best_ai_response(self, ai_messages):
        """从一轮对话的多个AI消息中选择最佳的最终回答"""
        if not ai_messages:
            return None
        
        # 优先级排序：
        # 1. supervisor_final (包含[TASK_COMPLETE]的supervisor消息)
        # 2. final_answer (纯粹的最终回答)
        # 3. mixed_with_final (包含工具调用但也有最终回答的消息)
        # 4. supervisor_decision (supervisor决策消息)
        # 最后考虑：react_intermediate (ReAct中间消息)
        
        priority_order = {
            'supervisor_final': 1,
            'final_answer': 2, 
            'mixed_with_final': 3,
            'supervisor_decision': 4,
            'react_intermediate': 5,
            'swarm_internal': 6  # 最低优先级，通常应该被过滤
        }
        
        # 按优先级和位置排序（同优先级选择最后出现的）
        sorted_messages = sorted(ai_messages, 
                               key=lambda x: (priority_order.get(x[2], 99), x[0]))
        
        # 选择最高优先级的消息
        best_msg = sorted_messages[0]
        
        # 如果是swarm_internal类型，尝试查找更好的替代
        if best_msg[2] == 'swarm_internal' and len(sorted_messages) > 1:
            for _, msg, msg_type in sorted_messages[1:]:
                if msg_type != 'swarm_internal':
                    return msg
        
        return best_msg[1]
    
    def _build_user_context(self, state: TextUnifiedQAState) -> Dict[str, Any]:
        """构建清晰的用户对话上下文信息，基于智能消息分析"""
        
        if not state.messages or len(state.messages) < 3:
            return {}
        
        # 使用智能消息分析来构建上下文
        historical_messages = state.messages[:-1]  # 排除当前消息
        conversation_turns = self._analyze_conversation_structure(historical_messages)
        
        if len(conversation_turns) < 1:
            return {}
        
        # 构建格式化的对话历史
        formatted_conversations = []
        
        # 取最近的5轮对话作为上下文
        recent_turns = conversation_turns[-5:] if len(conversation_turns) >= 5 else conversation_turns
        
        for turn_idx, turn in enumerate(recent_turns):
            # 提取用户消息
            user_content = ""
            if turn["user_msg"]:
                user_content = self._extract_clean_content(turn["user_msg"][1])
            
            # 提取AI最终回答
            ai_content = ""
            best_ai_msg = self._select_best_ai_response(turn["ai_messages"])
            if best_ai_msg:
                ai_content = self._extract_clean_content(best_ai_msg)
                # 截断过长的回答但保留关键信息
                if len(ai_content) > 300:
                    ai_content = ai_content[:250] + "...[内容已截断]"
            
            # 只有当有有效内容时才添加到历史中
            if user_content and ai_content:
                formatted_conversations.append({
                    "turn": turn_idx + 1,
                    "user": user_content,
                    "assistant": ai_content
                })
        
        if not formatted_conversations:
            return {}
        
        # 构建结构化的对话历史文本，便于LLM理解
        formatted_history = []
        for i, turn in enumerate(formatted_conversations):
            turn_number = i + 1
            formatted_turn = f"📝 对话轮次 {turn_number}:\n👤 用户: {turn['user']}\n🤖 助手: {turn['assistant']}"
            formatted_history.append(formatted_turn)
        
        session_history = "\n\n".join(formatted_history)
        
        user_context = {
            "session_history": session_history,
            "conversation_count": len(formatted_conversations),
            "total_historical_turns": len(conversation_turns)
        }
        
        logger.info(f"构建智能用户上下文: 包含{len(formatted_conversations)}轮清晰的对话历史，总共{len(conversation_turns)}轮原始对话")
        
        return user_context

    def _extract_clean_content(self, msg):
        """从消息中提取干净的显示内容"""
        if isinstance(msg, HumanMessage):
            return self._extract_message_text(msg)
        elif isinstance(msg, AIMessage):
            # 使用通用的内容提取逻辑
            content = self._extract_content_text(msg.content)
            
            # 清理AI消息中的特殊标记，但保留有用内容
            clean_content = content
            
            # 移除工具调用标记
            clean_content = re.sub(r'\[TOOL_CALL\].*?\[/TOOL_CALL\]', '', clean_content, flags=re.DOTALL)
            clean_content = re.sub(r'<think>.*?</think>', '', clean_content, flags=re.DOTALL)
            clean_content = re.sub(r'<think>.*', '', clean_content, flags=re.DOTALL)
            clean_content = re.sub(r'.*</think>', '', clean_content, flags=re.DOTALL)
            clean_content = re.sub(r'\[HANDOFF\].*?\[/HANDOFF\]', '', clean_content, flags=re.DOTALL)
            
            # 移除任务完成标记（更全面的匹配）
            clean_content = re.sub(r'\[TASK_COMPLETE\]\s*', '', clean_content)
            clean_content = re.sub(r'\[/TASK_COMPLETE\]\s*', '', clean_content)
            
            # 移除agent格式标记（更灵活的匹配）
            # 匹配 "## name\n任意agent名称\n可能的空格和换行\n## content\n"
            clean_content = re.sub(r'##\s*name\s*\n\s*\w+\s*\n+\s*##\s*content\s*\n+', '', clean_content, flags=re.DOTALL)
            
            # 移除可能残留的单独的 ## name 或 ## content 行
            clean_content = re.sub(r'##\s*name\s*\n', '', clean_content)
            clean_content = re.sub(r'##\s*content\s*\n', '', clean_content)
            
            # 移除agent名称行（如果agent格式标记被部分清理）
            clean_content = re.sub(r'^\s*(knowledge_agent|mcp_agent|multimodal_agent|simple_chat|custom_rag_agent|supervisor)\s*\n', '', clean_content, flags=re.MULTILINE)
            
            # 移除多余的空行和空格
            clean_content = re.sub(r'\n\s*\n', '\n', clean_content)
            clean_content = clean_content.strip()
            
            return clean_content if clean_content else "[无有效内容]"
        else:
            return str(msg.content) if hasattr(msg, 'content') else str(msg)
    
    def _extract_content_text(self, content):
        """通用的内容文本提取方法，处理字符串和列表格式"""
        if isinstance(content, list):
            text_parts = []
            for item in content:
                if isinstance(item, dict) and item.get('type') == 'text':
                    text_parts.append(item.get('text', ''))
                elif isinstance(item, str):
                    text_parts.append(item)
            return " ".join(text_parts)
        elif isinstance(content, str):
            return content
        else:
            return str(content)

    def _extract_message_text(self, msg: HumanMessage) -> str:
        """从消息中提取文本内容，处理多种格式"""
        if isinstance(msg.content, list):
            text_parts = []
            for item in msg.content:
                if isinstance(item, dict) and item.get('type') == 'text':
                    text_parts.append(item.get('text', ''))
                elif isinstance(item, str):
                    text_parts.append(item)
            return " ".join(text_parts)
        else:
            # content是字符串类型
            return str(msg.content)
    
    def _build_agent_config(self, state: TextUnifiedQAState, suffix: str = "") -> Dict[str, Any]:
        """构建Agent配置"""
        thread_id = state.thread_id or "default"
        if suffix:
            thread_id = f"{thread_id}_{suffix}"
        
        return {
            "configurable": {
                "thread_id": thread_id,
                "checkpoint_ns": "text_unified_multi_agent"
            }
        }


def create_text_unified_multi_agent_graph(
    checkpointer: Any = None,
    mcp_router: Any = None,
    mcp_simple_router: Any = None,
    execution_mode: str = "react_mode",
    filter_agent_messages: bool = False,
    include_agent_name: str = "inline",
    enable_global_context_summarization: bool = True
):
    """创建基于文本解析的统一多智能体问答图
    
    Args:
        checkpointer: 检查点保存器
        mcp_router: MCP RAG路由器
        mcp_simple_router: MCP简单路由器
        execution_mode: 执行模式 ("text_parsing", "function_call", 或 "react_mode")
        filter_agent_messages: 是否过滤Agent消息（True=只返回最后一条，False=返回全部）
        include_agent_name: Agent名称包含方式（None=使用message.name，"inline"=内联到内容中）
        enable_global_context_summarization: 是否启用全局上下文总结
        
    Returns:
        StateGraph: 编译好的图实例
    """
    if execution_mode == "function_call":
        from .unified_graph import create_unified_multi_agent_graph
        logger.info("使用function_call模式")
        return create_unified_multi_agent_graph(
            checkpointer=(checkpointer if checkpointer else MemorySaver()),
            mcp_router=mcp_router,
            mcp_simple_router=mcp_simple_router,
            enable_global_context_summarization=enable_global_context_summarization
        )
    else:
        text_unified_graph = TextUnifiedMultiAgentGraph(
            checkpointer=(checkpointer if checkpointer else MemorySaver()),
            mcp_router=mcp_router,
            mcp_simple_router=mcp_simple_router,
            execution_mode=execution_mode,
            filter_agent_messages=filter_agent_messages,
            include_agent_name=include_agent_name,
            enable_global_context_summarization=enable_global_context_summarization
        )

        graph = text_unified_graph.build_graph()
        save_graph_image(graph, "text_unified_multi_agent_graph.mmd")
        logger.info(f"基于文本解析的统一多智能体问答图创建完成，执行模式: {execution_mode}")

        # 将AgentManager附加到graph对象上，以便在服务初始化时访问
        graph.agent_manager = text_unified_graph.agent_manager
        
        return graph

def save_graph_image(app, filename):
    """Save graph visualization as an image file."""
    try:
        mermaid_code = app.get_graph(xray=1).draw_mermaid()
        with open(filename, "w", encoding="utf-8") as f:
            f.write(mermaid_code)
        print(f"工作流Mermaid代码已保存到 {filename}")
        print("请使用以下方式查看图形:")
        print("1. 在线: 访问 https://mermaid.live/ 并粘贴文件内容")
        print("2. VS Code: 安装Mermaid扩展并打开.mmd文件")
        print("3. 其他Mermaid查看器或编辑器")
    except Exception as e:
        print(f"无法保存工作流图: {e}")
