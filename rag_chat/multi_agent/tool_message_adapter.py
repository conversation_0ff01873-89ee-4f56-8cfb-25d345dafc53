"""
ToolMessage 兼容性适配器
当模型不支持ToolMessage时，提供替代方案
"""

import os
import json
from typing import Union, Dict, Any, List, Optional
from langchain_core.messages import BaseMessage, ToolMessage, HumanMessage
from dotenv import load_dotenv

load_dotenv(override=True)
print(os.getenv('ENABLE_TOOLMESSAGE', 'true'))

class ToolMessageAdapter:
    """ToolMessage 适配器，根据配置决定使用ToolMessage还是HumanMessage替代"""
    
    def __init__(self):
        self.enable_toolmessage = os.getenv('ENABLE_TOOLMESSAGE', 'true').lower() == 'true'
    
    def create_tool_message(self, 
                           content: str,
                           tool_name: str,
                           tool_call_id: Optional[str] = None,
                           additional_kwargs: Optional[Dict[str, Any]] = None) -> BaseMessage:
        """
        创建工具消息
        
        Args:
            content: 消息内容
            tool_name: 工具名称
            tool_call_id: 工具调用ID
            additional_kwargs: 额外的参数
            
        Returns:
            BaseMessage: ToolMessage 或 AIMessage
        """
        if self.enable_toolmessage:
            # 使用标准的 ToolMessage
            return ToolMessage(
                content=content,
                name=tool_name,
                tool_call_id=tool_call_id or f"call_{tool_name}",
                additional_kwargs=additional_kwargs or {}
            )
        else:
            # 使用HumanMessage替代，在content中使用Markdown格式
            # 这样做的原因：
            # 1. SystemMessage 通常只有一条，放在对话开头
            # 2. 工具结果可以被视为"用户提供的补充信息"
            # 3. 避免多个SystemMessage破坏对话结构
            structured_content = {
                "type": "tool_result",
                "tool_name": tool_name,
                "tool_call_id": tool_call_id or f"call_{tool_name}",
                "content": content
            }
            
            # 使用Markdown格式，更清晰易读
            formatted_content = f"""## 🔧 工具执行结果

**工具名称:** `{tool_name}`  
**调用ID:** `{tool_call_id or f'call_{tool_name}'}`

### 执行结果
```
{content}
```

---
*这是一个工具执行结果，作为补充信息提供，仅代表当前工具的执行结果*"""
            
            return HumanMessage(
                content=formatted_content,
                additional_kwargs={
                    "tool_message_info": structured_content,
                    "is_tool_result": True,  # 标记这是工具结果而不是真正的用户输入
                    **(additional_kwargs or {})
                }
            )
    
    def create_handoff_message(self, 
                              target_agent: str,
                              task_description: str,
                              current_agent: Optional[str] = None,
                              tool_call_id: Optional[str] = None) -> BaseMessage:
        """
        创建Agent转交消息
        
        Args:
            target_agent: 目标Agent名称
            task_description: 任务描述
            current_agent: 当前Agent名称
            tool_call_id: 工具调用ID
            
        Returns:
            BaseMessage: ToolMessage 或 SystemMessage
        """
        content = f"准备转交给 {target_agent}: {task_description}"
        if current_agent:
            content = f"{current_agent} " + content
        
        # 如果是兼容模式，使用特殊的转交消息格式
        if not self.enable_toolmessage:
            formatted_content = f"""## 🔄 Agent转交

**当前Agent:** `{current_agent or 'Unknown'}`  
**目标Agent:** `{target_agent}`  
**任务描述:** {task_description}

---
*Agent转交操作*"""
            
            return HumanMessage(
                content=formatted_content,
                additional_kwargs={
                    "tool_message_info": {
                        "type": "agent_handoff",
                        "current_agent": current_agent,
                        "target_agent": target_agent,
                        "task_description": task_description,
                        "tool_call_id": tool_call_id or f"handoff_{target_agent}"
                    },
                    "is_tool_result": True
                }
            )
            
        return self.create_tool_message(
            content=content,
            tool_name="agent_handoff",
            tool_call_id=tool_call_id or f"handoff_{target_agent}"
        )
    
    def create_tool_error_message(self,
                                 error_message: str,
                                 tool_name: str,
                                 tool_call_id: Optional[str] = None,
                                 additional_kwargs: Optional[Dict[str, Any]] = None) -> BaseMessage:
        """
        创建工具错误消息
        
        Args:
            error_message: 错误信息
            tool_name: 工具名称
            tool_call_id: 工具调用ID
            additional_kwargs: 额外的参数
            
        Returns:
            BaseMessage: ToolMessage 或 SystemMessage
        """
        content = f"工具调用失败: {error_message}"
        
        # 如果是兼容模式，使用特殊的错误消息格式
        if not self.enable_toolmessage:
            formatted_content = f"""## ❌ 工具执行错误

**工具名称:** `{tool_name}`  
**调用ID:** `{tool_call_id or f'call_{tool_name}_error'}`

### 错误信息
```
{error_message}
```

---
*工具执行失败*"""
            
            combined_kwargs = {
                "tool_message_info": {
                    "type": "tool_error",
                    "tool_name": tool_name,
                    "tool_call_id": tool_call_id or f"call_{tool_name}_error",
                    "error_message": error_message
                },
                "is_tool_result": True
            }
            if additional_kwargs:
                combined_kwargs.update(additional_kwargs)
                
            return HumanMessage(
                content=formatted_content,
                additional_kwargs=combined_kwargs
            )
        
        return self.create_tool_message(
            content=content,
            tool_name=tool_name,
            tool_call_id=tool_call_id or f"call_{tool_name}_error",
            additional_kwargs=additional_kwargs
        )
    
    def create_supervisor_message(self,
                                 agent_name: str,
                                 action: str,
                                 description: str,
                                 tool_call_id: Optional[str] = None) -> BaseMessage:
        """
        创建监督者消息
        
        Args:
            agent_name: Agent名称
            action: 动作类型 (start, complete, etc.)
            description: 描述
            tool_call_id: 工具调用ID
            
        Returns:
            BaseMessage: ToolMessage 或 HumanMessage (根据action类型决定格式)
        """
        if action == "start":
            # 开始动作使用工具调用格式，因为这确实是一个工具调用
            content = f"监督者正在调用 {agent_name}: {description}"
            return self.create_tool_message(
                content=content,
                tool_name=f"transfer_to_{agent_name}",
                tool_call_id=tool_call_id or f"supervisor_{action}_{agent_name}"
            )
        elif action == "complete":
            # 完成动作使用独特的状态报告格式，这是给监督者的反馈信息
            return self._create_agent_status_report(
                agent_name=agent_name,
                status="执行完成",
                description=description,
                tool_call_id=tool_call_id or f"supervisor_{action}_{agent_name}"
            )
        else:
            # 其他动作也使用状态报告格式
            return self._create_agent_status_report(
                agent_name=agent_name,
                status=action,
                description=description,
                tool_call_id=tool_call_id or f"supervisor_{action}_{agent_name}"
            )
    
    def _create_agent_status_report(self,
                                   agent_name: str,
                                   status: str,
                                   description: str,
                                   tool_call_id: str) -> BaseMessage:
        """
        创建Agent状态报告消息（给监督者的反馈信息）
        
        这种消息的特点：
        1. 不是工具调用结果，而是Agent执行状态的汇报
        2. 是给监督者的信息，帮助监督者了解子Agent的执行情况
        3. 应该使用不同于工具调用的格式，避免LLM混淆
        
        Args:
            agent_name: Agent名称
            status: 状态描述
            description: 详细描述
            tool_call_id: 调用ID
            
        Returns:
            BaseMessage: 格式化的状态报告消息
        """
        if self.enable_toolmessage:
            # 使用ToolMessage但使用特殊的name前缀来区分
            content = f"[状态报告] {agent_name}: {description}"
            return ToolMessage(
                content=content,
                name=f"agent_status_report",  # 使用特殊的name，区别于工具调用
                tool_call_id=tool_call_id,
                additional_kwargs={
                    "message_type": "agent_status_report",
                    "agent_name": agent_name,
                    "status": status
                }
            )
        else:
            # 兼容模式：使用独特的状态报告格式
            formatted_content = f"""## 📋 Agent执行报告

**Agent:** {agent_name}  
**状态:** {status}  

---
*这是Agent执行状态的汇报信息，用于监督者了解子Agent的执行情况*"""
            
            return HumanMessage(
                content=formatted_content,
                additional_kwargs={
                    "tool_message_info": {
                        "type": "agent_status_report",
                        "agent_name": agent_name,
                        "status": status,
                        "description": description,
                        "tool_call_id": tool_call_id
                    },
                    "is_tool_result": True,
                    "message_type": "agent_status_report"
                }
            )
    
    def create_swarm_message(self,
                            current_agent: str,
                            next_agent: str,
                            task: str,
                            tool_call_id: Optional[str] = None) -> BaseMessage:
        """
        创建Swarm转交消息
        
        Args:
            current_agent: 当前Agent
            next_agent: 下一个Agent
            task: 任务描述
            tool_call_id: 工具调用ID
            
        Returns:
            BaseMessage: ToolMessage 或 AIMessage
        """
        content = f"Swarm转交: {current_agent} -> {next_agent}, 任务: {task}"
        
        return self.create_tool_message(
            content=content,
            tool_name=f"handoff_to_{next_agent}",
            tool_call_id=tool_call_id or f"swarm_handoff_{next_agent}"
        )
    
    def is_tool_message(self, message: BaseMessage) -> bool:
        """
        判断消息是否是工具消息
        
        Args:
            message: 消息对象
            
        Returns:
            bool: 是否是工具消息
        """
        if isinstance(message, ToolMessage):
            return True
        elif isinstance(message, HumanMessage):
            return message.additional_kwargs.get("is_tool_result", False)
        return False
    
    def get_compatibility_status(self) -> Dict[str, Any]:
        """
        获取兼容性状态信息
        
        Returns:
            Dict[str, Any]: 状态信息
        """
        return {
            "enable_toolmessage": self.enable_toolmessage,
            "compatibility_mode": not self.enable_toolmessage,
            "message_type": "ToolMessage" if self.enable_toolmessage else "HumanMessage",
            "format": "Standard" if self.enable_toolmessage else "Markdown",
            "reasoning": "HumanMessage避免多个SystemMessage，符合对话结构最佳实践"
        }


# 全局实例
tool_message_adapter = ToolMessageAdapter()