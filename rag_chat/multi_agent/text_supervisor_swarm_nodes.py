"""
文本统一图的supervisor和swarm节点实现

基于LangGraph官方API风格的文本模式supervisor和swarm节点。
"""

import asyncio
from typing import Any, Dict, List, Optional
from langchain_core.messages import AIMessage
from langchain_openai import ChatOpenAI

from .text_supervisor_swarm import create_text_supervisor, create_text_swarm, create_text_handoff_tool
from .agents.text_simple_chat_agent import create_text_simple_chat_agent
from .agents.text_knowledge_agent import create_text_knowledge_agent
from .agents.text_mcp_agent import create_text_mcp_agent
from .agents.text_multimodal_agent import create_text_multimodal_agent
# ReAct Agent导入
from .agents.text_knowledge_agent_react import create_text_knowledge_agent_react
from .agents.text_mcp_agent_react import create_text_mcp_agent_react
from .agents.text_multimodal_agent_react import create_text_multimodal_agent_react
from .agents.text_custom_rag_agent_react import create_text_custom_rag_agent_react
# 导入优化的缓存工具
from .cache_utils import _create_llm

import sys
import os
sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))
from logger import logger
from jiliang_chat_prompt import SUPERVISOR_AGENT_PROMPT


class TextSupervisorSwarmNodes:
    """文本模式的supervisor和swarm节点实现"""
    
    def __init__(self, 
                 mcp_router: Any = None,
                 mcp_simple_router: Any = None,
                 checkpointer: Any = None,
                 agent_manager: Any = None):
        """初始化节点处理器
        
        Args:
            mcp_router: MCP RAG路由器
            mcp_simple_router: MCP简单路由器
            checkpointer: 检查点保存器
            agent_manager: Agent管理器（用于复用预构建Agent）
        """
        self.mcp_router = mcp_router
        self.mcp_simple_router = mcp_simple_router
        self.checkpointer = checkpointer
        self.agent_manager = agent_manager
        
    
    async def text_supervisor_node(self, state: Any) -> Any:
        """文本模式监督者协调节点"""
        
        try:
            logger.info("执行文本模式监督者协调")
            
            # 监督者模式：使用优化的Agent创建（可复用预构建Agent，因为不需要handoff_tools）
            agents = []
            
            # 主要Agent
            primary_agent = await self._get_optimized_agent_for_supervisor(state.primary_agent, state)
            agents.append(primary_agent)
            
            # 次要Agent
            for agent_type in state.secondary_agents:
                if agent_type != state.primary_agent:
                    agent = await self._get_optimized_agent_for_supervisor(agent_type, state)
                    agents.append(agent)
            
            # 使用优化的LLM创建（复用连接池）
            supervisor_model = os.getenv("SUPERVISOR_MODEL", "qwq32b")
            llm = _create_llm(extra_headers=state.extra_headers, model_name=supervisor_model, is_reasoning=True, agent_name="supervisor")
            supervisor_prompt = SUPERVISOR_AGENT_PROMPT.format(model_desc=state.model_desc)
            
            supervisor_workflow = create_text_supervisor(
                agents=agents,
                model=llm,
                prompt=supervisor_prompt,
                output_mode="last_message",
                include_agent_name=getattr(state, 'include_agent_name', "inline"),  # 使用状态中的设置
                filter_agent_messages=getattr(state, 'filter_agent_messages', False)
            )
            
            # 编译并执行
            supervisor_graph = supervisor_workflow.compile(checkpointer=self.checkpointer)
            
            config = self._build_agent_config(state, "text_supervisor")
            result = await supervisor_graph.ainvoke(
                {"messages": state.messages},
                config=config
            )
            
            # 更新状态
            if result and "messages" in result:
                new_messages = result.get("messages", [])
                # 只添加新的消息，避免重复
                existing_count = len(state.messages)
                if len(new_messages) > existing_count:
                    to_add = new_messages[existing_count:]
                else:
                    to_add = new_messages
                
                # 根据过滤设置决定要添加的消息
                if getattr(state, 'filter_agent_messages', False):
                    # 只返回最后一条AI消息（类似LangGraph的last_message模式）
                    filtered_messages = []
                    for msg in reversed(to_add):
                        if hasattr(msg, 'content') and isinstance(msg, AIMessage):
                            filtered_messages = [msg]
                            break
                    state.messages.extend(filtered_messages)
                    logger.info(f"Supervisor消息过滤：从{len(to_add)}条消息过滤到{len(filtered_messages)}条")
                else:
                    # 返回全部执行流程
                    state.messages.extend(to_add)
            
            state.agent_results["text_supervisor"] = result
            state.execution_status = "completed"
            
            logger.info("文本模式监督者协调完成")
            return state
            
        except Exception as e:
            logger.error(f"文本模式监督者协调失败: {e}")
            error_msg = AIMessage(content=f"监督者协调时遇到问题：{str(e)}")
            state.messages.append(error_msg)
            state.execution_status = "failed"
            return state
    
    async def text_parallel_coordinator_node(self, state: Any) -> Any:
        """文本模式并行协调节点（基于Swarm）"""
        
        try:
            logger.info("执行文本模式Swarm智能并行协调")
            
            # 准备需要协调的Agent列表
            agents_to_run = [state.primary_agent] + state.secondary_agents
            swarm_agents = []
            
            # 创建Agent并添加handoff工具
            for agent_type in agents_to_run:
                # 创建handoff工具列表，允许Agent相互协调
                handoff_tools = []
                for other_agent in agents_to_run:
                    if other_agent != agent_type:
                        handoff_tool = create_text_handoff_tool(
                            agent_name=other_agent,
                            description=f"将任务转交给{self._get_agent_description(other_agent)}处理相关问题"
                        )
                        handoff_tools.append(handoff_tool)
                
                # Swarm模式：必须动态创建Agent（因为需要动态的handoff_tools，无法使用预缓存）
                enhanced_agent = await self._create_text_agent(
                    agent_type, state, handoff_tools
                )
                swarm_agents.append(enhanced_agent)
            
            # 创建文本模式Swarm工作流
            swarm_workflow = create_text_swarm(
                agents=swarm_agents,
                default_active_agent=state.primary_agent,
                filter_agent_messages=getattr(state, 'filter_agent_messages', False),
                include_agent_name=getattr(state, 'include_agent_name', "inline")  # 默认使用inline模式
            )
            
            # 编译Swarm图，支持checkpointer
            swarm_graph = swarm_workflow.compile(checkpointer=self.checkpointer)
            
            # 执行Swarm协调
            config = self._build_agent_config(state, "text_swarm")
            result = await swarm_graph.ainvoke(
                {"messages": state.messages},
                config=config
            )
            
            # 更新状态
            new_messages = result.get("messages", [])
            
            # 根据过滤设置决定要添加的消息
            if getattr(state, 'filter_agent_messages', False):
                # 只返回最后一条AI消息（类似LangGraph的last_message模式）
                filtered_messages = []
                for msg in reversed(new_messages):
                    if hasattr(msg, 'content') and isinstance(msg, AIMessage):
                        filtered_messages = [msg]
                        break
                state.messages.extend(filtered_messages)
                logger.info(f"Swarm消息过滤：从{len(new_messages)}条消息过滤到{len(filtered_messages)}条")
            else:
                # 返回全部执行流程
                state.messages.extend(new_messages)
            
            state.agent_results["text_swarm_coordinator"] = result
            state.execution_status = "completed"
            
            logger.info(f"文本模式Swarm智能协调完成，协调了{len(swarm_agents)}个Agent")
            return state
            
        except Exception as e:
            logger.error(f"文本模式Swarm智能协调失败: {e}")
            error_msg = AIMessage(content=f"Swarm智能协调时遇到问题：{str(e)}")
            state.messages.append(error_msg)
            state.execution_status = "failed"
            return state
    
    async def _get_optimized_agent_for_supervisor(self, agent_type: str, state: Any):
        """为监督者模式获取优化的Agent实例（监督者模式不需要handoff_tools）"""
        if not self.agent_manager:
            # 降级：如果没有AgentManager，使用原有方式
            return await self._create_text_agent(agent_type, state, [])
        
        # 等待AgentManager初始化完成
        if not self.agent_manager.initialized:
            await self.agent_manager.initialize()
        
        try:
            # 监督者模式可以使用预构建Agent（无handoff_tools）
            if agent_type == "simple_chat":
                return await self.agent_manager.get_simple_chat_agent(
                    model_desc=state.model_desc,
                    handoff_tools=[]  # 监督者模式不需要handoff_tools
                )
            elif agent_type == "knowledge_agent":
                return await self.agent_manager.get_knowledge_agent(
                    model_desc=state.model_desc,
                    handoff_tools=[],  # 监督者模式不需要handoff_tools
                    max_iterations=getattr(state, 'max_iterations', 20)
                )
            elif agent_type in ["mcp_agent", "multimodal_agent", "custom_rag_agent"]:
                # 这些Agent参数动态性很大，使用动态创建
                return await self._create_text_agent(agent_type, state, [])
            else:
                # 未知Agent类型，使用动态创建
                return await self._create_text_agent(agent_type, state, [])
                
        except Exception as e:
            logger.warning(f"使用预构建Agent失败，降级到动态创建: {e}")
            return await self._create_text_agent(agent_type, state, [])
    
    async def _create_text_agent(self, agent_type: str, 
                                 state: Any, 
                                 handoff_tools: Optional[List[Any]] = None) -> Any:
        """创建文本模式的单个Agent实例（支持ReAct模式）"""
        
        # 根据agent类型选择模型
        if agent_type in ["knowledge_agent", "custom_rag_agent"]:
            model_name = os.getenv("KNOWLEDGE_MODEL", "qwen2---5-72b-goxmbugy")
        elif agent_type == "mcp_agent":
            model_name = os.getenv("TOOL_MODEL", "qwen2---5-72b-goxmbugy")
        elif agent_type == "multimodal_agent":
            model_name = os.getenv("MULTIMODAL_MODEL", "qwen2-5-vl-72b")
        else:
            model_name = os.getenv("CHAT_MODEL", "qwen2---5-72b-goxmbugy")

        llm = _create_llm(extra_headers=state.extra_headers, model_name=model_name, agent_name=agent_type)
        
        if handoff_tools is None:
            handoff_tools = []
        
        # 检查是否使用ReAct模式
        use_react_mode = getattr(state, 'execution_mode', 'text_parsing') == 'react_mode' and getattr(state, 'use_react_agents', True)
        max_iterations = getattr(state, 'max_iterations', 10)
        
        if agent_type == "knowledge_agent":
            if use_react_mode:
                agent = await create_text_knowledge_agent_react(
                    llm=llm,
                    model_desc=state.model_desc,
                    handoff_tools=handoff_tools,
                    max_iterations=max_iterations,
                    enable_context_summarization=True,
                    checkpointer=self.checkpointer
                )
            else:
                agent = await create_text_knowledge_agent(
                    llm=llm,
                    model_desc=state.model_desc,
                    handoff_tools=handoff_tools,
                    checkpointer=self.checkpointer
                )
            agent.name = "knowledge_agent"  # 设置名称用于映射
            return agent
        
        elif agent_type == "simple_chat":
            # 简单聊天不需要ReAct模式
            agent = await create_text_simple_chat_agent(
                llm=llm,
                model_desc=state.model_desc,
                handoff_tools=handoff_tools,
                checkpointer=self.checkpointer,
                enable_context_summarization=True
            )
            agent.name = "simple_chat"  # 设置名称用于映射
            return agent
            
        elif agent_type == "mcp_agent":
            if use_react_mode:
                agent = await create_text_mcp_agent_react(
                    llm=llm,
                    question=state.current_question,
                    mcp_ids=state.mcp_ids,
                    model_desc=state.model_desc,
                    mcp_router=self.mcp_router,
                    mcp_simple_router=self.mcp_simple_router,
                    handoff_tools=handoff_tools,
                    max_iterations=max_iterations,
                    enable_context_summarization=True,
                    checkpointer=self.checkpointer,
                    api_key=getattr(state.extra_headers, 'api_key', None),
                    username=getattr(state.extra_headers, 'username', None)
                )
            else:
                agent = await create_text_mcp_agent(
                    llm=llm,
                    question=state.current_question,
                    mcp_ids=state.mcp_ids,
                    model_desc=state.model_desc,
                    mcp_router=self.mcp_router,
                    mcp_simple_router=self.mcp_simple_router,
                    handoff_tools=handoff_tools,
                    checkpointer=self.checkpointer,
                    api_key=getattr(state.extra_headers, 'api_key', None),
                    username=getattr(state.extra_headers, 'username', None)
                )
            agent.name = "mcp_agent"  # 设置名称用于映射
            return agent
            
        elif agent_type == "multimodal_agent":
            if use_react_mode:
                agent = await create_text_multimodal_agent_react(
                    llm=llm,
                    model_desc=state.model_desc,
                    handoff_tools=handoff_tools,
                    max_iterations=max_iterations,
                    enable_context_summarization=True,
                    checkpointer=self.checkpointer,
                    uploaded_files=state.file_uploads
                )
            else:
                agent = await create_text_multimodal_agent(
                    llm=llm,
                    model_desc=state.model_desc,
                    handoff_tools=handoff_tools,
                    checkpointer=self.checkpointer
                )
            agent.name = "multimodal_agent"  # 设置名称用于映射
            return agent
            
        elif agent_type == "custom_rag_agent":
            if use_react_mode:
                agent = await create_text_custom_rag_agent_react(
                    llm=llm,
                    custom_rag_ids=state.extral_rag_ids or [],
                    model_desc=state.model_desc,
                    handoff_tools=handoff_tools,
                    max_iterations=max_iterations,
                    enable_context_summarization=True,
                    checkpointer=self.checkpointer
                )
            agent.name = "custom_rag_agent"  # 设置名称用于映射
            return agent
    
        else:
            raise ValueError(f"未知的文本模式Agent类型: {agent_type}")
    
    def _get_agent_description(self, agent_type: str) -> str:
        """获取Agent的描述信息"""
        descriptions = {
            "simple_chat": "简单问答助手",
            "knowledge_agent": "三峡集团知识专家",
            "mcp_agent": "外部工具专家",
            "multimodal_agent": "多模态处理专家",
            "custom_rag_agent": "自定义知识库专家"
        }
        return descriptions.get(agent_type, agent_type)
    
    def _build_agent_config(self, state: Any, suffix: str = "") -> Dict[str, Any]:
        """构建Agent配置"""
        thread_id = state.thread_id or "default"
        if suffix:
            thread_id = f"{thread_id}_{suffix}"
        
        return {
            "configurable": {
                "thread_id": thread_id,
                "checkpoint_ns": "text_unified_multi_agent"
            }
        }