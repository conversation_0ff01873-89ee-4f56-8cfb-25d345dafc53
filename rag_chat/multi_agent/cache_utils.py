import sys
import os
from typing import Dict, Any, List, Optional, Literal, Annotated
import time

from .intent_analyzer import create_intent_analyzer
from .agents.text_simple_chat_agent import create_text_simple_chat_agent
from .agents.text_knowledge_agent import create_text_knowledge_agent
from .agents.text_mcp_agent import create_text_mcp_agent
from .agents.text_multimodal_agent import create_text_multimodal_agent
# ReAct Agent导入
from .agents.text_knowledge_agent_react import create_text_knowledge_agent_react
from .agents.text_mcp_agent_react import create_text_mcp_agent_react
from .agents.text_multimodal_agent_react import create_text_multimodal_agent_react
from .agents.text_custom_rag_agent_react import create_text_custom_rag_agent_react

# 导入token统计功能
sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))
from logger import logger
from token_usage_tracker import create_token_callback
# 导入LLM连接池
from llm_pool import get_llm_from_pool

def _create_llm(temperature: float = 0.6, 
                extra_headers: Optional[Dict[str, Any]] = None,
                model_name: str = "",
                is_reasoning: bool = False,
                agent_name: str = "unknown"):
        """使用连接池创建LLM实例，优化资源利用"""
        if extra_headers is None:
            extra_headers = {}
        
        # 确定最终的模型名称    
        final_model_name = model_name if model_name else str(os.getenv("OPENAI_MODEL"))
        
        # 创建token统计回调
        token_callback = create_token_callback(agent_name, final_model_name)
        logger.debug(f"为{agent_name}创建token回调，模型: {final_model_name}")
        
        # 准备回调列表
        callbacks = [token_callback]
        
        # 使用连接池获取LLM实例
        llm = get_llm_from_pool(
            model_name=final_model_name,
            temperature=temperature,
            extra_headers=extra_headers,
            is_reasoning=is_reasoning,
            agent_name=agent_name,
            callbacks=callbacks
        )
        
        logger.debug(f"从连接池获取LLM实例: {agent_name} -> {final_model_name}")
        return llm


class AgentManager:
    """Agent管理器：负责预创建和缓存agent实例，避免重复初始化"""

    def __init__(self,
                 checkpointer: Any = None,
                 mcp_router: Any = None,
                 mcp_simple_router: Any = None,
                 execution_mode: str = "react_mode"):
        """初始化Agent管理器

        Args:
            checkpointer: 检查点保存器
            mcp_router: MCP RAG路由器
            mcp_simple_router: MCP简单路由器
            execution_mode: 执行模式
        """
        self.checkpointer = checkpointer
        self.mcp_router = mcp_router
        self.mcp_simple_router = mcp_simple_router
        self.execution_mode = execution_mode

        # 静态agent缓存（参数变化少，可以预创建）
        self.static_agents = {}
        
        # 初始化标志
        self.initialized = False

        logger.info("AgentManager初始化完成")
    
    async def initialize(self):
        """预初始化所有固定参数的Agent实例"""
        if self.initialized:
            return
            
        try:
            logger.info("开始预初始化Agent实例...")
            start_time = time.time()
            
            # 直接使用self._create_llm（已经有连接池优化）
            intent_llm = _create_llm(temperature=0.1, model_name=os.getenv("INTENT_MODEL", "qwen2---5-72b-goxmbugy"), agent_name="intent_analyzer")
            chat_llm = _create_llm(temperature=0.7, model_name=os.getenv("CHAT_MODEL", "qwen2---5-72b-goxmbugy"), agent_name="simple_chat")
            knowledge_llm = _create_llm(temperature=0.6, model_name=os.getenv("KNOWLEDGE_MODEL", "qwen2---5-72b-goxmbugy"), agent_name="knowledge_agent")
            
            # 预创建关键Agent实例
            # 1. 意图分析器（参数完全固定）
            intent_analyzer = await create_intent_analyzer(intent_llm)
            intent_analyzer.name = "intent_analyzer"  # 设置名称用于映射
            self.static_agents["intent_analyzer"] = intent_analyzer
            
            # 2. 简单聊天Agent（基础版本）
            simple_chat_agent = await create_text_simple_chat_agent(
                llm=chat_llm,
                model_desc="",  # 大多数情况下为空
                handoff_tools=[],  # 大多数情况下为空
                enable_context_summarization=True,
                checkpointer=self.checkpointer
            )
            simple_chat_agent.name = "simple_chat"  # 设置名称用于映射
            self.static_agents["simple_chat_base"] = simple_chat_agent
            
            # 3. 知识Agent（预创建ReAct和传统两个版本）
            if self.execution_mode == "react_mode":
                knowledge_react_agent = await create_text_knowledge_agent_react(
                    llm=knowledge_llm,
                    model_desc="",
                    handoff_tools=[],
                    max_iterations=20,  # 默认值
                    enable_context_summarization=True,
                    checkpointer=self.checkpointer
                )
                knowledge_react_agent.name = "knowledge_agent"  # 设置名称用于映射
                self.static_agents["knowledge_react"] = knowledge_react_agent
            else:
                knowledge_traditional_agent = await create_text_knowledge_agent(
                    llm=knowledge_llm,
                    model_desc="",
                    handoff_tools=[],
                    enable_context_summarization=True,
                    checkpointer=self.checkpointer
                )
                knowledge_traditional_agent.name = "knowledge_agent"  # 设置名称用于映射
                self.static_agents["knowledge_traditional"] = knowledge_traditional_agent
            
            self.initialized = True
            init_time = time.time() - start_time
            logger.info(f"Agent预初始化完成，耗时: {init_time:.2f}秒，预创建{len(self.static_agents)}个Agent")
            
        except Exception as e:
            logger.error(f"Agent预初始化失败: {e}")
            # 不抛出异常，允许降级到运行时创建

    async def get_intent_analyzer(self):
        """获取意图分析器（预缓存，参数完全固定）"""
        if "intent_analyzer" in self.static_agents:
            return self.static_agents["intent_analyzer"]
        
        # 降级：运行时创建
        logger.warning("意图分析器未预缓存，运行时创建")
        intent_llm = _create_llm(temperature=0.1, model_name=os.getenv("INTENT_MODEL", "qwen2---5-72b-goxmbugy"), agent_name="intent_analyzer")
        return await create_intent_analyzer(intent_llm)
    
    async def get_simple_chat_agent(self, llm=None, model_desc: str = "", handoff_tools: Optional[List[Any]] = None):
        """获取简单聊天agent（优化版：大多数情况使用预缓存）"""
        # 99%的情况下model_desc为空，handoff_tools为空，直接使用预缓存
        if not handoff_tools:
            if "simple_chat_base" in self.static_agents:
                return self.static_agents["simple_chat_base"]
        
        # 特殊情况：有自定义参数，动态创建（但很少发生）
        logger.debug(f"动态创建简单聊天agent，model_desc: {model_desc}, tools: {len(handoff_tools or [])}")
        used_llm = llm or _create_llm(temperature=0.7, model_name=os.getenv("CHAT_MODEL", "qwen2---5-72b-goxmbugy"), agent_name="simple_chat")
        agent = await create_text_simple_chat_agent(
            llm=used_llm,
            model_desc=model_desc,
            handoff_tools=handoff_tools or [],
            enable_context_summarization=True,
            checkpointer=self.checkpointer
        )
        agent.name = "simple_chat"  # 设置名称用于映射
        return agent

    async def get_knowledge_agent(self, llm=None, model_desc: str = "", handoff_tools: Optional[List[Any]] = None, max_iterations: int = 20):
        """获取知识agent（优化版：优先使用预缓存）"""
        # 检查是否可以使用预缓存（大多数情况下参数都是默认值）
        if not handoff_tools and max_iterations == 20:
            if self.execution_mode == "react_mode" and "knowledge_react" in self.static_agents:
                return self.static_agents["knowledge_react"]
            elif self.execution_mode != "react_mode" and "knowledge_traditional" in self.static_agents:
                return self.static_agents["knowledge_traditional"]
        
        # 特殊情况：有自定义参数，动态创建
        logger.debug(f"动态创建知识agent，mode: {self.execution_mode}, desc: {model_desc}, iterations: {max_iterations}")
        used_llm = llm or _create_llm(temperature=0.6, model_name=os.getenv("KNOWLEDGE_MODEL", "qwen2---5-72b-goxmbugy"), agent_name="knowledge_agent")
        
        if self.execution_mode == "react_mode":
            agent = await create_text_knowledge_agent_react(
                llm=used_llm,
                model_desc=model_desc,
                handoff_tools=handoff_tools or [],
                max_iterations=max_iterations,
                enable_context_summarization=True,
                checkpointer=self.checkpointer
            )
            agent.name = "knowledge_agent"  # 设置名称用于映射
            return agent
        else:
            agent = await create_text_knowledge_agent(
                llm=used_llm,
                model_desc=model_desc,
                handoff_tools=handoff_tools or [],
                enable_context_summarization=True,
                checkpointer=self.checkpointer
            )
            agent.name = "knowledge_agent"  # 设置名称用于映射
            return agent
    

    async def get_mcp_agent(self, llm, question: str, mcp_ids: List[str], model_desc: str,
                           handoff_tools: Optional[List[Any]] = None, max_iterations: int = 20,
                           api_key: Optional[str] = None, username: Optional[str] = None):
        """获取MCP agent（动态创建，但使用优化的工厂）"""
        # MCP agent参数变化较大，使用快速创建而非缓存
        logger.debug(f"快速创建MCP agent，mcp_ids: {mcp_ids}")

        if self.execution_mode == "react_mode":
            agent = await create_text_mcp_agent_react(
                llm=llm,
                question=question,
                mcp_ids=mcp_ids,
                model_desc=model_desc,
                mcp_router=self.mcp_router,
                mcp_simple_router=self.mcp_simple_router,
                handoff_tools=handoff_tools or [],
                max_iterations=max_iterations,
                enable_context_summarization=True,
                checkpointer=self.checkpointer,
                api_key=api_key or "",
                username=username or ""
            )
            agent.name = "mcp_agent"  # 设置名称用于映射
            return agent
        else:
            agent = await create_text_mcp_agent(
                llm=llm,
                question=question,
                mcp_ids=mcp_ids,
                model_desc=model_desc,
                mcp_router=self.mcp_router,
                mcp_simple_router=self.mcp_simple_router,
                handoff_tools=handoff_tools or [],
                checkpointer=self.checkpointer,
                api_key=api_key or "",
                username=username or ""
            )
            agent.name = "mcp_agent"  # 设置名称用于映射
            return agent

    async def get_multimodal_agent(self, llm, model_desc: str, handoff_tools: Optional[List[Any]] = None,
                                  max_iterations: int = 20, uploaded_files: Optional[List] = None):
        """获取多模态agent"""
        # 多模态agent由于uploaded_files经常变化，使用快速创建
        logger.debug(f"快速创建多模态agent，文件数: {len(uploaded_files or [])}")

        if self.execution_mode == "react_mode":
            agent = await create_text_multimodal_agent_react(
                llm=llm,
                model_desc=model_desc,
                handoff_tools=handoff_tools or [],
                max_iterations=max_iterations,
                enable_context_summarization=True,
                checkpointer=self.checkpointer,
                uploaded_files=uploaded_files
            )
            agent.name = "multimodal_agent"  # 设置名称用于映射
            return agent
        else:
            agent = await create_text_multimodal_agent(
                llm=llm,
                model_desc=model_desc,
                handoff_tools=handoff_tools or [],
                enable_context_summarization=True,
                checkpointer=self.checkpointer
            )
            agent.name = "multimodal_agent"  # 设置名称用于映射
            return agent

    async def get_custom_rag_agent(self, llm, custom_rag_ids: List[str], model_desc: str,
                                  handoff_tools: Optional[List[Any]] = None, max_iterations: int = 20):
        """获取自定义RAG agent"""
        # 自定义RAG agent由于custom_rag_ids可能变化，使用快速创建
        logger.debug(f"快速创建自定义RAG agent，rag_ids: {custom_rag_ids}")

        agent = await create_text_custom_rag_agent_react(
            llm=llm,
            custom_rag_ids=custom_rag_ids,
            model_desc=model_desc,
            handoff_tools=handoff_tools or [],
            max_iterations=max_iterations,
            enable_context_summarization=True,
            checkpointer=self.checkpointer
        )
        agent.name = "custom_rag_agent"  # 设置名称用于映射
        return agent