"""
文本模式MCP工具专家Agent

基于文本解析的MCP工具专家Agent，专门处理外部工具调用和复杂计算任务，不依赖function calling。
"""

import os
import sys
from typing import Any, Dict, Optional, List
from langchain_openai import ChatOpenAI
from langchain_core.messages import SystemMessage, HumanMessage, AIMessage
from langchain_core.output_parsers import StrOutputParser
from langchain_core.prompts import ChatPromptTemplate
import json

sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))
from logger import logger
from jiliang_chat_prompt import MCP_AGENT_PROMPT
from jiliang_chat_tools import search_knowledge_base
from ..text_parser import text_parser, ParsedActionType, ToolCall


class TextMCPAgent:
    """基于文本解析的MCP工具专家Agent"""
    
    def __init__(self, 
                 llm: ChatOpenAI,
                 question: str,
                 mcp_ids: List[str],
                 model_desc: str = "DeepSeek",
                 mcp_router: Any = None,
                 mcp_simple_router: Any = None,
                 handoff_tools: List[Any] = None,
                 api_key: str = None,
                 username: str = None):
        """初始化文本模式MCP工具专家Agent
        
        Args:
            llm: ChatOpenAI模型实例
            question: 用户问题（用于动态工具选择）
            mcp_ids: MCP工具ID列表
            model_desc: 模型描述
            mcp_router: MCP RAG路由器
            mcp_simple_router: MCP简单路由器
            handoff_tools: handoff工具列表（用于Swarm模式）
            api_key: API密钥（从请求头获取）
            username: 用户名（从请求头获取）
        """
        self.llm = llm
        self.question = question
        self.mcp_ids = mcp_ids
        self.model_desc = model_desc
        self.mcp_router = mcp_router
        self.mcp_simple_router = mcp_simple_router
        self.handoff_tools = handoff_tools or []
        self.mcp_tools = []
        self.api_key = api_key
        self.username = username
        
        # 构建系统提示词
        self.system_prompt = MCP_AGENT_PROMPT.format(model_desc=model_desc)
        
        logger.info(f"文本模式MCP工具专家Agent初始化开始，MCP IDs: {mcp_ids}")
    
    async def _initialize_tools(self):
        """异步初始化MCP工具"""
        try:
            # 获取MCP工具
            self.mcp_tools = await self._get_mcp_tools()
            
            # 构建工具使用指令
            tool_instructions = []
            
            # 添加基础搜索工具指令
            search_instruction = text_parser.format_tool_call_prompt(
                tool_name="search_knowledge_base",
                description="搜索三峡集团知识库以获取相关信息",
                args_schema={"query": "搜索查询词", "max_results": 5}
            )
            tool_instructions.append(search_instruction)
            
            # 添加MCP工具指令
            for tool in self.mcp_tools:
                try:
                    tool_name = getattr(tool, 'name', str(tool))
                    tool_desc = getattr(tool, 'description', f"MCP工具: {tool_name}")
                    # 简化的参数模式
                    args_schema = {"input": "工具输入参数"}
                    
                    mcp_instruction = text_parser.format_tool_call_prompt(
                        tool_name=tool_name,
                        description=tool_desc,
                        args_schema=args_schema
                    )
                    tool_instructions.append(mcp_instruction)
                except Exception as e:
                    logger.warning(f"处理MCP工具 {tool} 时出错: {e}")
            
            # 合并工具指令到系统提示词
            if tool_instructions:
                self.system_prompt += "\n\n可用工具:\n" + "\n\n".join(tool_instructions)
            
            # 如果有handoff工具，添加相关指令
            if self.handoff_tools:
                available_agents = [tool.name.replace("transfer_to_", "") for tool in self.handoff_tools]
                handoff_instruction = text_parser.format_handoff_prompt(available_agents)
                self.system_prompt += "\n\n" + handoff_instruction
            
            # 创建聊天模板
            self.prompt_template = ChatPromptTemplate.from_messages([
                ("system", self.system_prompt),
                ("placeholder", "{messages}")
            ])
            
            # 创建处理链
            self.chain = self.prompt_template | self.llm | StrOutputParser()
            
            logger.info(f"文本模式MCP Agent工具初始化完成，{len(self.mcp_tools)}个MCP工具，{len(self.handoff_tools)}个handoff工具")
            
        except Exception as e:
            logger.error(f"MCP工具初始化失败: {e}")
            # 创建基础的处理链
            self.prompt_template = ChatPromptTemplate.from_messages([
                ("system", self.system_prompt),
                ("placeholder", "{messages}")
            ])
            self.chain = self.prompt_template | self.llm | StrOutputParser()
    
    async def _get_mcp_tools(self) -> List[Any]:
        """获取MCP工具列表"""
        mcp_tools = []
        
        try:
            # 获取MCP路由器类型
            mcp_router_type = os.getenv('MCP_ROUTER_TYPE', 'rag').lower()
            
            if mcp_router_type == 'rag' and self.mcp_router:
                # 使用RAG路由器动态选择工具
                mcp_tools = await self.mcp_router.get_dynamic_mcp_tools(self.question, top_k=2)
                logger.info(f"RAG路由器为问题 '{self.question[:50]}...' 动态加载了 {len(mcp_tools)} 个MCP工具")
                
            elif mcp_router_type == 'simple' and self.mcp_simple_router:
                # 使用简单路由器
                if self.mcp_ids:
                    # 从状态中获取API密钥和用户名（如果可用）
                    api_key = getattr(self, 'api_key', None)
                    username = getattr(self, 'username', None)
                    
                    success = await self.mcp_simple_router.initialize_for_user(
                        self.mcp_ids, 
                        api_key=api_key, 
                        username=username
                    )
                    if success:
                        mcp_tools = await self.mcp_simple_router.get_dynamic_mcp_tools(self.question)
                        logger.info(f"Simple路由器为 {self.mcp_ids} 加载了 {len(mcp_tools)} 个MCP工具")
                    else:
                        logger.warning(f"Simple路由器为 {self.mcp_ids} 初始化失败")
                else:
                    logger.warning("Simple路由器需要mcp_ids，但未提供")
            
        except Exception as e:
            logger.error(f"获取MCP工具失败: {e}")
        
        return mcp_tools
    
    async def ainvoke(self, input_data: Dict[str, Any], config: Optional[Dict] = None) -> Dict[str, Any]:
        """异步调用Agent
        
        Args:
            input_data: 输入数据，包含messages字段
            config: 配置信息
            
        Returns:
            包含messages字段的结果字典
        """
        try:
            # 确保工具已初始化
            if not hasattr(self, 'chain'):
                await self._initialize_tools()
            
            messages = input_data.get("messages", [])
            
            # 调用LLM生成响应
            response_text = await self.chain.ainvoke(
                {"messages": messages},
                config=config
            )
            
            # 解析文本输出
            parsed_actions = text_parser.parse_text(response_text)
            
            # 处理解析结果
            new_messages = []
            
            for action in parsed_actions:
                if action.action_type == ParsedActionType.THINKING:
                    # 思考内容不添加到消息流
                    logger.debug(f"MCP Agent思考: {action.content}")
                    continue
                    
                elif action.action_type == ParsedActionType.TOOL_CALL:
                    # 处理工具调用
                    tool_data = action.metadata.get("tool_call")
                    if tool_data:
                        tool_name = tool_data["name"]
                        tool_args = tool_data["args"]
                        
                        try:
                            if tool_name == "search_knowledge_base":
                                # 执行知识库搜索
                                search_result = await search_knowledge_base.ainvoke(tool_args)
                                
                                # 添加工具调用记录
                                tool_call_msg = AIMessage(
                                    content="",
                                    additional_kwargs={
                                        "tool_calls": [{
                                            "id": "text_tool_call",
                                            "function": {
                                                "name": "search_knowledge_base",
                                                "arguments": json.dumps(tool_args)
                                            },
                                            "type": "function"
                                        }]
                                    }
                                )
                                new_messages.append(tool_call_msg)
                                
                                # 生成基于搜索结果的回复
                                if search_result:
                                    knowledge_response = f"基于知识库搜索，我找到了以下相关信息：\n\n{search_result}"
                                else:
                                    knowledge_response = "抱歉，在知识库中没有找到相关信息。"
                                
                                new_messages.append(AIMessage(content=knowledge_response))
                                logger.info(f"MCP Agent完成知识库搜索")
                                
                            else:
                                # 尝试执行MCP工具
                                mcp_result = await self._execute_mcp_tool(tool_name, tool_args)
                                
                                if mcp_result is not None:
                                    # 添加工具调用记录
                                    tool_call_msg = AIMessage(
                                        content="",
                                        additional_kwargs={
                                            "tool_calls": [{
                                                "id": "text_mcp_call",
                                                "function": {
                                                    "name": tool_name,
                                                    "arguments": json.dumps(tool_args)
                                                },
                                                "type": "function"
                                            }]
                                        }
                                    )
                                    new_messages.append(tool_call_msg)
                                    
                                    # 生成基于MCP工具结果的回复
                                    mcp_response = f"使用{tool_name}工具处理结果：\n\n{mcp_result}"
                                    new_messages.append(AIMessage(content=mcp_response))
                                    logger.info(f"MCP Agent完成工具调用: {tool_name}")
                                else:
                                    error_response = f"调用MCP工具 {tool_name} 失败"
                                    new_messages.append(AIMessage(content=error_response))
                                    logger.warning(f"MCP工具调用失败: {tool_name}")
                                    
                        except Exception as tool_error:
                            logger.error(f"工具调用失败 {tool_name}: {tool_error}")
                            error_response = f"工具调用时遇到问题：{str(tool_error)}"
                            new_messages.append(AIMessage(content=error_response))
                    else:
                        # 工具调用解析失败
                        logger.warning(f"MCP Agent工具调用解析失败")
                        new_messages.append(AIMessage(content="收到了格式不正确的工具调用请求。"))
                        
                elif action.action_type == ParsedActionType.AGENT_HANDOFF:
                    # 处理Agent转交
                    handoff_data = action.metadata.get("handoff")
                    if handoff_data:
                        target_agent = handoff_data["target_agent"]
                        task_description = handoff_data["task_description"]
                        
                        # 生成转交消息
                        handoff_message = f"根据您的需求和工具分析，我将这个任务转交给{target_agent}代理来处理：{task_description}"
                        new_messages.append(AIMessage(content=handoff_message))
                        
                        logger.info(f"MCP Agent转交任务给{target_agent}: {task_description}")
                    else:
                        # 如果解析失败，使用原始内容
                        new_messages.append(AIMessage(content=action.content))
                        
                elif action.action_type == ParsedActionType.FINAL_RESPONSE:
                    # 最终回复
                    clean_content = text_parser.clean_text_output(action.content)
                    if clean_content.strip():
                        new_messages.append(AIMessage(content=clean_content))
                        
                else:
                    # 其他类型的动作
                    logger.warning(f"MCP Agent出现意外的动作类型: {action.action_type}")
                    clean_content = text_parser.clean_text_output(action.content)
                    if clean_content.strip():
                        new_messages.append(AIMessage(content=clean_content))
            
            # 如果没有生成任何消息，添加一个默认回复
            if not new_messages:
                clean_response = text_parser.clean_text_output(response_text)
                if clean_response.strip():
                    new_messages.append(AIMessage(content=clean_response))
                else:
                    new_messages.append(AIMessage(content="我理解了您的需求，但暂时无法使用相关工具处理。"))
            
            return {"messages": new_messages}
            
        except Exception as e:
            logger.error(f"文本模式MCP工具专家Agent执行失败: {e}")
            error_message = AIMessage(content=f"工具调用时遇到问题：{str(e)}")
            return {"messages": [error_message]}
    
    async def _execute_mcp_tool(self, tool_name: str, tool_args: Dict[str, Any]) -> Any:
        """执行MCP工具"""
        try:
            # 查找对应的MCP工具
            target_tool = None
            for tool in self.mcp_tools:
                if hasattr(tool, 'name') and tool.name == tool_name:
                    target_tool = tool
                    break
                elif str(tool) == tool_name:
                    target_tool = tool
                    break
            
            if target_tool is None:
                logger.warning(f"未找到MCP工具: {tool_name}")
                return None
            
            # 执行工具
            if hasattr(target_tool, 'ainvoke'):
                result = await target_tool.ainvoke(tool_args)
            elif hasattr(target_tool, 'invoke'):
                result = target_tool.invoke(tool_args)
            elif callable(target_tool):
                result = await target_tool(**tool_args) if asyncio.iscoroutinefunction(target_tool) else target_tool(**tool_args)
            else:
                logger.warning(f"MCP工具 {tool_name} 不可调用")
                return None
            
            return result
            
        except Exception as e:
            logger.error(f"执行MCP工具 {tool_name} 失败: {e}")
            return None
    
    def invoke(self, input_data: Dict[str, Any], config: Optional[Dict] = None) -> Dict[str, Any]:
        """同步调用Agent（实际使用异步实现）"""
        import asyncio
        return asyncio.run(self.ainvoke(input_data, config))


async def create_text_mcp_agent(
    llm: ChatOpenAI,
    question: str,
    mcp_ids: List[str],
    model_desc: str = "DeepSeek",
    checkpointer: Any = None,
    mcp_router: Any = None,
    mcp_simple_router: Any = None,
    handoff_tools: List[Any] = None,
    api_key: str = None,
    username: str = None
) -> TextMCPAgent:
    """创建文本模式MCP工具专家Agent
    
    Args:
        llm: ChatOpenAI模型实例
        question: 用户问题（用于动态工具选择）
        mcp_ids: MCP工具ID列表
        model_desc: 模型描述
        checkpointer: 检查点保存器（文本模式暂不支持）
        mcp_router: MCP RAG路由器
        mcp_simple_router: MCP简单路由器
        handoff_tools: handoff工具列表（用于Swarm模式）
        enable_context_summarization: 是否启用上下文总结（传统模式暂不支持）
        
    Returns:
        文本模式MCP工具专家Agent实例
    """
    try:
        logger.info(f"创建文本模式MCP工具专家Agent，MCP IDs: {mcp_ids}")
        
        agent = TextMCPAgent(
            llm=llm,
            question=question,
            mcp_ids=mcp_ids,
            model_desc=model_desc,
            mcp_router=mcp_router,
            mcp_simple_router=mcp_simple_router,
            handoff_tools=handoff_tools,
            api_key=api_key,
            username=username
        )
        
        # 预初始化工具
        await agent._initialize_tools()
        
        logger.info("文本模式MCP工具专家Agent创建成功")
        return agent
        
    except Exception as e:
        logger.error(f"创建文本模式MCP工具专家Agent失败: {e}")
        raise