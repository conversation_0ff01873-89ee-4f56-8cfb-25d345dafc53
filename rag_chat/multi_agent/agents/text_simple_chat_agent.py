"""
文本模式简单问答Agent

基于文本解析的简单问答Agent，不依赖function calling，使用结构化文本输出。
"""

import os
import sys
from typing import Any, Dict, Optional, List, Sequence, Annotated
from langchain_openai import ChatOpenAI
from langchain_core.messages import SystemMessage, HumanMessage, AIMessage, ToolMessage, BaseMessage
from langchain_core.output_parsers import StrOutputParser
from langchain_core.prompts import ChatPromptTemplate
from langchain_core.runnables import RunnableConfig
from langgraph.graph import StateGraph, END, START
from langgraph.graph.message import add_messages
from typing_extensions import TypedDict

from logger import logger
from jiliang_chat_prompt import SIMPLE_CHAT_PROMPT
from text_parser import text_parser, ParsedActionType
from tool_message_adapter import tool_message_adapter
# 导入上下文总结功能
sys.path.append(os.path.dirname(os.path.dirname(os.path.dirname(os.path.abspath(__file__)))))
from context_summarizer import get_default_summarizer


class TextSimpleChatAgentState(TypedDict):
    """文本模式简单问答Agent状态定义"""
    messages: Annotated[Sequence[BaseMessage], add_messages]
    # 存储模型描述
    model_desc: str
    # 标记是否执行了handoff，执行后应该结束Agent让Swarm处理
    handoff_executed: bool
    # 上下文总结状态（用于LangMem上下文管理）
    running_summary: Optional[Any]  # RunningSummary类型，但防止导入错误使用Any


class TextSimpleChatAgent:
    """基于文本解析的简单问答Agent"""
    
    def __init__(self, 
                 llm: ChatOpenAI,
                 model_desc: str = "DeepSeek",
                 handoff_tools: List[Any] = None,
                 enable_context_summarization: bool = True,
                 checkpointer: Any = None):
        """初始化文本模式简单问答Agent
        
        Args:
            llm: ChatOpenAI模型实例
            model_desc: 模型描述
            handoff_tools: handoff工具列表（用于Swarm模式）
            enable_context_summarization: 是否启用上下文总结
        """
        self.llm = llm
        self.model_desc = model_desc
        self.handoff_tools = handoff_tools or []
        self.enable_context_summarization = enable_context_summarization
        self.checkpointer = checkpointer

        # 初始化上下文总结器
        self.context_summarizer = None
        if self.enable_context_summarization:
            try:
                self.context_summarizer = get_default_summarizer()
                logger.info(f"简单问答Agent上下文总结功能已启用")
            except Exception as e:
                logger.warning(f"简单问答Agent上下文总结器初始化失败: {e}")
                self.enable_context_summarization = False
        
        # 构建系统提示词
        self.system_prompt = SIMPLE_CHAT_PROMPT.format(model_desc=model_desc)
        
        # 如果有handoff工具，添加相关指令
        if self.handoff_tools:
            available_agents = [tool.name.replace("transfer_to_", "") for tool in self.handoff_tools]
            handoff_instruction = text_parser.format_handoff_prompt(available_agents)
            self.system_prompt += "\n\n" + handoff_instruction
        
        # 创建聊天模板
        self.prompt_template = ChatPromptTemplate.from_messages([
            ("system", self.system_prompt),
            ("placeholder", "{messages}")
        ])
        
        # 创建处理链
        self.chain = self.prompt_template | self.llm | StrOutputParser()
        
        logger.info(f"文本模式简单问答Agent初始化完成，handoff工具数量: {len(self.handoff_tools)}")
    
    def _create_context_summarization_node(self):
        """创建上下文总结节点（模拟pre_model_hook）"""
        async def context_summarization(state: TextSimpleChatAgentState, config: RunnableConfig):
            """上下文总结节点：在LLM调用前进行上下文管理"""
            try:
                if not self.enable_context_summarization or not self.context_summarizer:
                    # 未启用或初始化失败，直接返回空更新
                    return {}
                
                messages = state.get("messages", [])
                if not messages:
                    return {}
                
                # 从状态中获取运行总结
                running_summary = state.get("running_summary")
                
                # 检查是否需要总结
                if not self.context_summarizer.should_summarize(messages):
                    return {}
                
                logger.info(f"简单问答Agent开始上下文总结：{len(messages)}条消息")
                
                # 执行总结
                processed_messages, updated_summary = self.context_summarizer.summarize_messages_with_state(
                    messages, running_summary
                )
                
                # 检查是否实际进行了压缩
                if len(processed_messages) >= len(messages):
                    # 没有进行实际的压缩，返回空更新
                    return {}
                
                logger.info(f"简单问答Agent上下文总结完成：{len(messages)} -> {len(processed_messages)}条消息")
                
                # 构建状态更新：使用RemoveMessage移除所有现有消息，然后添加处理后的消息
                from langchain_core.messages import RemoveMessage
                try:
                    from langgraph.graph.message import REMOVE_ALL_MESSAGES
                except ImportError:
                    REMOVE_ALL_MESSAGES = "REMOVE_ALL_MESSAGES"
                
                state_update = {
                    "messages": [RemoveMessage(REMOVE_ALL_MESSAGES)] + processed_messages
                }
                
                # 如果有更新的运行总结，也保存到状态中
                if updated_summary != running_summary:
                    state_update["running_summary"] = updated_summary
                    logger.debug("简单问答Agent更新了运行总结状态")
                
                return state_update
                
            except Exception as e:
                logger.error(f"简单问答Agent上下文总结失败: {e}")
                return {}  # 发生错误时返回空更新，不影响正常流程
        
        return context_summarization
    
    def _build_graph(self):
        """构建LangGraph图"""
        from langgraph.checkpoint.memory import MemorySaver
        
        # 创建状态图
        workflow = StateGraph(TextSimpleChatAgentState)
        
        # 创建主要执行节点
        async def call_agent(state: TextSimpleChatAgentState, config: RunnableConfig):
            """调用Agent主逻辑"""
            try:
                messages = state.get("messages", [])
                
                # 调用LLM生成响应
                response_text = await self.chain.ainvoke(
                    {"messages": messages},
                    config=config
                )
                
                # 解析文本输出
                parsed_actions = text_parser.parse_text(response_text)
                
                # 处理解析结果
                new_messages = []
                handoff_executed = False  # 跟踪是否执行了handoff
                
                for action in parsed_actions:
                    if action.action_type == ParsedActionType.THINKING:
                        # 思考内容不添加到消息流
                        logger.debug(f"Agent思考: {action.content}")
                        continue
                        
                    elif action.action_type == ParsedActionType.AGENT_HANDOFF:
                        # 处理Agent转交
                        handoff_data = action.metadata.get("handoff")
                        if handoff_data:
                            target_agent = handoff_data["target_agent"]
                            task_description = handoff_data["task_description"]
                            
                            # 生成转交消息（用户可见）
                            handoff_message = f"我将这个任务转交给{target_agent}代理来处理：{task_description}"
                            new_messages.append(AIMessage(content=handoff_message))
                            
                            # 生成handoff工具消息（供Swarm检测）
                            handoff_tool_msg = tool_message_adapter.create_handoff_message(
                                target_agent=target_agent,
                                task_description=task_description,
                                current_agent="simple_chat",
                                tool_call_id=f"handoff_{target_agent}_{len(new_messages)}"
                            )
                            new_messages.append(handoff_tool_msg)
                            handoff_executed = True  # 标记已执行handoff
                            
                            logger.info(f"简单问答Agent转交任务给{target_agent}: {task_description}")
                        else:
                            # 如果解析失败，使用原始内容
                            new_messages.append(AIMessage(content=action.content))
                            
                    elif action.action_type == ParsedActionType.FINAL_RESPONSE:
                        # 最终回复
                        clean_content = text_parser.clean_text_output(action.content)
                        if clean_content.strip():
                            new_messages.append(AIMessage(content=clean_content))
                            
                    else:
                        # 其他类型的动作（如工具调用）在简单问答中不应该出现
                        logger.warning(f"简单问答Agent出现意外的动作类型: {action.action_type}")
                        clean_content = text_parser.clean_text_output(action.content)
                        if clean_content.strip():
                            new_messages.append(AIMessage(content=clean_content))
                
                # 如果没有生成任何消息，添加一个默认回复
                if not new_messages:
                    clean_response = text_parser.clean_text_output(response_text)
                    if clean_response.strip():
                        new_messages.append(AIMessage(content=clean_response))
                    else:
                        new_messages.append(AIMessage(content="我理解了您的问题，但暂时无法提供具体回复。"))
                
                return {"messages": new_messages, "handoff_executed": handoff_executed}
                
            except Exception as e:
                logger.error(f"文本模式简单问答Agent执行失败: {e}")
                error_message = AIMessage(content=f"抱歉，处理您的问题时遇到了问题：{str(e)}")
                return {"messages": [error_message], "handoff_executed": False}
        
        # 创建上下文总结节点
        context_summarization = self._create_context_summarization_node()
        
        # 添加节点
        if self.enable_context_summarization and self.context_summarizer:
            # 启用上下文总结时，添加总结节点
            workflow.add_node("context_summarization", context_summarization)
            workflow.add_node("simple_chat_agent", call_agent)
            
            # 设置入口点为上下文总结节点
            workflow.set_entry_point("context_summarization")
            
            # 上下文总结后直接进入agent节点
            workflow.add_edge("context_summarization", "simple_chat_agent")
        else:
            # 未启用上下文总结时，直接使用agent节点
            workflow.add_node("simple_chat_agent", call_agent)
            
            # 设置入口点为agent节点
            workflow.set_entry_point("simple_chat_agent")
        
        # agent节点执行完成后直接结束
        workflow.add_edge("simple_chat_agent", END)
        
        # 编译图
        graph = workflow.compile(checkpointer=self.checkpointer)
        
        logger.info("文本模式简单问答Agent图构建完成")
        return graph
    
    async def ainvoke(self, input_data: Dict[str, Any], config: Optional[Dict] = None) -> Dict[str, Any]:
        """异步调用Agent
        
        Args:
            input_data: 输入数据，包含messages字段
            config: 配置信息
            
        Returns:
            包含messages字段的结果字典
        """
        try:
            # 构建图
            graph = self._build_graph()
            
            # 准备状态
            messages = input_data.get("messages", [])
            state = TextSimpleChatAgentState(
                messages=messages,
                model_desc=self.model_desc,
                handoff_executed=False,
                running_summary=None
            )
            
            # 执行图
            result = await graph.ainvoke(state, config)
            
            return {"messages": result["messages"], "handoff_executed": result.get("handoff_executed", False)}
            
        except Exception as e:
            logger.error(f"文本模式简单问答Agent执行失败: {e}")
            error_message = AIMessage(content=f"抱歉，处理您的问题时遇到了问题：{str(e)}")
            return {"messages": [error_message], "handoff_executed": False}
    
    def invoke(self, input_data: Dict[str, Any], config: Optional[Dict] = None) -> Dict[str, Any]:
        """同步调用Agent（实际使用异步实现）"""
        import asyncio
        return asyncio.run(self.ainvoke(input_data, config))


async def create_text_simple_chat_agent(
    llm: ChatOpenAI,
    model_desc: str = "DeepSeek",
    checkpointer: Any = None,
    handoff_tools: List[Any] = None,
    enable_context_summarization: bool = True
) -> TextSimpleChatAgent:
    """创建文本模式简单问答Agent
    
    Args:
        llm: ChatOpenAI模型实例
        model_desc: 模型描述
        checkpointer: 检查点保存器（文本模式暂不支持）
        handoff_tools: handoff工具列表（用于Swarm模式）
        enable_context_summarization: 是否启用上下文总结
        
    Returns:
        文本模式简单问答Agent实例
    """
    try:
        logger.info(f"创建文本模式简单问答Agent，上下文总结: {enable_context_summarization}")
        
        agent = TextSimpleChatAgent(
            llm=llm,
            model_desc=model_desc,
            handoff_tools=handoff_tools,
            enable_context_summarization=enable_context_summarization,
            checkpointer=checkpointer
        )
        
        logger.info("文本模式简单问答Agent创建成功")
        return agent
        
    except Exception as e:
        logger.error(f"创建文本模式简单问答Agent失败: {e}")
        raise