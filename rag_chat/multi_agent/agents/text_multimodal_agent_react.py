"""
基于文本解析的真正ReAct循环多模态Agent

保留文本解析的核心逻辑，但改造为真正的LangGraph ReAct循环：
LLM输出 → 文本解析 → 工具执行 → 工具结果反馈给LLM → LLM继续推理 → 直到完成
"""

import os
import sys
import json
import asyncio
from typing import Any, Dict, Optional, List, Sequence, Annotated, Literal
from datetime import datetime
from langchain_openai import ChatOpenAI
from langchain_core.messages import SystemMessage, HumanMessage, AIMessage, BaseMessage, ToolMessage
from langchain_core.runnables import RunnableConfig
from langgraph.graph import StateGraph, END, START
from langgraph.graph.message import add_messages
from langgraph.types import Command
from langgraph.config import get_stream_writer
from typing_extensions import TypedDict
from models import UploadedFileInfo

sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))
from logger import logger
from jiliang_chat_prompt import MULTIMODAL_AGENT_PROMPT
from text_parser import text_parser, ParsedActionType, ToolCall
from langgraph.checkpoint.memory import MemorySaver
from tool_message_adapter import tool_message_adapter
# 导入上下文总结功能
sys.path.append(os.path.dirname(os.path.dirname(os.path.dirname(os.path.abspath(__file__)))))
from context_summarizer import get_default_summarizer


class TextMultimodalAgentState(TypedDict):
    """基于文本解析的多模态Agent状态定义"""
    messages: Annotated[Sequence[BaseMessage], add_messages]
    # 存储模型描述
    model_desc: str
    # 存储当前迭代次数，防止无限循环
    iteration_count: int
    # 标记是否执行了handoff，执行后应该结束Agent让Swarm处理
    handoff_executed: bool
    # 上下文总结状态（用于LangMem上下文管理）
    running_summary: Optional[Any]  # RunningSummary类型，但防止导入错误使用Any


class TextMultimodalAgentReAct:
    """基于文本解析的ReAct循环多模态Agent"""
    
    def __init__(self, 
                 llm: ChatOpenAI,
                 model_desc: str = "DeepSeek",
                 handoff_tools: Optional[List[Any]] = None,
                 max_iterations: int = 10,
                 checkpointer: Any = MemorySaver(),
                 enable_context_summarization: bool = True,
                 uploaded_files: Optional[List[UploadedFileInfo]] = None):
        """初始化基于文本解析的ReAct多模态Agent
        
        Args:
            llm: ChatOpenAI模型实例
            model_desc: 模型描述
            handoff_tools: handoff工具列表（用于Swarm模式）
            max_iterations: 最大迭代次数，防止无限循环
            checkpointer: 检查点保存器
            enable_context_summarization: 是否启用上下文总结
            uploaded_files: 上传的文件信息列表
        """
        self.llm = llm
        self.model_desc = model_desc
        self.handoff_tools = handoff_tools or []
        self.max_iterations = max_iterations
        self.checkpointer = checkpointer
        self.enable_context_summarization = enable_context_summarization
        self.uploaded_files = uploaded_files or []
        
        # 初始化上下文总结器
        self.context_summarizer = None
        if self.enable_context_summarization:
            try:
                self.context_summarizer = get_default_summarizer()
                logger.info(f"多模态Agent上下文总结功能已启用")
            except Exception as e:
                logger.warning(f"多模态Agent上下文总结器初始化失败: {e}")
                self.enable_context_summarization = False
        
        # 构建系统提示词
        self.system_prompt = self._build_system_prompt()
        
        # 初始化多模态工具
        self.multimodal_tools = self._get_multimodal_tools()
        
        self._initialize_tools_prompt()
        
        logger.info(f"文本ReAct多模态Agent初始化完成")
    
    def _build_system_prompt(self) -> str:
        """构建包含文件信息的系统提示词"""
        # 构建可用文件列表
        available_files_text = "当前没有上传文件"

        logger.info(f"多模态Agent上传的文件: {self.uploaded_files}")

        if self.uploaded_files:
            file_list = []
            for i, file_info in enumerate(self.uploaded_files, 1):
                filename = file_info.filename
                file_path = file_info.file_path
                file_size = file_info.file_size
                is_image = file_info.is_image
                upload_time = file_info.upload_time

                # 只显示文档文件，图片文件会直接添加到消息中
                if not is_image:
                    size_mb = file_size / (1024 * 1024) if file_size > 0 else 0
                    file_desc = f"文档 {len(file_list) + 1}: {filename} ({size_mb:.2f}MB)\n   路径: {file_path}\n   上传时间: {upload_time}"
                    file_list.append(file_desc)
            
            if file_list:
                available_files_text = f"当前已上传 {len(file_list)} 个文档文件：\n" + "\n".join(file_list)
            else:
                available_files_text = "当前没有上传文档文件（图片已直接添加到对话中）"
        
        return MULTIMODAL_AGENT_PROMPT.format(
            model_desc=self.model_desc,
            available_files=available_files_text
        )
    
    def _initialize_tools_prompt(self):
        """初始化工具提示词"""
        tool_instructions = []
        
        # 添加基础搜索工具指令
        # search_instruction = text_parser.format_tool_call_prompt(
        #     tool_name="search_knowledge_base",
        #     description="搜索三峡集团知识库以获取相关信息",
        #     args_schema={"question": "搜索关键词或问题"}
        # )
        # tool_instructions.append(search_instruction)
        
        # 添加多模态工具指令
        for tool in self.multimodal_tools:
            try:
                tool_name = getattr(tool, 'name', str(tool))
                tool_desc = getattr(tool, 'description', f"多模态工具: {tool_name}")
                
                # 尝试获取工具的参数模式
                args_schema = {"input": "工具输入参数"}  # 默认值
                
                # 如果工具有 args_schema 属性，使用它
                if hasattr(tool, 'args_schema') and tool.args_schema:
                    try:
                        schema_dict = tool.args_schema.model_json_schema()
                        if 'properties' in schema_dict:
                            args_schema = {}
                            required_fields = schema_dict.get('required', [])
                            for prop_name, prop_info in schema_dict['properties'].items():
                                args_schema[prop_name] = {
                                    "type": prop_info.get('type', 'string'),
                                    "description": prop_info.get('description', f"{prop_name}参数"),
                                    "required": prop_name in required_fields
                                }
                    except Exception as schema_error:
                        logger.debug(f"获取工具{tool_name}的参数模式失败: {schema_error}")
                
                # 如果工具有 args 属性，使用它
                elif hasattr(tool, 'args') and tool.args:
                    try:
                        if isinstance(tool.args, dict) and 'properties' in tool.args:
                            args_schema = {}
                            required_fields = tool.args.get('required', [])
                            for prop_name, prop_info in tool.args['properties'].items():
                                args_schema[prop_name] = {
                                    "type": prop_info.get('type', 'string'),
                                    "description": prop_info.get('description', f"{prop_name}参数"),
                                    "required": prop_name in required_fields
                                }
                    except Exception as args_error:
                        logger.debug(f"获取工具{tool_name}的args失败: {args_error}")
                
                tool_instruction = text_parser.format_tool_call_prompt(
                    tool_name=tool_name,
                    description=tool_desc,
                    args_schema=args_schema
                )
                tool_instructions.append(tool_instruction)
            except Exception as e:
                logger.warning(f"处理多模态工具 {tool} 时出错: {e}")
        
        # 合并工具指令到系统提示词
        if tool_instructions:
            self.system_prompt += "\n\n可用工具:\n" + "\n\n".join(tool_instructions)
        
        # 添加handoff工具指令
        if self.handoff_tools:
            for handoff_tool in self.handoff_tools:
                try:
                    tool_name = handoff_tool.name
                    tool_desc = handoff_tool.description
                    # 为转交工具添加标准的参数模式
                    args_schema = {"task_description": "要转交的任务描述"}
                    
                    handoff_instruction = text_parser.format_tool_call_prompt(
                        tool_name=tool_name,
                        description=tool_desc,
                        args_schema=args_schema
                    )
                    tool_instructions.append(handoff_instruction)
                except Exception as e:
                    logger.warning(f"处理handoff工具 {handoff_tool} 时出错: {e}")
            
            # 添加额外的转交指导
            available_agents = [tool.name.replace("transfer_to_", "") for tool in self.handoff_tools]
            handoff_instruction = text_parser.format_handoff_prompt(available_agents)
            self.system_prompt += "\n\n" + handoff_instruction
        
        # 添加ReAct循环指令
        self.system_prompt += """

## 重要提醒：
**在决定调用工具时，禁止输出回答，只允许生成调用信息**
"""
    
    def _get_multimodal_tools(self) -> List[Any]:
        """获取多模态工具列表"""
        tools = []
        
        try:
            # 导入工具实例
            from jiliang_chat_tools import search_knowledge_base, search_document_with_rag, read_document_directly, smart_document_search
            
            # 添加文档处理工具
            tools.append(search_document_with_rag)      # RAG智能搜索
            tools.append(read_document_directly)        # 直接文档读取  
            tools.append(smart_document_search)         # 智能混合策略搜索
            
            logger.info(f"多模态Agent加载了 {len(tools)} 个文档处理工具")
            
        except Exception as e:
            logger.error(f"加载多模态工具失败: {e}")
            
        return tools
    
    def _create_call_model_node(self):
        """创建调用模型的节点"""
        async def call_model(state: TextMultimodalAgentState, config: RunnableConfig):
            """调用LLM模型节点"""
            try:
                messages = state["messages"]
                iteration_count = state.get("iteration_count", 0)
                
                # 防止无限循环
                if iteration_count >= self.max_iterations:
                    logger.warning(f"多模态Agent达到最大迭代次数 {self.max_iterations}，强制结束")
                    return {
                        "messages": [AIMessage(
                            content="已达到最大处理次数，请简化您的问题重新提问。",
                            name="multimodal_agent"
                        )],
                        "iteration_count": iteration_count
                    }
                
                # 如果第一条消息不是系统消息，添加系统消息
                if not messages or not isinstance(messages[0], SystemMessage):
                    system_msg = SystemMessage(content=self.system_prompt)
                    messages = [system_msg] + list(messages)
                
                # 调用LLM（不绑定工具，使用文本解析）
                response = await self.llm.ainvoke(messages, config)
                response.content = response.content.split("</think>")[-1].strip()
                
                # 设置agent名称
                response.name = "multimodal_agent"
                
                logger.info(f"多模态Agent LLM调用完成，iteration: {iteration_count + 1}")
                
                return {
                    "messages": [response],
                    "iteration_count": iteration_count + 1
                }
                
            except Exception as e:
                logger.error(f"多模态Agent调用LLM失败: {e}")
                error_msg = AIMessage(
                    content=f"处理请求时出错: {str(e)}",
                    name="multimodal_agent"
                )
                return {
                    "messages": [error_msg],
                    "iteration_count": state.get("iteration_count", 0)
                }
        
        return call_model
    
    def _create_should_continue_node(self):
        """创建条件判断节点"""
        def should_continue(state: TextMultimodalAgentState) -> Literal["execute_tools", END]:
            """判断是否继续执行工具"""
            messages = state["messages"]
            last_message = messages[-1]
            
            if not hasattr(last_message, 'content') or not last_message.content:
                logger.info("多模态Agent最后消息为空，结束流程")
                return END
            
            # 使用文本解析检查是否有工具调用
            parsed_actions = text_parser.parse_text(last_message.content)
            
            has_tool_calls = any(action.action_type == ParsedActionType.TOOL_CALL for action in parsed_actions)
            has_handoffs = any(action.action_type == ParsedActionType.AGENT_HANDOFF for action in parsed_actions)
            
            if has_tool_calls:
                logger.info("多模态Agent检测到工具调用，继续执行")
                return "execute_tools"
            
            # 检查是否有agent转交
            if has_handoffs:
                logger.info("多模态Agent检测到agent转交，执行转交")
                return "execute_tools"
            
            # 否则结束
            logger.info("多模态Agent没有检测到工具调用或转交，结束流程")
            return END
        
        return should_continue
    
    def _create_context_summarization_node(self):
        """创建上下文总结节点（模拟pre_model_hook）"""
        async def context_summarization(state: TextMultimodalAgentState, config: RunnableConfig):
            """上下文总结节点：在LLM调用前进行上下文管理"""
            try:
                if not self.enable_context_summarization or not self.context_summarizer:
                    # 未启用或初始化失败，直接返回空更新
                    return {}
                
                messages = state.get("messages", [])
                if not messages:
                    return {}
                
                # 从状态中获取运行总结
                running_summary = state.get("running_summary")
                
                # 检查是否需要总结
                if not self.context_summarizer.should_summarize(messages):
                    return {}
                
                logger.info(f"多模态Agent开始上下文总结：{len(messages)}条消息")
                
                # 执行总结
                processed_messages, updated_summary = self.context_summarizer.summarize_messages_with_state(
                    messages, running_summary
                )
                
                # 检查是否实际进行了压缩
                if len(processed_messages) >= len(messages):
                    # 没有进行实际的压缩，返回空更新
                    return {}
                
                logger.info(f"多模态Agent上下文总结完成：{len(messages)} -> {len(processed_messages)}条消息")
                
                # 构建状态更新：使用RemoveMessage移除所有现有消息，然后添加处理后的消息
                from langchain_core.messages import RemoveMessage
                try:
                    from langgraph.graph.message import REMOVE_ALL_MESSAGES
                except ImportError:
                    REMOVE_ALL_MESSAGES = "REMOVE_ALL_MESSAGES"
                
                state_update = {
                    "messages": [RemoveMessage(REMOVE_ALL_MESSAGES)] + processed_messages
                }
                
                # 如果有更新的运行总结，也保存到状态中
                if updated_summary != running_summary:
                    state_update["running_summary"] = updated_summary
                    logger.debug("多模态Agent更新了运行总结状态")
                
                return state_update
                
            except Exception as e:
                logger.error(f"多模态Agent上下文总结失败: {e}")
                return {}  # 发生错误时返回空更新，不影响正常流程
        
        return context_summarization
    
    def _create_execute_tools_node(self):
        """创建执行工具的节点"""
        async def execute_tools(state: TextMultimodalAgentState, config: RunnableConfig):
            """执行工具节点"""
            try:
                # 导入所需的工具
                from jiliang_chat_tools import search_knowledge_base, search_document_with_rag, read_document_directly, smart_document_search
                
                messages = state["messages"]
                last_message = messages[-1]
                
                # 解析文本输出
                parsed_actions = text_parser.parse_text(last_message.content)
                
                # 处理解析结果 - 优化执行策略
                new_messages = []
                handoff_executed = False  # 跟踪是否执行了handoff
                
                # 分类工具调用：普通工具 vs 转交工具
                regular_tools = []  # 普通工具调用
                handoff_tools = []  # 转交工具调用
                other_actions = []  # 其他动作
                
                for action in parsed_actions:
                    if action.action_type == ParsedActionType.TOOL_CALL:
                        tool_data = action.metadata.get("tool_call")
                        if tool_data:
                            tool_name = tool_data.get("name", "")
                            if tool_name.startswith("transfer_to_"):
                                handoff_tools.append(action)
                            else:
                                regular_tools.append(action)
                    else:
                        other_actions.append(action)
                
                # 执行策略：优先执行普通工具，再考虑转交
                if regular_tools:
                    # 有普通工具调用，优先并行执行这些工具
                    logger.info(f"多模态Agent检测到{len(regular_tools)}个普通工具调用，开始并行执行")
                    # 定义异步工具执行函数
                    async def execute_single_tool(action):
                        tool_data = action.metadata.get("tool_call")
                        if tool_data:
                            tool_name = tool_data["name"]
                            tool_args = tool_data["args"]
                            
                            try:
                                if tool_name == "search_knowledge_base":

                                    # 执行知识库搜索
                                    search_result = await search_knowledge_base.ainvoke(tool_args)
                                    
                                    # 生成工具执行结果消息
                                    if search_result:
                                        tool_result = f"知识库搜索结果：\n{search_result}"
                                    else:
                                        tool_result = "知识库搜索未找到相关信息"
                                    
                                    # 发送工具执行结果事件到service层
                                    stream_writer = get_stream_writer()
                                    if stream_writer:
                                        stream_writer({
                                            "event_type": "tool_execution",
                                            "event": "tool_result",
                                            "agent_name": "multimodal_agent",
                                            "tool_name": tool_name,
                                            "display_name": tool_name,
                                            "content": tool_result,
                                            "truncated_content": tool_result[:500] + "..." if len(tool_result) > 500 else tool_result,
                                            "content_length": len(tool_result),
                                            "timestamp": datetime.now().isoformat(),
                                            "is_error": False
                                        })
                                    
                                    # 创建工具结果消息
                                    tool_msg = tool_message_adapter.create_tool_message(
                                        content=tool_result,
                                        tool_name=tool_name,
                                        tool_call_id=f"call_{tool_name}_{len(messages)}_{id(action)}"
                                    )
                                    
                                    logger.info(f"多模态Agent完成知识库搜索")
                                    return tool_msg
                                    
                                else:
                                    # 参数检查
                                    if tool_name == "search_document_with_rag" and "file_paths" in tool_args and type(tool_args["file_paths"]) == str:
                                        tool_args["file_paths"] = [tool_args["file_paths"]]

                                    # 尝试执行多模态工具
                                    multimodal_result = await self._execute_multimodal_tool(tool_name, tool_args)
                                    
                                    if multimodal_result is not None:
                                        tool_result = f"多模态工具 {tool_name} 执行结果：\n{multimodal_result}"
                                        is_error = False
                                    else:
                                        tool_result = f"多模态工具 {tool_name} 执行失败"
                                        is_error = True
                                    
                                    # 发送工具执行结果事件到service层
                                    stream_writer = get_stream_writer()
                                    if stream_writer:
                                        stream_writer({
                                            "event_type": "tool_execution",
                                            "event": "tool_result",
                                            "agent_name": "multimodal_agent",
                                            "tool_name": tool_name,
                                            "display_name": tool_name,
                                            "content": tool_result,
                                            "truncated_content": tool_result[:500] + "..." if len(tool_result) > 500 else tool_result,
                                            "content_length": len(tool_result),
                                            "timestamp": datetime.now().isoformat(),
                                            "is_error": is_error
                                        })
                                    
                                    # 创建工具结果消息
                                    tool_msg = tool_message_adapter.create_tool_message(
                                        content=tool_result,
                                        tool_name=tool_name,
                                        tool_call_id=f"call_{tool_name}_{len(messages)}_{id(action)}"
                                    )
                                    
                                    logger.info(f"多模态Agent完成工具调用: {tool_name}")
                                    return tool_msg
                                    
                            except Exception as tool_error:
                                logger.error(f"工具调用失败 {tool_name}: {tool_error}")
                                
                                # 发送工具执行错误事件
                                stream_writer = get_stream_writer()
                                if stream_writer:
                                    stream_writer({
                                        "event_type": "tool_execution",
                                        "event": "tool_error",
                                        "agent_name": "multimodal_agent",
                                        "tool_name": tool_name,
                                        "display_name": tool_name,
                                        "content": f"工具执行失败: {str(tool_error)}",
                                        "error": str(tool_error),
                                        "timestamp": datetime.now().isoformat(),
                                        "is_error": True
                                    })
                                
                                error_msg = tool_message_adapter.create_tool_error_message(
                                    error_message=str(tool_error),
                                    tool_name=tool_name,
                                    tool_call_id=f"call_{tool_name}_{len(messages)}_{id(action)}_error"
                                )
                                return error_msg
                        return None
                    
                    # 并行执行所有工具
                    tool_tasks = [execute_single_tool(action) for action in regular_tools]
                    tool_results = await asyncio.gather(*tool_tasks, return_exceptions=True)
                    
                    # 处理工具执行结果
                    for result in tool_results:
                        if result and not isinstance(result, Exception):
                            new_messages.append(result)
                        elif isinstance(result, Exception):
                            logger.error(f"工具并行执行异常: {result}")
                            error_msg = tool_message_adapter.create_tool_error_message(
                                error_message=f"工具并行执行异常: {str(result)}",
                                tool_name="parallel_execution_error",
                                tool_call_id=f"call_parallel_error_{len(messages)}"
                            )
                            new_messages.append(error_msg)
                    
                    # 普通工具执行完成后，不执行转交工具，让LLM基于结果决定下一步
                    logger.info(f"多模态Agent完成{len(regular_tools)}个普通工具并行调用，跳过转交工具，等待LLM下一轮决策")
                    
                elif handoff_tools:
                    # 没有普通工具调用，只有转交工具，执行第一个转交
                    logger.info(f"多模态Agent只有转交工具调用，执行第一个转交")
                    action = handoff_tools[0]  # 只执行第一个转交工具
                    tool_data = action.metadata.get("tool_call")
                    if tool_data:
                        tool_name = tool_data["name"]
                        target_agent = tool_name.replace("transfer_to_", "")
                        tool_args = tool_data.get("args", {})
                        task_description = tool_args.get("task_description", "")
                        
                        try:
                            # 执行转交工具（使用handoff工具）
                            handoff_result = None
                            for tool in self.handoff_tools:
                                if tool.name == tool_name:
                                    handoff_result = await tool._arun(task_description)
                                    break
                            
                            if handoff_result:
                                tool_result = handoff_result
                            else:
                                tool_result = f"转交到 {target_agent} 失败：未找到对应的转交工具"
                            
                            # 添加工具结果消息
                            tool_msg = tool_message_adapter.create_tool_message(
                                content=tool_result,
                                tool_name=tool_name,
                                tool_call_id=f"call_{tool_name}_{len(messages)}"
                            )
                            new_messages.append(tool_msg)
                            handoff_executed = True  # 标记已执行handoff
                            
                            logger.info(f"多模态Agent完成转交工具调用: {tool_name} -> {target_agent}")
                            
                        except Exception as tool_error:
                            logger.error(f"转交工具调用失败: {tool_error}")
                            error_msg = tool_message_adapter.create_tool_error_message(
                                error_message=f"转交工具调用失败: {str(tool_error)}",
                                tool_name=tool_name,
                                tool_call_id=f"call_{tool_name}_{len(messages)}_error"
                            )
                            new_messages.append(error_msg)
                
                # 处理其他类型的动作（非工具调用类型）
                for action in other_actions:
                    if action.action_type == ParsedActionType.THINKING:
                        # 思考内容不添加到消息流
                        logger.debug(f"多模态Agent思考: {action.content}")
                        continue
                        
                    elif action.action_type == ParsedActionType.AGENT_HANDOFF:
                        # 处理Agent转交（在工具执行阶段记录，但不执行）
                        handoff_data = action.metadata.get("handoff")
                        if handoff_data:
                            target_agent = handoff_data["target_agent"]
                            task_description = handoff_data["task_description"]
                            
                            # 记录转交信息
                            handoff_msg = tool_message_adapter.create_handoff_message(
                                target_agent=target_agent,
                                task_description=task_description,
                                current_agent="multimodal_agent",
                                tool_call_id=f"handoff_{target_agent}_{len(messages)}"
                            )
                            new_messages.append(handoff_msg)
                            
                            logger.info(f"多模态Agent记录转交信息: {target_agent}")
                
                # 如果没有生成任何消息，添加一个默认消息
                if not new_messages:
                    default_msg = tool_message_adapter.create_tool_message(
                        content="工具执行完成",
                        tool_name="default",
                        tool_call_id=f"call_default_{len(messages)}"
                    )
                    new_messages.append(default_msg)
                
                return {"messages": new_messages, "handoff_executed": handoff_executed}
                
            except Exception as e:
                logger.error(f"多模态Agent执行工具失败: {e}")
                error_msg = tool_message_adapter.create_tool_error_message(
                    error_message=f"工具执行时出错: {str(e)}",
                    tool_name="execution_error",
                    tool_call_id=f"call_error_{len(messages)}"
                )
                return {"messages": [error_msg]}
        
        return execute_tools
    
    async def _execute_multimodal_tool(self, tool_name: str, tool_args: Dict[str, Any]) -> Any:
        """执行多模态工具"""
        try:
            # 导入所需的工具
            from jiliang_chat_tools import search_document_with_rag, read_document_directly, smart_document_search
            
            if tool_name == "search_document_with_rag":
                # 执行RAG搜索
                file_paths = tool_args.get("file_paths", [])
                query = tool_args.get("query", "")
                describe_image = tool_args.get("describe_image", False)
                k = tool_args.get("k", 5)
                
                # 添加调试日志
                logger.info(f"多模态Agent执行RAG搜索: query={query}, files={len(file_paths)}")
                
                result = await search_document_with_rag.ainvoke({
                    "file_paths": file_paths,
                    "query": query,
                    "describe_image": describe_image,
                    "k": k
                })
                return result
                
            elif tool_name == "read_document_directly":
                # 执行直接文档读取
                file_paths = tool_args.get("file_paths", [])
                start_line = tool_args.get("start_line", 1)
                line_count = tool_args.get("line_count", 50)
                search_keywords = tool_args.get("search_keywords", None)
                describe_image = tool_args.get("describe_image", False)
                
                logger.info(f"多模态Agent执行直接读取: files={len(file_paths)}, lines={start_line}-{start_line+line_count-1}")
                
                result = await read_document_directly.ainvoke({
                    "file_paths": file_paths,
                    "start_line": start_line,
                    "line_count": line_count,
                    "search_keywords": search_keywords,
                    "describe_image": describe_image
                })
                return result
                
            elif tool_name == "smart_document_search":
                # 执行智能文档搜索（自动选择策略）
                file_paths = tool_args.get("file_paths", [])
                query = tool_args.get("query", "")
                describe_image = tool_args.get("describe_image", False)
                size_threshold_mb = tool_args.get("size_threshold_mb", None)
                max_results = tool_args.get("max_results", 5)
                
                logger.info(f"多模态Agent执行智能搜索: query={query}, files={len(file_paths)}, threshold={size_threshold_mb}")
                
                result = await smart_document_search.ainvoke({
                    "file_paths": file_paths,
                    "query": query,
                    "describe_image": describe_image,
                    "size_threshold_mb": size_threshold_mb,
                    "max_results": max_results
                })
                return result
                
            else:
                logger.warning(f"未知的多模态工具: {tool_name}")
                return f"未知的多模态工具: {tool_name}"
            
        except Exception as e:
            logger.error(f"执行多模态工具 {tool_name} 失败: {e}")
            return None
    
    def _build_graph(self):
        """构建LangGraph图"""
        # 创建状态图
        workflow = StateGraph(TextMultimodalAgentState)
        
        # 创建节点
        call_model = self._create_call_model_node()
        should_continue = self._create_should_continue_node()
        execute_tools = self._create_execute_tools_node()
        context_summarization = self._create_context_summarization_node()
        
        # 添加节点
        if self.enable_context_summarization and self.context_summarizer:
            # 启用上下文总结时，添加总结节点
            workflow.add_node("context_summarization", context_summarization)
            workflow.add_node("multimodal_agent", call_model)
            workflow.add_node("execute_tools", execute_tools)
            
            # 设置入口点为上下文总结节点
            workflow.set_entry_point("context_summarization")
            
            # 上下文总结后直接进入agent节点
            workflow.add_edge("context_summarization", "multimodal_agent")
        else:
            # 未启用上下文总结时，直接使用agent节点
            workflow.add_node("multimodal_agent", call_model)
            workflow.add_node("execute_tools", execute_tools)
            
            # 设置入口点为agent节点
            workflow.set_entry_point("multimodal_agent")
        
        # 添加条件边
        workflow.add_conditional_edges(
            "multimodal_agent",
            should_continue,
            {
                "execute_tools": "execute_tools",
                END: END
            }
        )
        
        # 添加execute_tools后的条件判断
        if self.enable_context_summarization and self.context_summarizer:
            # 启用上下文总结时，工具执行后回到上下文总结节点
            def should_continue_after_tools(state: TextMultimodalAgentState) -> Literal["context_summarization", END]:
                """工具执行后判断是否继续循环"""
                if state.get("handoff_executed", False):
                    logger.info("多模态Agent检测到handoff已执行，直接结束")
                    return END
                else:
                    logger.info("多模态Agent工具执行完成，返回上下文总结节点")
                    return "context_summarization"
            
            # 工具执行完成后的条件边
            workflow.add_conditional_edges(
                "execute_tools",
                should_continue_after_tools,
                {
                    "context_summarization": "context_summarization",
                    END: END
                }
            )
        else:
            # 未启用上下文总结时，工具执行后直接回到agent节点
            def should_continue_after_tools(state: TextMultimodalAgentState) -> Literal["agent", END]:
                """工具执行后判断是否继续循环"""
                if state.get("handoff_executed", False):
                    logger.info("多模态Agent检测到handoff已执行，直接结束")
                    return END
                else:
                    logger.info("多模态Agent工具执行完成，继续循环")
                    return "multimodal_agent"
            
            # 工具执行完成后的条件边
            workflow.add_conditional_edges(
                "execute_tools",
                should_continue_after_tools,
                {
                    "multimodal_agent": "multimodal_agent",
                    END: END
                }
            )
        
        # 编译图
        graph = workflow.compile(checkpointer=self.checkpointer)
        
        logger.info("文本ReAct多模态Agent图构建完成")
        return graph
    
    async def ainvoke(self, input_data: Dict[str, Any], config: Optional[Dict] = None) -> Dict[str, Any]:
        """异步调用Agent"""
        try:
            # 构建图
            graph = self._build_graph()
            
            # 准备状态
            messages = input_data.get("messages", [])
            state = TextMultimodalAgentState(
                messages=messages,
                model_desc=self.model_desc,
                iteration_count=0,
                handoff_executed=False,
                running_summary=None
            )
            
            # 执行图
            result = await graph.ainvoke(state, config)
            
            return {"messages": result["messages"]}
            
        except Exception as e:
            logger.error(f"文本ReAct多模态Agent执行失败: {e}")
            error_message = AIMessage(
                content=f"处理请求时出错: {str(e)}",
                name="multimodal_agent"
            )
            return {"messages": [error_message]}
    
    def invoke(self, input_data: Dict[str, Any], config: Optional[Dict] = None) -> Dict[str, Any]:
        """同步调用Agent"""
        return asyncio.run(self.ainvoke(input_data, config))
    
    async def get_graph(self):
        """获取图对象，用于可视化或嵌入父图"""
        return self._build_graph()


async def create_text_multimodal_agent_react(
    llm: ChatOpenAI,
    model_desc: str = "DeepSeek",
    checkpointer: Any = None,
    handoff_tools: List[Any] = None,
    max_iterations: int = 10,
    enable_context_summarization: bool = True,
    uploaded_files: Optional[List[UploadedFileInfo]] = None
) -> TextMultimodalAgentReAct:
    """创建基于文本解析的ReAct循环多模态Agent
    
    Args:
        llm: ChatOpenAI模型实例
        model_desc: 模型描述
        checkpointer: 检查点保存器
        handoff_tools: handoff工具列表（用于Swarm模式）
        max_iterations: 最大迭代次数
        enable_context_summarization: 是否启用上下文总结
        
    Returns:
        文本ReAct多模态Agent实例
    """
    try:
        logger.info(f"创建文本ReAct多模态Agent，上下文总结: {enable_context_summarization}")
        
        agent = TextMultimodalAgentReAct(
            llm=llm,
            model_desc=model_desc,
            handoff_tools=handoff_tools,
            max_iterations=max_iterations,
            checkpointer=checkpointer,
            enable_context_summarization=enable_context_summarization,
            uploaded_files=uploaded_files
        )
        
        logger.info("文本ReAct多模态Agent创建成功")
        return agent
        
    except Exception as e:
        logger.error(f"创建文本ReAct多模态Agent失败: {e}")
        raise