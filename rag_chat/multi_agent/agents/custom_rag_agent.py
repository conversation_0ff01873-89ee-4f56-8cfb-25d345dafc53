"""
自定义RAG知识库专家Agent

专门处理用户指定的自定义知识库查询。
"""

import os
from typing import Any, Dict, Optional, List
from langchain_openai import ChatOpenAI
from langgraph.prebuilt import create_react_agent
from langgraph.prebuilt.chat_agent_executor import AgentState

import sys
sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))
from logger import logger
from jiliang_chat_prompt import CUSTOM_RAG_AGENT_PROMPT
from jiliang_chat_tools import create_custom_rag_search_tool
from context_summarizer import create_summarization_pre_model_hook, extend_state_for_summarization


async def create_custom_rag_agent(
    llm: ChatOpenAI,
    custom_rag_ids: List[str],
    model_desc: str = "DeepSeek",
    checkpointer: Any = None,
    handoff_tools: List[Any] = None,
    enable_context_summarization: bool = True
) -> Any:
    """创建自定义RAG知识库专家Agent
    
    专门处理用户指定的自定义知识库查询。
    
    Args:
        llm: ChatOpenAI模型实例
        custom_rag_ids: 自定义RAG知识库ID列表
        model_desc: 模型描述
        checkpointer: 检查点保存器
        handoff_tools: handoff工具列表（用于Swarm模式）
        enable_context_summarization: 是否启用上下文总结
        
    Returns:
        自定义RAG专家Agent实例
    """
    try:
        logger.info(f"创建自定义RAG专家Agent，知识库: {custom_rag_ids}")
        
        # 格式化提示词
        prompt = CUSTOM_RAG_AGENT_PROMPT.format(model_desc=model_desc)
        
        # 自定义RAG Agent的工具集
        tools = []
        
        # 创建自定义RAG搜索工具
        if custom_rag_ids:
            custom_rag_tool = create_custom_rag_search_tool(custom_rag_ids)
            tools.append(custom_rag_tool)
            logger.info(f"已配置自定义RAG搜索工具，支持{len(custom_rag_ids)}个知识库")
        else:
            logger.warning("未提供自定义RAG知识库ID，Agent将无法执行搜索")
        
        # 添加handoff工具
        if handoff_tools:
            tools.extend(handoff_tools)
        
        # 创建上下文总结钩子（根据参数决定是否启用）
        pre_model_hook = None
        state_schema = AgentState
        
        if enable_context_summarization:
            pre_model_hook = create_summarization_pre_model_hook()
            
            # 扩展状态模式
            @extend_state_for_summarization
            class ExtendedAgentState(AgentState):
                pass
            
            state_schema = ExtendedAgentState
        
        # 创建自定义RAG专家Agent
        agent = create_react_agent(
            model=llm,
            tools=tools,
            prompt=prompt,
            pre_model_hook=pre_model_hook,
            state_schema=state_schema,
            checkpointer=checkpointer,
            name="custom_rag_agent"
        )
        
        logger.info("自定义RAG专家Agent创建成功")
        return agent
        
    except Exception as e:
        logger.error(f"创建自定义RAG专家Agent失败: {e}")
        raise