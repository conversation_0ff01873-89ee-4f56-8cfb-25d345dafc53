"""
文本模式多模态专家Agent

基于文本解析的多模态专家Agent，专门处理文件、图像和多媒体内容分析，不依赖function calling。
"""

import os
import sys
from typing import Any, Dict, Optional, List
from langchain_openai import ChatOpenAI
from langchain_core.messages import SystemMessage, HumanMessage, AIMessage
from langchain_core.output_parsers import StrOutputParser
from langchain_core.prompts import ChatPromptTemplate
import json

sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))
from logger import logger
from jiliang_chat_prompt import MULTIMODAL_AGENT_PROMPT
from jiliang_chat_tools import search_knowledge_base
from ..text_parser import text_parser, ParsedActionType, ToolCall


class TextMultimodalAgent:
    """基于文本解析的多模态专家Agent"""
    
    def __init__(self, 
                 llm: ChatOpenAI,
                 model_desc: str = "DeepSeek",
                 handoff_tools: List[Any] = None):
        """初始化文本模式多模态专家Agent
        
        Args:
            llm: ChatOpenAI模型实例
            model_desc: 模型描述
            handoff_tools: handoff工具列表（用于Swarm模式）
        """
        self.llm = llm
        self.model_desc = model_desc
        self.handoff_tools = handoff_tools or []
        
        # 构建系统提示词
        self.system_prompt = MULTIMODAL_AGENT_PROMPT.format(model_desc=model_desc)
        
        # 添加多模态工具使用指令
        tool_instructions = []
        
        # 添加基础搜索工具指令
        search_instruction = text_parser.format_tool_call_prompt(
            tool_name="search_knowledge_base",
            description="搜索三峡集团知识库以获取相关信息",
            args_schema={"query": "搜索查询词", "max_results": 5}
        )
        tool_instructions.append(search_instruction)
        
        # 添加多模态工具指令
        multimodal_tools = self._get_multimodal_tool_definitions()
        for tool_def in multimodal_tools:
            tool_instruction = text_parser.format_tool_call_prompt(
                tool_name=tool_def["name"],
                description=tool_def["description"],
                args_schema=tool_def["args_schema"]
            )
            tool_instructions.append(tool_instruction)
        
        # 合并工具指令到系统提示词
        if tool_instructions:
            self.system_prompt += "\n\n可用工具:\n" + "\n\n".join(tool_instructions)
        
        # 如果有handoff工具，添加相关指令
        if self.handoff_tools:
            available_agents = [tool.name.replace("transfer_to_", "") for tool in self.handoff_tools]
            handoff_instruction = text_parser.format_handoff_prompt(available_agents)
            self.system_prompt += "\n\n" + handoff_instruction
        
        # 创建聊天模板
        self.prompt_template = ChatPromptTemplate.from_messages([
            ("system", self.system_prompt),
            ("placeholder", "{messages}")
        ])
        
        # 创建处理链
        self.chain = self.prompt_template | self.llm | StrOutputParser()
        
        logger.info(f"文本模式多模态专家Agent初始化完成，handoff工具数量: {len(self.handoff_tools)}")
    
    def _get_multimodal_tool_definitions(self) -> List[Dict[str, Any]]:
        """获取多模态工具定义列表
        
        TODO: 根据实际需求添加具体的多模态工具定义
        """
        return [
            # TODO: 添加具体的多模态工具定义
            # 例如：
            # {
            #     "name": "process_image",
            #     "description": "处理和分析图像文件",
            #     "args_schema": {"image_path": "图像文件路径"}
            # }
        ]
    
    async def ainvoke(self, input_data: Dict[str, Any], config: Optional[Dict] = None) -> Dict[str, Any]:
        """异步调用Agent
        
        Args:
            input_data: 输入数据，包含messages字段
            config: 配置信息
            
        Returns:
            包含messages字段的结果字典
        """
        try:
            messages = input_data.get("messages", [])
            
            # 调用LLM生成响应
            response_text = await self.chain.ainvoke(
                {"messages": messages},
                config=config
            )
            
            # 解析文本输出
            parsed_actions = text_parser.parse_text(response_text)
            
            # 处理解析结果
            new_messages = []
            
            for action in parsed_actions:
                if action.action_type == ParsedActionType.THINKING:
                    # 思考内容不添加到消息流
                    logger.debug(f"多模态Agent思考: {action.content}")
                    continue
                    
                elif action.action_type == ParsedActionType.TOOL_CALL:
                    # 处理工具调用
                    tool_data = action.metadata.get("tool_call")
                    if tool_data:
                        tool_name = tool_data["name"]
                        tool_args = tool_data["args"]
                        
                        try:
                            if tool_name == "search_knowledge_base":
                                # 执行知识库搜索
                                search_result = await search_knowledge_base.ainvoke(tool_args)
                                
                                # 添加工具调用记录
                                tool_call_msg = AIMessage(
                                    content="",
                                    additional_kwargs={
                                        "tool_calls": [{
                                            "id": "text_tool_call",
                                            "function": {
                                                "name": "search_knowledge_base",
                                                "arguments": json.dumps(tool_args)
                                            },
                                            "type": "function"
                                        }]
                                    }
                                )
                                new_messages.append(tool_call_msg)
                                
                                # 生成基于搜索结果的回复
                                if search_result:
                                    knowledge_response = f"基于知识库搜索，我找到了以下相关信息：\n\n{search_result}"
                                else:
                                    knowledge_response = "抱歉，在知识库中没有找到相关信息。"
                                
                                new_messages.append(AIMessage(content=knowledge_response))
                                logger.info(f"多模态Agent完成知识库搜索")
                                
                            else:
                                # 处理多模态工具调用
                                multimodal_result = await self._execute_multimodal_tool(tool_name, tool_args)
                                
                                # 添加工具调用记录
                                tool_call_msg = AIMessage(
                                    content="",
                                    additional_kwargs={
                                        "tool_calls": [{
                                            "id": "text_multimodal_call",
                                            "function": {
                                                "name": tool_name,
                                                "arguments": json.dumps(tool_args)
                                            },
                                            "type": "function"
                                        }]
                                    }
                                )
                                new_messages.append(tool_call_msg)
                                
                                # 生成基于多模态工具结果的回复
                                if multimodal_result:
                                    multimodal_response = f"使用{tool_name}工具处理结果：\n\n{multimodal_result}"
                                else:
                                    multimodal_response = f"使用{tool_name}工具处理完成，但未获得结果。"
                                
                                new_messages.append(AIMessage(content=multimodal_response))
                                logger.info(f"多模态Agent完成工具调用: {tool_name}")
                                
                        except Exception as tool_error:
                            logger.error(f"工具调用失败 {tool_name}: {tool_error}")
                            error_response = f"处理多模态内容时遇到问题：{str(tool_error)}"
                            new_messages.append(AIMessage(content=error_response))
                    else:
                        # 工具调用解析失败
                        logger.warning(f"多模态Agent工具调用解析失败")
                        new_messages.append(AIMessage(content="收到了格式不正确的工具调用请求。"))
                        
                elif action.action_type == ParsedActionType.AGENT_HANDOFF:
                    # 处理Agent转交
                    handoff_data = action.metadata.get("handoff")
                    if handoff_data:
                        target_agent = handoff_data["target_agent"]
                        task_description = handoff_data["task_description"]
                        
                        # 生成转交消息
                        handoff_message = f"根据多模态内容分析，我将这个任务转交给{target_agent}代理来处理：{task_description}"
                        new_messages.append(AIMessage(content=handoff_message))
                        
                        logger.info(f"多模态Agent转交任务给{target_agent}: {task_description}")
                    else:
                        # 如果解析失败，使用原始内容
                        new_messages.append(AIMessage(content=action.content))
                        
                elif action.action_type == ParsedActionType.FINAL_RESPONSE:
                    # 最终回复
                    clean_content = text_parser.clean_text_output(action.content)
                    if clean_content.strip():
                        new_messages.append(AIMessage(content=clean_content))
                        
                else:
                    # 其他类型的动作
                    logger.warning(f"多模态Agent出现意外的动作类型: {action.action_type}")
                    clean_content = text_parser.clean_text_output(action.content)
                    if clean_content.strip():
                        new_messages.append(AIMessage(content=clean_content))
            
            # 如果没有生成任何消息，添加一个默认回复
            if not new_messages:
                clean_response = text_parser.clean_text_output(response_text)
                if clean_response.strip():
                    new_messages.append(AIMessage(content=clean_response))
                else:
                    new_messages.append(AIMessage(content="我理解了您的多模态处理需求，但暂时无法提供具体处理结果。"))
            
            return {"messages": new_messages}
            
        except Exception as e:
            logger.error(f"文本模式多模态专家Agent执行失败: {e}")
            error_message = AIMessage(content=f"处理多模态内容时遇到问题：{str(e)}")
            return {"messages": [error_message]}
    
    async def _execute_multimodal_tool(self, tool_name: str, tool_args: Dict[str, Any]) -> str:
        """执行多模态工具
        
        TODO: 实现具体的多模态工具调用逻辑
        
        Args:
            tool_name: 工具名称
            tool_args: 工具参数
            
        Returns:
            工具执行结果
        """
        # TODO: 根据tool_name调用相应的多模态处理工具
        # 例如：
        # if tool_name == "process_image":
        #     return await process_image(**tool_args)
        # elif tool_name == "extract_text_ocr":
        #     return await extract_text_ocr(**tool_args)
        # ...
        
        logger.warning(f"多模态工具 {tool_name} 尚未实现")
        return f"多模态工具 {tool_name} 处理完成（模拟结果）"
    
    def invoke(self, input_data: Dict[str, Any], config: Optional[Dict] = None) -> Dict[str, Any]:
        """同步调用Agent（实际使用异步实现）"""
        import asyncio
        return asyncio.run(self.ainvoke(input_data, config))


async def create_text_multimodal_agent(
    llm: ChatOpenAI,
    model_desc: str = "DeepSeek",
    checkpointer: Any = None,
    handoff_tools: List[Any] = None,
    enable_context_summarization: bool = True
) -> TextMultimodalAgent:
    """创建文本模式多模态专家Agent
    
    Args:
        llm: ChatOpenAI模型实例
        model_desc: 模型描述
        checkpointer: 检查点保存器（文本模式暂不支持）
        handoff_tools: handoff工具列表（用于Swarm模式）
        enable_context_summarization: 是否启用上下文总结（传统模式暂不支持）
        
    Returns:
        文本模式多模态专家Agent实例
    """
    try:
        logger.info("创建文本模式多模态专家Agent")
        
        agent = TextMultimodalAgent(
            llm=llm,
            model_desc=model_desc,
            handoff_tools=handoff_tools
        )
        
        logger.info("文本模式多模态专家Agent创建成功")
        return agent
        
    except Exception as e:
        logger.error(f"创建文本模式多模态专家Agent失败: {e}")
        raise