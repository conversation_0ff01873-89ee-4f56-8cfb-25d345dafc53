"""
简单问答Agent

专门处理日常对话和简单问答任务，使用无工具的create_react_agent支持上下文压缩。
"""

from typing import Any, Dict, Optional, List
from langchain_openai import ChatOpenAI
from langgraph.prebuilt import create_react_agent
from langgraph.prebuilt.chat_agent_executor import AgentState

from logger import logger
from jiliang_chat_prompt import SIMPLE_CHAT_PROMPT
from context_summarizer import create_summarization_pre_model_hook, extend_state_for_summarization


async def create_simple_chat_agent(
    llm: ChatOpenAI,
    model_desc: str = "DeepSeek",
    checkpointer: Any = None,
    handoff_tools: List[Any] = None,
    enable_context_summarization: bool = True
) -> Any:
    """创建简单问答Agent
    
    专门处理日常对话和简单问答任务，不使用特殊工具，支持上下文压缩。
    
    Args:
        llm: ChatOpenAI模型实例
        model_desc: 模型描述
        checkpointer: 检查点保存器
        handoff_tools: handoff工具列表（用于Swarm模式）
        enable_context_summarization: 是否启用上下文总结
        
    Returns:
        简单问答Agent实例
    """
    try:
        logger.info("创建简单问答Agent")
        
        # 格式化提示词
        prompt = SIMPLE_CHAT_PROMPT.format(model_desc=model_desc)
        
        # 简单问答Agent的工具集（无工具 + handoff工具）
        tools = handoff_tools if handoff_tools else []
        
        # 创建上下文总结钩子（根据参数决定是否启用）
        pre_model_hook = None
        state_schema = AgentState
        
        if enable_context_summarization:
            pre_model_hook = create_summarization_pre_model_hook()
            
            # 扩展状态模式
            @extend_state_for_summarization
            class ExtendedAgentState(AgentState):
                pass
            
            state_schema = ExtendedAgentState
        
        # 创建简单问答Agent
        agent = create_react_agent(
            model=llm,
            tools=tools,
            prompt=prompt,
            pre_model_hook=pre_model_hook,
            state_schema=state_schema,
            checkpointer=checkpointer,
            name="simple_chat"
        )
        
        logger.info("简单问答Agent创建成功")
        return agent
        
    except Exception as e:
        logger.error(f"创建简单问答Agent失败: {e}")
        raise