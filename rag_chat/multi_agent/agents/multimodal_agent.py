"""
多模态专家Agent

专门处理文件、图像和多媒体内容分析。
"""

from typing import Any, Dict, List, Optional
from langchain_openai import ChatOpenAI
from langgraph.prebuilt import create_react_agent
from langgraph.prebuilt.chat_agent_executor import AgentState
import os
import sys
sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))
from logger import logger
from jiliang_chat_prompt import MULTIMODAL_AGENT_PROMPT
from jiliang_chat_tools import search_knowledge_base, search_document_with_rag, read_document_directly, smart_document_search
from context_summarizer import create_summarization_pre_model_hook, extend_state_for_summarization


async def create_multimodal_agent(
    llm: ChatOpenAI,
    model_desc: str = "DeepSeek",
    checkpointer: Any = None,
    handoff_tools: List[Any] = None,
    enable_context_summarization: bool = True
) -> Any:
    """创建多模态专家Agent
    
    专门处理文件、图像和多媒体内容分析。
    
    Args:
        llm: ChatOpenAI模型实例
        model_desc: 模型描述
        checkpointer: 检查点保存器
        handoff_tools: handoff工具列表（用于Swarm模式）
        enable_context_summarization: 是否启用上下文总结
        
    Returns:
        多模态专家Agent实例
    """
    try:
        logger.info("创建多模态专家Agent")
        
        # 格式化提示词
        prompt = MULTIMODAL_AGENT_PROMPT.format(model_desc=model_desc)
        
        # 多模态Agent的工具集
        tools = [
            search_knowledge_base,           # 知识库搜索
            search_document_with_rag,        # RAG文档搜索
            read_document_directly,          # 直接文档读取
            smart_document_search,           # 智能混合策略搜索
            # TODO: 未来添加更多多模态处理工具
            # process_image_tool,
            # extract_text_tool,
            # ocr_tool,
            # handwriting_recognition_tool
        ]
        if handoff_tools:
            tools.extend(handoff_tools)
        
        # 创建上下文总结钩子（根据参数决定是否启用）
        pre_model_hook = None
        state_schema = AgentState
        
        if enable_context_summarization:
            pre_model_hook = create_summarization_pre_model_hook()
            
            # 扩展状态模式
            @extend_state_for_summarization
            class ExtendedAgentState(AgentState):
                pass
            
            state_schema = ExtendedAgentState
        
        # 创建多模态专家Agent
        agent = create_react_agent(
            model=llm,
            tools=tools,
            prompt=prompt,
            pre_model_hook=pre_model_hook,
            state_schema=state_schema,
            checkpointer=checkpointer,
            name="multimodal_agent"
        )
        
        logger.info(f"多模态专家Agent创建成功，包含{len(handoff_tools) if handoff_tools else 0}个handoff工具")
        return agent
        
    except Exception as e:
        logger.error(f"创建多模态专家Agent失败: {e}")
        raise


def _get_multimodal_tools() -> List[Any]:
    """获取多模态处理工具列表
    
    TODO: 实现具体的多模态工具
    - 图片处理工具
    - 文档解析工具
    - OCR识别工具
    - 手写识别工具
    - 表格提取工具
    
    Returns:
        多模态工具列表
    """
    tools = []
    
    # TODO: 添加实际的多模态工具实现
    # tools.extend([
    #     process_image_tool,
    #     process_document_tool,
    #     extract_text_tool,
    #     ocr_tool,
    #     handwriting_recognition_tool,
    #     table_extraction_tool
    # ])
    
    return tools