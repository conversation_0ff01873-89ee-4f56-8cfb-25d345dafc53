"""
MCP工具专家Agent

专门处理外部工具调用和复杂计算任务。
"""

from typing import Any, Dict, List, Optional
from langchain_openai import ChatOpenAI
from langgraph.prebuilt import create_react_agent
from langgraph.prebuilt.chat_agent_executor import AgentState
import os
import sys
sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))
from logger import logger
from jiliang_chat_prompt import MCP_AGENT_PROMPT
from jiliang_chat_tools import search_knowledge_base
from context_summarizer import create_summarization_pre_model_hook, extend_state_for_summarization


async def create_mcp_agent(
    llm: ChatOpenAI,
    question: str,
    mcp_ids: List[str],
    model_desc: str = "DeepSeek",
    checkpointer: Any = None,
    mcp_router: Any = None,
    mcp_simple_router: Any = None,
    handoff_tools: List[Any] = None,
    enable_context_summarization: bool = True,
    api_key: str = None,
    username: str = None
) -> Any:
    """创建MCP工具专家Agent
    
    专门处理外部工具调用和复杂计算任务。
    
    Args:
        llm: ChatOpenAI模型实例
        question: 用户问题（用于动态工具选择）
        mcp_ids: MCP工具ID列表
        model_desc: 模型描述
        checkpointer: 检查点保存器
        mcp_router: MCP RAG路由器
        mcp_simple_router: MCP简单路由器
        handoff_tools: handoff工具列表（用于Swarm模式）
        enable_context_summarization: 是否启用上下文总结
        
    Returns:
        MCP工具专家Agent实例
    """
    try:
        logger.info(f"创建MCP工具专家Agent，MCP IDs: {mcp_ids}")
        
        # 格式化提示词
        prompt = MCP_AGENT_PROMPT.format(model_desc=model_desc)
        
        # 动态获取MCP工具
        mcp_tools = await _get_mcp_tools(question, mcp_ids, mcp_router, mcp_simple_router, api_key, username)
        
        # MCP Agent的工具集（包含基础检索工具）
        tools = [search_knowledge_base]
        if mcp_tools:
            tools.extend(mcp_tools)
        if handoff_tools:
            tools.extend(handoff_tools)
        
        # 创建上下文总结钩子（根据参数决定是否启用）
        pre_model_hook = None
        state_schema = AgentState
        
        if enable_context_summarization:
            pre_model_hook = create_summarization_pre_model_hook()
            
            # 扩展状态模式
            @extend_state_for_summarization
            class ExtendedAgentState(AgentState):
                pass
            
            state_schema = ExtendedAgentState
        
        # 创建MCP专家Agent
        agent = create_react_agent(
            model=llm,
            tools=tools,
            prompt=prompt,
            pre_model_hook=pre_model_hook,
            state_schema=state_schema,
            checkpointer=checkpointer,
            name="mcp_agent"
        )
        
        logger.info(f"MCP工具专家Agent创建成功，加载了{len(mcp_tools)}个MCP工具，{len(handoff_tools) if handoff_tools else 0}个handoff工具")
        return agent
        
    except Exception as e:
        logger.error(f"创建MCP工具专家Agent失败: {e}")
        raise


async def _get_mcp_tools(
    question: str,
    mcp_ids: List[str],
    mcp_router: Any = None,
    mcp_simple_router: Any = None,
    api_key: str = None,
    username: str = None
) -> List[Any]:
    """获取MCP工具列表
    
    Args:
        question: 用户问题
        mcp_ids: MCP工具ID列表
        mcp_router: MCP RAG路由器
        mcp_simple_router: MCP简单路由器
        api_key: API密钥（从请求头获取）
        username: 用户名（从请求头获取）
        
    Returns:
        MCP工具列表
    """
    mcp_tools = []
    
    try:
        # 获取MCP路由器类型
        mcp_router_type = os.getenv('MCP_ROUTER_TYPE', 'rag').lower()
        
        if mcp_router_type == 'rag' and mcp_router:
            # 使用RAG路由器动态选择工具
            mcp_tools = await mcp_router.get_dynamic_mcp_tools(question, top_k=2)
            logger.info(f"RAG路由器为问题 '{question[:50]}...' 动态加载了 {len(mcp_tools)} 个MCP工具")
            
        elif mcp_router_type == 'simple' and mcp_simple_router:
            # 使用简单路由器
            if mcp_ids:
                success = await mcp_simple_router.initialize_for_user(
                    mcp_ids, 
                    api_key=api_key, 
                    username=username
                )
                if success:
                    mcp_tools = await mcp_simple_router.get_dynamic_mcp_tools(question)
                    logger.info(f"Simple路由器为 {mcp_ids} 加载了 {len(mcp_tools)} 个MCP工具")
                else:
                    logger.warning(f"Simple路由器为 {mcp_ids} 初始化失败")
            else:
                logger.warning("Simple路由器需要mcp_ids，但未提供")
        
    except Exception as e:
        logger.error(f"获取MCP工具失败: {e}")
    
    return mcp_tools