"""
多智能体系统 - 单一Agent模块

包含各种专门化的Agent实现，支持传统版本和ReAct循环版本。
"""

# 传统版本（单次调用）
from .simple_chat_agent import create_simple_chat_agent
from .knowledge_agent import create_knowledge_agent
from .mcp_agent import create_mcp_agent
from .multimodal_agent import create_multimodal_agent
from .custom_rag_agent import create_custom_rag_agent

# 文本模式版本（基于文本解析）
from .text_simple_chat_agent import create_text_simple_chat_agent
from .text_knowledge_agent import create_text_knowledge_agent
from .text_mcp_agent import create_text_mcp_agent
from .text_multimodal_agent import create_text_multimodal_agent

# ReAct循环版本（真正的LangGraph ReAct实现）
from .text_knowledge_agent_react import create_text_knowledge_agent_react
from .text_mcp_agent_react import create_text_mcp_agent_react
from .text_multimodal_agent_react import create_text_multimodal_agent_react
from .text_custom_rag_agent_react import create_text_custom_rag_agent_react

__all__ = [
    # 传统版本
    "create_simple_chat_agent",
    "create_knowledge_agent",
    "create_mcp_agent", 
    "create_multimodal_agent",
    "create_custom_rag_agent",
    # 文本模式版本
    "create_text_simple_chat_agent",
    "create_text_knowledge_agent",
    "create_text_mcp_agent",
    "create_text_multimodal_agent",
    # ReAct循环版本
    "create_text_knowledge_agent_react",
    "create_text_mcp_agent_react",
    "create_text_multimodal_agent_react",
    "create_text_custom_rag_agent_react"
]