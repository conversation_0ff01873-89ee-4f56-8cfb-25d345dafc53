"""
知识专家Agent

专门处理三峡集团相关的知识查询和业务问答。
"""

import os
from typing import Any, Dict, Optional, List
from langchain_openai import ChatOpenAI
from langgraph.prebuilt import create_react_agent
from langgraph.prebuilt.chat_agent_executor import AgentState

import sys
sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))
from logger import logger
from jiliang_chat_prompt import KNOWLEDGE_AGENT_PROMPT
from jiliang_chat_tools import search_knowledge_base
from context_summarizer import create_summarization_pre_model_hook, extend_state_for_summarization


async def create_knowledge_agent(
    llm: ChatOpenAI,
    model_desc: str = "DeepSeek",
    checkpointer: Any = None,
    handoff_tools: List[Any] = None,
    enable_context_summarization: bool = True
) -> Any:
    """创建知识专家Agent
    
    专门处理三峡集团相关的知识查询和业务问答。
    
    Args:
        llm: ChatOpenAI模型实例
        model_desc: 模型描述
        checkpointer: 检查点保存器
        handoff_tools: handoff工具列表（用于Swarm模式）
        enable_context_summarization: 是否启用上下文总结
        
    Returns:
        知识专家Agent实例
    """
    try:
        logger.info("创建知识专家Agent")
        
        # 格式化提示词
        prompt = KNOWLEDGE_AGENT_PROMPT.format(model_desc=model_desc)
        
        # 知识Agent的工具集
        tools = [search_knowledge_base]
        if handoff_tools:
            tools.extend(handoff_tools)
        
        # 创建上下文总结钩子（根据参数决定是否启用）
        pre_model_hook = None
        state_schema = AgentState
        
        if enable_context_summarization:
            pre_model_hook = create_summarization_pre_model_hook()
            
            # 扩展状态模式
            @extend_state_for_summarization
            class ExtendedAgentState(AgentState):
                pass
            
            state_schema = ExtendedAgentState
        
        # 创建知识专家Agent
        agent = create_react_agent(
            model=llm,
            tools=tools,
            prompt=prompt,
            pre_model_hook=pre_model_hook,
            state_schema=state_schema,
            checkpointer=checkpointer,
            name="knowledge_agent"
        )
        
        logger.info("知识专家Agent创建成功")
        return agent
        
    except Exception as e:
        logger.error(f"创建知识专家Agent失败: {e}")
        raise