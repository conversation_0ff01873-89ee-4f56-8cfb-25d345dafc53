import asyncio
from typing import Any, Dict, Optional, Type, List, Union
from pydantic import BaseModel, ValidationError
from langchain_core.messages import HumanMessage, SystemMessage, AIMessage
from langchain_core.language_models.base import BaseLanguageModel
from loguru import logger
import traceback

from .json_repair_custom import safe_structured_output, JSONRepairError, JSONRepairer


class StructuredOutputConfig:
    """结构化输出配置类"""
    
    def __init__(
        self,
        max_repair_attempts: int = 3,
        max_llm_retries: int = 2,
        enable_fallback: bool = True,
        enable_logging: bool = True,
        timeout_seconds: int = 60
    ):
        self.max_repair_attempts = max_repair_attempts
        self.max_llm_retries = max_llm_retries
        self.enable_fallback = enable_fallback
        self.enable_logging = enable_logging
        self.timeout_seconds = timeout_seconds


class StructuredOutputHelper:
    """结构化输出助手类"""
    
    def __init__(self, config: Optional[StructuredOutputConfig] = None):
        self.config = config or StructuredOutputConfig()
        self.repairer = JSONRepairer()
    
    def safe_invoke(
        self,
        llm: BaseLanguageModel,
        schema: Type[BaseModel],
        messages: List[Union[HumanMessage, SystemMessage, AIMessage]],
        config: Optional[Dict] = None
    ) -> Dict[str, Any]:
        """
        安全调用结构化输出
        
        Args:
            llm: 语言模型实例
            schema: Pydantic模型类
            messages: 消息列表
            config: LangChain配置字典
            
        Returns:
            包含parsed、raw、repaired等信息的字典
        """
        try:
            return safe_structured_output(
                llm=llm,
                schema=schema,
                messages=messages,
                config=config,
                max_repair_attempts=self.config.max_repair_attempts,
                max_llm_retries=self.config.max_llm_retries
            )
        except JSONRepairError as e:
            if self.config.enable_fallback:
                return self._fallback_strategy(llm, schema, messages, config, e)
            else:
                raise
        except Exception as e:
            logger.error(f"结构化输出调用失败: {e}")
            if self.config.enable_logging:
                logger.error(traceback.format_exc())
            raise
    
    async def safe_ainvoke(
        self,
        llm: BaseLanguageModel,
        schema: Type[BaseModel],
        messages: List[Union[HumanMessage, SystemMessage, AIMessage]],
        config: Optional[Dict] = None
    ) -> Dict[str, Any]:
        """
        异步安全调用结构化输出
        """
        try:
            # 使用asyncio.wait_for添加超时控制
            return await asyncio.wait_for(
                self._async_safe_structured_output(llm, schema, messages, config),
                timeout=self.config.timeout_seconds
            )
        except asyncio.TimeoutError:
            logger.error(f"结构化输出调用超时 ({self.config.timeout_seconds}秒)")
            raise JSONRepairError("调用超时")
        except JSONRepairError as e:
            if self.config.enable_fallback:
                return await self._async_fallback_strategy(llm, schema, messages, config, e)
            else:
                raise
        except Exception as e:
            logger.error(f"异步结构化输出调用失败: {e}")
            if self.config.enable_logging:
                logger.error(traceback.format_exc())
            raise
    
    async def _async_safe_structured_output(
        self,
        llm: BaseLanguageModel,
        schema: Type[BaseModel],
        messages: List,
        config: Optional[Dict]
    ) -> Dict[str, Any]:
        """异步版本的安全结构化输出"""
        repairer = JSONRepairer()
        
        for llm_retry in range(self.config.max_llm_retries + 1):
            try:
                # 尝试使用结构化输出
                llm_with_structured = llm.with_structured_output(
                    schema, 
                    include_raw=True
                )
                
                result = await llm_with_structured.ainvoke(messages, config=config)
                
                # 如果成功，直接返回
                if result.get("parsed") is not None:
                    return result
                
                # 如果解析失败，尝试修复原始输出
                raw_content = result.get("raw", {})
                raw_text = ""
                
                # 优先从tool_calls中提取JSON数据
                if hasattr(raw_content, 'additional_kwargs') and 'tool_calls' in raw_content.additional_kwargs:
                    tool_calls = raw_content.additional_kwargs.get('tool_calls', [])
                    if tool_calls and len(tool_calls) > 0:
                        # 从第一个tool_call的arguments中提取JSON
                        arguments = tool_calls[0].get('function', {}).get('arguments', '')
                        if arguments:
                            raw_text = arguments
                            logger.info("从tool_calls中提取到JSON数据")
                
                # 如果tool_calls中没有数据，尝试从content中提取
                if not raw_text:
                    if hasattr(raw_content, 'content'):
                        raw_text = raw_content.content
                    elif isinstance(raw_content, dict) and 'content' in raw_content:
                        raw_text = raw_content['content']
                    else:
                        raw_text = str(raw_content)
                
                logger.warning(f"结构化输出解析失败，尝试修复JSON。原始输出: {raw_text[:200]}...")
                
                # 尝试修复JSON
                for repair_attempt in range(self.config.max_repair_attempts):
                    try:
                        # 提取可能的JSON内容
                        from .json_repair_custom import extract_json_from_text
                        json_content = extract_json_from_text(raw_text)
                        if not json_content:
                            raise JSONRepairError("未找到JSON内容")
                        
                        # 修复JSON
                        repaired_json = repairer.repair_json(json_content)
                        
                        # 验证修复后的JSON是否符合schema
                        parsed_result = schema(**repaired_json)
                        
                        logger.info(f"JSON修复成功，第 {repair_attempt + 1} 次尝试")
                        return {
                            "parsed": parsed_result,
                            "raw": raw_content,
                            "repaired": True
                        }
                        
                    except (JSONRepairError, ValidationError, Exception) as e:
                        logger.warning(f"JSON修复尝试 {repair_attempt + 1} 失败: {e}")
                        if repair_attempt == self.config.max_repair_attempts - 1:
                            logger.error(f"所有JSON修复尝试都失败")
                
            except Exception as e:
                logger.error(f"LLM调用失败 (尝试 {llm_retry + 1}): {e}")
                if llm_retry == self.config.max_llm_retries:
                    logger.error("所有LLM重试都失败")
                    raise
                
                # 添加重试提示
                retry_message = HumanMessage(
                    content="请重新生成，确保输出是有效的JSON格式，严格按照要求的schema结构。"
                )
                messages.append(retry_message)
        
        raise JSONRepairError("所有修复和重试尝试都失败")
    
    def _fallback_strategy(
        self,
        llm: BaseLanguageModel,
        schema: Type[BaseModel],
        messages: List,
        config: Optional[Dict],
        original_error: Exception
    ) -> Dict[str, Any]:
        """降级策略：使用更简单的方法尝试获取结构化输出"""
        logger.warning(f"使用降级策略，原始错误: {original_error}")
        
        try:
            # 策略1：直接调用LLM并手动解析
            simple_prompt = self._create_simple_extraction_prompt(schema)
            simple_messages = messages + [HumanMessage(content=simple_prompt)]
            
            response = llm.invoke(simple_messages, config=config)
            content = response.content if hasattr(response, 'content') else str(response)
            
            # 尝试提取和修复JSON
            from .json_repair_custom import extract_json_from_text
            json_content = extract_json_from_text(content)
            if json_content:
                repaired_json = self.repairer.repair_json(json_content)
                parsed_result = schema(**repaired_json)
                
                return {
                    "parsed": parsed_result,
                    "raw": response,
                    "repaired": True,
                    "fallback": True
                }
            
        except Exception as e:
            logger.error(f"降级策略也失败: {e}")
        
        # 策略2：返回部分结果或默认值
        try:
            default_instance = schema()
            return {
                "parsed": default_instance,
                "raw": None,
                "repaired": False,
                "fallback": True,
                "default": True
            }
        except Exception as e:
            logger.error(f"无法创建默认实例: {e}")
            raise JSONRepairError(f"所有策略都失败，原始错误: {original_error}")
    
    async def _async_fallback_strategy(
        self,
        llm: BaseLanguageModel,
        schema: Type[BaseModel],
        messages: List,
        config: Optional[Dict],
        original_error: Exception
    ) -> Dict[str, Any]:
        """异步降级策略"""
        logger.warning(f"使用异步降级策略，原始错误: {original_error}")
        
        try:
            # 策略1：直接调用LLM并手动解析
            simple_prompt = self._create_simple_extraction_prompt(schema)
            simple_messages = messages + [HumanMessage(content=simple_prompt)]
            
            response = await llm.ainvoke(simple_messages, config=config)
            content = response.content if hasattr(response, 'content') else str(response)
            
            # 尝试提取和修复JSON
            from .json_repair_custom import extract_json_from_text
            json_content = extract_json_from_text(content)
            if json_content:
                repaired_json = self.repairer.repair_json(json_content)
                parsed_result = schema(**repaired_json)
                
                return {
                    "parsed": parsed_result,
                    "raw": response,
                    "repaired": True,
                    "fallback": True
                }
            
        except Exception as e:
            logger.error(f"异步降级策略也失败: {e}")
        
        # 策略2：返回部分结果或默认值
        try:
            default_instance = schema()
            return {
                "parsed": default_instance,
                "raw": None,
                "repaired": False,
                "fallback": True,
                "default": True
            }
        except Exception as e:
            logger.error(f"无法创建默认实例: {e}")
            raise JSONRepairError(f"所有策略都失败，原始错误: {original_error}")
    
    def _create_simple_extraction_prompt(self, schema: Type[BaseModel]) -> str:
        """创建简单的提取提示"""
        schema_info = schema.model_json_schema()
        
        prompt = f"""
请将上述内容转换为以下JSON格式，确保输出是有效的JSON：

```json
{{
"""
        
        # 添加字段示例
        for field_name, field_info in schema_info.get("properties", {}).items():
            field_type = field_info.get("type", "string")
            description = field_info.get("description", "")
            
            if field_type == "string":
                prompt += f'  "{field_name}": "根据内容提取的{description}",\n'
            elif field_type == "array":
                prompt += f'  "{field_name}": ["根据内容提取的{description}"],\n'
            else:
                prompt += f'  "{field_name}": "根据内容提取的{description}",\n'
        
        prompt = prompt.rstrip(',\n') + '\n}\n```'
        
        return prompt

def create_safe_structured_output(
    max_repair_attempts: int = 3,
    max_llm_retries: int = 2,
    enable_fallback: bool = True,
    enable_logging: bool = True,
    timeout_seconds: int = 60
) -> StructuredOutputHelper:
    """创建结构化输出助手实例"""
    config = StructuredOutputConfig(
        max_repair_attempts=max_repair_attempts,
        max_llm_retries=max_llm_retries,
        enable_fallback=enable_fallback,
        enable_logging=enable_logging,
        timeout_seconds=timeout_seconds
    )
    return StructuredOutputHelper(config)
