import json
import re
from typing import Any, Dict, Optional, Type
from pydantic import BaseModel, ValidationError
from langchain_core.messages import HumanMessage
from loguru import logger

try:
    from json_repair import repair_json as third_party_repair
    HAS_JSON_REPAIR = True
except ImportError:
    HAS_JSON_REPAIR = False
    logger.warning("json_repair库未安装，将使用内置修复策略")


class JSONRepairError(Exception):
    """JSON修复失败时抛出的异常"""
    pass


class JSONRepairer:
    """JSON修复工具类，提供多种修复策略"""
    
    def __init__(self):
        self.repair_strategies = [
            self._basic_repair,
            self._quote_repair,
            self._bracket_repair,
            self._comma_repair,
            self._escape_repair,
            self._truncation_repair
        ]
        
        # 如果有第三方库，添加到策略列表
        if HAS_JSON_REPAIR:
            self.repair_strategies.insert(1, self._third_party_repair)
    
    def repair_json(self, json_str: str, max_attempts: Optional[int] = None) -> Dict[str, Any]:
        """
        尝试修复JSON字符串
        
        Args:
            json_str: 需要修复的JSON字符串
            max_attempts: 最大尝试次数，默认为所有策略
            
        Returns:
            修复后的JSON对象
            
        Raises:
            JSONRepairError: 所有修复策略都失败时抛出
        """
        if not json_str or not json_str.strip():
            raise JSONRepairError("输入的JSON字符串为空")
        
        # 首先尝试直接解析
        try:
            return json.loads(json_str)
        except json.JSONDecodeError:
            pass
        
        # 如果没有指定最大尝试次数，使用所有策略
        if max_attempts is None:
            max_attempts = len(self.repair_strategies)
        
        # 逐个尝试修复策略
        for i, strategy in enumerate(self.repair_strategies[:max_attempts]):
            try:
                logger.info(f"尝试修复策略 {i+1}: {strategy.__name__}")
                repaired = strategy(json_str)
                result = json.loads(repaired)
                logger.info(f"修复策略 {i+1} 成功")
                return result
            except (json.JSONDecodeError, Exception) as e:
                logger.warning(f"修复策略 {i+1} 失败: {e}")
                continue
        
        raise JSONRepairError(f"所有修复策略都失败，无法修复JSON: {json_str[:200]}...")
    
    def _third_party_repair(self, json_str: str) -> str:
        """使用第三方库进行修复"""
        if not HAS_JSON_REPAIR:
            raise Exception("第三方修复库不可用")
        
        try:
            # 先进行基础清理
            cleaned = self._basic_repair(json_str)
            # 使用第三方库修复
            repaired = third_party_repair(cleaned)
            return repaired
        except Exception as e:
            logger.warning(f"第三方修复失败: {e}")
            raise
    
    def _basic_repair(self, json_str: str) -> str:
        """基础修复：处理常见的格式问题"""
        # 移除多余的空白字符
        json_str = json_str.strip()
        
        # 移除可能的markdown代码块标记
        json_str = re.sub(r'^```json\s*', '', json_str)
        json_str = re.sub(r'\s*```$', '', json_str)
        
        # 移除可能的注释
        json_str = re.sub(r'//.*?$', '', json_str, flags=re.MULTILINE)
        json_str = re.sub(r'/\*.*?\*/', '', json_str, flags=re.DOTALL)
        
        return json_str
    
    def _quote_repair(self, json_str: str) -> str:
        """引号修复：处理单引号和引号不匹配问题"""
        # 先进行基础修复
        json_str = self._basic_repair(json_str)
        
        # 将单引号替换为双引号（但要避免字符串内容中的单引号）
        # 处理键名的单引号
        json_str = re.sub(r"'([^']*?)'(\s*:)", r'"\1"\2', json_str)
        
        # 处理值的单引号
        json_str = re.sub(r"(:\s*)'([^']*?)'(\s*[,}\]])", r'\1"\2"\3', json_str)
        
        # 处理数组中的单引号
        json_str = re.sub(r"(\[\s*)'([^']*?)'", r'\1"\2"', json_str)
        json_str = re.sub(r"(,\s*)'([^']*?)'", r'\1"\2"', json_str)
        
        return json_str
    
    def _bracket_repair(self, json_str: str) -> str:
        """括号修复：处理括号不匹配问题"""
        json_str = self._quote_repair(json_str)
        
        # 统计括号数量
        open_braces = json_str.count('{')
        close_braces = json_str.count('}')
        open_brackets = json_str.count('[')
        close_brackets = json_str.count(']')
        
        # 补充缺失的右括号
        if open_braces > close_braces:
            json_str += '}' * (open_braces - close_braces)
        elif close_braces > open_braces:
            json_str = '{' * (close_braces - open_braces) + json_str
            
        if open_brackets > close_brackets:
            json_str += ']' * (open_brackets - close_brackets)
        elif close_brackets > open_brackets:
            json_str = '[' * (close_brackets - open_brackets) + json_str
        
        return json_str
    
    def _comma_repair(self, json_str: str) -> str:
        """逗号修复：处理多余或缺失的逗号"""
        json_str = self._bracket_repair(json_str)
        
        # 移除对象和数组结束前的多余逗号
        json_str = re.sub(r',(\s*[}\]])', r'\1', json_str)
        
        # 在缺少逗号的地方添加逗号
        # 在 "value" 后面跟着 "key": 的情况
        json_str = re.sub(r'("(?:[^"\\]|\\.)*")\s*("(?:[^"\\]|\\.)*"\s*:)', r'\1,\2', json_str)
        
        # 在 } 后面跟着 "key": 的情况
        json_str = re.sub(r'}\s*("(?:[^"\\]|\\.)*"\s*:)', r'},\1', json_str)
        
        # 在 ] 后面跟着 "key": 的情况
        json_str = re.sub(r']\s*("(?:[^"\\]|\\.)*"\s*:)', r'],\1', json_str)
        
        return json_str
    
    def _escape_repair(self, json_str: str) -> str:
        """转义修复：处理字符串中的特殊字符"""
        json_str = self._comma_repair(json_str)
        
        # 修复字符串中未转义的引号
        def fix_quotes_in_strings(match):
            content = match.group(1)
            # 转义内部的双引号
            content = content.replace('"', '\\"')
            return f'"{content}"'
        
        # 匹配字符串值并修复内部引号
        json_str = re.sub(r'"([^"]*(?:\\"[^"]*)*)"', fix_quotes_in_strings, json_str)
        
        return json_str
    
    def _truncation_repair(self, json_str: str) -> str:
        """截断修复：处理不完整的JSON"""
        json_str = self._escape_repair(json_str)
        
        # 如果JSON看起来被截断了，尝试找到最后一个完整的结构
        if not json_str.endswith(('}', ']')):
            # 找到最后一个完整的键值对或数组元素
            last_complete = -1
            brace_count = 0
            bracket_count = 0
            in_string = False
            escape_next = False
            
            for i, char in enumerate(json_str):
                if escape_next:
                    escape_next = False
                    continue
                    
                if char == '\\':
                    escape_next = True
                    continue
                    
                if char == '"' and not escape_next:
                    in_string = not in_string
                    continue
                    
                if in_string:
                    continue
                    
                if char == '{':
                    brace_count += 1
                elif char == '}':
                    brace_count -= 1
                elif char == '[':
                    bracket_count += 1
                elif char == ']':
                    bracket_count -= 1
                
                # 如果找到了一个完整的结构
                if brace_count == 0 and bracket_count == 0 and char in '}]':
                    last_complete = i
            
            if last_complete > 0:
                json_str = json_str[:last_complete + 1]
        
        return json_str


def safe_structured_output(llm, schema: Type[BaseModel], messages: list, 
                          config: Optional[dict] = None, max_repair_attempts: int = 3,
                          max_llm_retries: int = 2) -> Dict[str, Any]:
    """
    安全的结构化输出函数，包含JSON修复和重试机制
    
    Args:
        llm: 语言模型实例
        schema: Pydantic模型类
        messages: 消息列表
        config: 配置字典
        max_repair_attempts: 最大JSON修复尝试次数
        max_llm_retries: 最大LLM重试次数
        
    Returns:
        包含parsed（解析结果）和raw（原始输出）的字典
    """
    repairer = JSONRepairer()
    
    for llm_retry in range(max_llm_retries + 1):
        try:
            # 尝试使用结构化输出
            llm_with_structured = llm.with_structured_output(
                schema, 
                include_raw=True
            )
            
            result = llm_with_structured.invoke(messages, config=config)
            
            # 如果成功，直接返回
            if result.get("parsed") is not None:
                return result
            
            # 如果解析失败，尝试修复原始输出
            raw_content = result.get("raw", {})
            raw_text = ""
            
            # 优先从tool_calls中提取JSON数据
            if hasattr(raw_content, 'additional_kwargs') and 'tool_calls' in raw_content.additional_kwargs:
                tool_calls = raw_content.additional_kwargs.get('tool_calls', [])
                if tool_calls and len(tool_calls) > 0:
                    # 从第一个tool_call的arguments中提取JSON
                    arguments = tool_calls[0].get('function', {}).get('arguments', '')
                    if arguments:
                        raw_text = arguments
                        logger.info("从tool_calls中提取到JSON数据")
            
            # 如果tool_calls中没有数据，尝试从content中提取
            if not raw_text:
                if hasattr(raw_content, 'content'):
                    raw_text = raw_content.content
                elif isinstance(raw_content, dict) and 'content' in raw_content:
                    raw_text = raw_content['content']
                else:
                    raw_text = str(raw_content)
            
            logger.warning(f"结构化输出解析失败，尝试修复JSON。原始输出: {raw_text[:200]}...")
            
            # 尝试修复JSON
            for repair_attempt in range(max_repair_attempts):
                try:
                    # 提取可能的JSON内容
                    json_content = extract_json_from_text(raw_text)
                    if not json_content:
                        raise JSONRepairError("未找到JSON内容")
                    
                    # 修复JSON
                    repaired_json = repairer.repair_json(json_content)
                    
                    # 验证修复后的JSON是否符合schema
                    parsed_result = schema(**repaired_json)
                    
                    logger.info(f"JSON修复成功，第 {repair_attempt + 1} 次尝试")
                    return {
                        "parsed": parsed_result,
                        "raw": raw_content,
                        "repaired": True
                    }
                    
                except (JSONRepairError, ValidationError, Exception) as e:
                    logger.warning(f"JSON修复尝试 {repair_attempt + 1} 失败: {e}")
                    if repair_attempt == max_repair_attempts - 1:
                        logger.error(f"所有JSON修复尝试都失败")
            
        except Exception as e:
            logger.error(f"LLM调用失败 (尝试 {llm_retry + 1}): {e}")
            if llm_retry == max_llm_retries:
                logger.error("所有LLM重试都失败")
                raise
            
            # 添加重试提示
            retry_message = HumanMessage(
                content="请重新生成，确保输出是有效的JSON格式，严格按照要求的schema结构。"
            )
            messages.append(retry_message)
    
    raise JSONRepairError("所有修复和重试尝试都失败")


def extract_json_from_text(text: str) -> Optional[str]:
    """从文本中提取JSON内容"""
    if not text:
        return None
    
    text = text.strip()
    
    # 移除think标签
    text = re.sub(r'<think>.*?</think>', '', text, flags=re.DOTALL)
    
    # 尝试提取markdown代码块中的JSON
    json_pattern = re.compile(r'```json\s*(\{.*?\})\s*```', re.DOTALL)
    match = json_pattern.search(text)
    if match:
        return match.group(1).strip()
    
    # 尝试提取普通代码块中的JSON
    code_pattern = re.compile(r'```\s*(\{.*?\})\s*```', re.DOTALL)
    match = code_pattern.search(text)
    if match:
        return match.group(1).strip()
    
    # 如果文本本身看起来像JSON
    if text.startswith('{') and text.endswith('}'):
        return text
    
    # 尝试找到第一个完整的JSON对象
    brace_count = 0
    start_pos = text.find('{')
    if start_pos == -1:
        return None
    
    for i in range(start_pos, len(text)):
        if text[i] == '{':
            brace_count += 1
        elif text[i] == '}':
            brace_count -= 1
            if brace_count == 0:
                return text[start_pos:i+1]
    
    return None
