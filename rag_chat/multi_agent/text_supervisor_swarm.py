"""
文本模式的LangGraph Supervisor和Swarm实现

基于LangGraph官方supervisor和swarm API，实现文本解析模式的多Agent协调。
不依赖function calling，使用结构化文本解析实现Agent间的协调。
"""

import asyncio
from typing import Any, Dict, List, Optional, Literal
from langchain_core.messages import BaseMessage, HumanMessage, AIMessage, ToolMessage
from langchain_core.tools import BaseTool, tool
from langchain_core.runnables import Runnable
from langchain_openai import ChatOpenAI
from langgraph.graph import StateGraph, MessagesState
from langgraph.prebuilt import create_react_agent
from langgraph.checkpoint.memory import MemorySaver
from pydantic import BaseModel, Field
import json
import re

from .text_parser import text_parser, ParsedActionType, AgentHandoff
from .tool_message_adapter import tool_message_adapter

import sys
import os
sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))
from logger import logger


class TextHandoffTool(BaseTool):
    """文本模式的Agent转交工具"""
    name: str = Field(default="")
    description: str = Field(default="")
    agent_name: str = Field(default="")
    
    def __init__(self, agent_name: str, description: str = None, **kwargs):
        tool_description = description or f"转交给{agent_name}代理处理"
        tool_name = f"transfer_to_{agent_name}"
        
        super().__init__(
            name=tool_name,
            description=tool_description,
            agent_name=agent_name,
            **kwargs
        )
    
    def _run(self, task_description: str = "") -> str:
        """执行转交操作"""
        handoff_text = f"[HANDOFF]\nagent: {self.agent_name}\ntask: {task_description}\n[/HANDOFF]"
        return handoff_text
    
    async def _arun(self, task_description: str = "") -> str:
        """异步执行转交操作"""
        return self._run(task_description)


def create_text_handoff_tool(agent_name: str, description: str = None) -> TextHandoffTool:
    """创建文本模式的转交工具
    
    Args:
        agent_name: 目标Agent名称
        description: 工具描述
        
    Returns:
        TextHandoffTool: 文本模式转交工具
    """
    return TextHandoffTool(agent_name=agent_name, description=description)

class TextSupervisorWrapper:
    """文本模式的监督者包装器
    
    模拟LangGraph官方supervisor的行为，但使用文本解析而不是function calling
    正确实现：有一个专门的监督者Agent做决策，然后调用专门化的Agent执行任务
    """
    
    def __init__(self, 
                 agents: List[Any], 
                 model: ChatOpenAI,
                 prompt: str = None,
                 output_mode: str = "last_message",
                 include_agent_name: str = "inline",
                 filter_agent_messages: bool = False):
        """初始化文本模式监督者
        
        Args:
            agents: Agent列表
            model: LLM模型
            prompt: 监督者提示词
            output_mode: 输出模式
            include_agent_name: 包含Agent名称的方式
            filter_agent_messages: 是否过滤Agent消息（True=只返回最后一条，False=返回全部）
        """
        self.agents = agents
        self.model = model
        self.output_mode = output_mode
        self.include_agent_name = include_agent_name
        self.filter_agent_messages = filter_agent_messages
        
        # 创建Agent名称映射
        self.agent_mapping = {}
        for i, agent in enumerate(agents):
            agent_name = getattr(agent, 'name', f"agent_{i}")
            self.agent_mapping[agent_name] = agent
        
        # 动态构建监督者的系统提示词，只包含当前可用的Agent
        agent_names = list(self.agent_mapping.keys())
        
        # 定义详细的Agent描述
        full_agent_descriptions = {
            "knowledge_agent": """### knowledge_agent (知识专家)
- **专长**: 三峡集团相关知识查询和业务问答
- **何时使用**: 查询涉及三峡、水电、能源、工程等专业领域
- **工具能力**: 权威知识库检索、专业内容分析""",
            "mcp_agent": """### mcp_agent (工具专家)  
- **专长**: 外部工具调用和复杂计算分析
- **何时使用**: 需要实时数据、API调用、计算处理
- **工具能力**: 动态MCP工具集、外部服务集成""",
            "multimodal_agent": """### multimodal_agent (多模态专家)
- **专长**: 文件和图像处理分析
- **何时使用**: 文档分析、图片识别、多媒体内容处理
- **工具能力**: OCR识别、文档解析、图像分析""",
            "simple_chat": """### simple_chat (通用助手)
- **专长**: 一般性对话和简单问答
- **何时使用**: 不需要专业工具的日常对话
- **工具能力**: 基础知识问答、对话交流""",
            "custom_rag_agent": """### custom_rag_agent (自定义知识库专家)
- **专长**: 处理用户指定的自定义知识库查询和回答
- **何时使用**: 用户提供了额外知识库ID
- **工具能力**: 用户自定义知识库检索
"""
        }
        
        # 只包含当前可用Agent的描述
        available_agent_descriptions = []
        for name in agent_names:
            if name in full_agent_descriptions:
                available_agent_descriptions.append(full_agent_descriptions[name])
            else:
                available_agent_descriptions.append(f"### {name}\n- **专长**: 专门化Agent\n- **何时使用**: 处理{name}相关任务")
        
        # 使用基础的监督者提示词模板，并动态插入可用Agent描述
        base_prompt = """
## 角色
你是吉量智能任务监督者，负责协调多个专业Agent完成复杂任务。

## 你的身份
你是由三峡集团数字化管理中心发布的吉量模型的核心协调中心，基于吉量模型定制优化。
        """
        
        self.supervisor_prompt = f"""
{base_prompt}

## 当前可用的专业Agent
{chr(10).join(available_agent_descriptions)}

## 协调策略
1. **任务分解**: 将复杂查询分解为子任务
2. **智能分配**: 根据各Agent专长分配最合适的任务
3. **执行协调**: 合理安排Agent执行顺序，避免资源冲突
4. **结果整合**: 综合各Agent结果，提供统一的高质量答案

## 决策流程
1. 分析用户查询的核心需求和复杂度
2. 确定需要哪些专业Agent参与
3. 制定最优的执行计划和分配任务
4. 分配具体任务给对应的Agent
5. 监控执行过程，基于Agent的回答，必要时调整执行计划和分配任务
6. 基于所有Agent的回答和你的深入理解，生成最终回答

## 要选择Agent执行任务，请按以下格式输出：（确保每次只能选择**一个**Agent）

[AGENT_SELECTION]
{{{{
    "agent": "agent_name",
    "reasoning": "选择这个Agent的原因",
    "task": "具体要执行的任务"
}}}}
[/AGENT_SELECTION]

## 要结束任务，请按以下格式输出：

[TASK_COMPLETE]
根据当前任务的完成情况，总结并输出最终给用户的回复。(这是占位符，实际输出时，请删除这个占位符，替换为实际的回复)
[/TASK_COMPLETE]
"""
        
        logger.info(f"文本模式监督者初始化完成，管理{len(agents)}个Agent")
    
    def compile(self, checkpointer=None):
        """编译监督者图"""
        return TextSupervisorGraph(
            supervisor=self,
            checkpointer=checkpointer
        )


class TextSupervisorGraph:
    """文本模式监督者图"""
    
    def __init__(self, supervisor: TextSupervisorWrapper, checkpointer=None):
        self.supervisor = supervisor
        self.checkpointer = checkpointer
    
    async def ainvoke(self, input_data: Dict[str, Any], config: Optional[Dict] = None) -> Dict[str, Any]:
        """异步执行监督者协调"""
        try:
            messages = input_data.get("messages", [])
            all_results = []
            max_iterations = 10  # 防止无限循环
            iteration = 0
            
            # 构建监督者的聊天模板
            from langchain_core.prompts import ChatPromptTemplate
            from langchain_core.output_parsers import StrOutputParser
            
            supervisor_template = ChatPromptTemplate.from_messages([
                ("system", self.supervisor.supervisor_prompt),
                ("placeholder", "{messages}")
            ])
            
            supervisor_chain = supervisor_template | self.supervisor.model | StrOutputParser()
            
            while iteration < max_iterations:
                iteration += 1
                logger.info(f"监督者第{iteration}轮决策")
                
                # 监督者分析当前状态并做决策
                supervisor_response = await supervisor_chain.ainvoke(
                    {"messages": messages},
                    config=config
                )
                supervisor_response = supervisor_response.split("</think>")[-1].rstrip()

                # 添加监督者的决策消息（带Agent标识）
                supervisor_msg = AIMessage(
                    content=supervisor_response,
                    name="supervisor",  # 重要：添加Agent标识
                    additional_kwargs={
                        "agent_name": "supervisor",
                        "decision_round": iteration
                    }
                )
                all_results.append(supervisor_msg)
                
                # 解析监督者的决策
                parsed_actions = text_parser.parse_text(supervisor_response)
                
                # 查找Agent选择指令
                agent_selection = None
                task_complete = False
                
                for action in parsed_actions:
                    if "[AGENT_SELECTION]" in action.content:
                        # 解析Agent选择
                        match = re.search(r'\[AGENT_SELECTION\]\s*(\{[^}]*\})\s*\[/AGENT_SELECTION\]', action.content, re.DOTALL)
                        if not match:
                            # 兼容旧格式
                            match = re.search(r'<agent_selection>\s*(\{[^}]*\})\s*</agent_selection>', action.content, re.DOTALL)
                        if match:
                            try:
                                agent_data = json.loads(match.group(1))
                                agent_selection = agent_data
                                break
                            except json.JSONDecodeError:
                                logger.warning(f"解析Agent选择失败: {match.group(1)}")
                    
                    elif "[TASK_COMPLETE]" in action.content:
                        # 任务完成
                        task_complete = True
                        # 提取完成总结
                        match = re.search(r'\[TASK_COMPLETE\]\s*([^\[]*)\s*\[/TASK_COMPLETE\]', action.content, re.DOTALL)
                        if not match:
                            # 兼容旧格式
                            match = re.search(r'<task_complete>\s*([^<]*)\s*</task_complete>', action.content, re.DOTALL)
                        if match:
                            summary = match.group(1).strip()
                            completion_msg = AIMessage(
                                content=f"监督者总结：{summary}",
                                name="supervisor",
                                additional_kwargs={"agent_name": "supervisor", "task_status": "completed"}
                            )
                            all_results.append(completion_msg)
                        break
                
                # 如果任务完成，退出循环
                if task_complete:
                    logger.info("监督者认为任务已完成")
                    break
                
                # 如果没有找到Agent选择，检查是否应该结束任务
                if not agent_selection:
                    # 如果既没有agent选择也没有task_complete标记，说明监督者已经完成了总结
                    # 这种情况应该认为任务已完成
                    if not task_complete:
                        logger.info("监督者既没有选择Agent也没有明确标记完成，认为任务已自然完成")
                        # 将监督者的回复作为最终总结
                        if supervisor_response.strip():
                            completion_msg = AIMessage(
                                content=supervisor_response,
                                name="supervisor",
                                additional_kwargs={"agent_name": "supervisor", "task_status": "auto_completed"}
                            )
                            all_results.append(completion_msg)
                        task_complete = True
                        break
                    continue
                
                # 执行选中的Agent
                selected_agent_name = agent_selection.get("agent")
                task_description = agent_selection.get("task", "")
                reasoning = agent_selection.get("reasoning", "")
                
                if selected_agent_name in self.supervisor.agent_mapping:
                    logger.info(f"监督者选择执行Agent: {selected_agent_name}，原因: {reasoning}")
                    
                    # 添加工具调用消息（模拟handoff tool call）
                    tool_call_id = f"call_{selected_agent_name}_{iteration}"
                    
                    # 构建handoff工具调用消息
                    handoff_tool_call = {
                        "name": f"handoff_to_{selected_agent_name}",
                        "args": {
                            "task_description": task_description or f"请处理以下任务",
                            "reasoning": reasoning
                        },
                        "id": tool_call_id
                    }
                    
                    # 更新监督者消息以包含工具调用
                    supervisor_msg.tool_calls = [handoff_tool_call]
                    
                    # 创建工具调用消息（表示监督者正在调用子agent）
                    tool_call_start_msg = tool_message_adapter.create_supervisor_message(
                        agent_name=selected_agent_name,
                        action="start",
                        description=task_description,
                        tool_call_id=tool_call_id
                    )
                    all_results.append(tool_call_start_msg)
                    
                    # 执行选中的Agent
                    selected_agent = self.supervisor.agent_mapping[selected_agent_name]
                    
                    # 构建Agent的输入（只包含监督者分配的任务，不包含原始用户消息）
                    agent_messages = [
                        # 监督者分配的具体任务
                        HumanMessage(content=f"监督者分配的任务：{task_description}")
                    ]
                    
                    try:
                        agent_result = await selected_agent.ainvoke(
                            {"messages": agent_messages},
                            config=config
                        )
                        
                        # 收集Agent的结果并添加标识
                        new_messages = agent_result.get("messages", [])
                        
                        # 为Agent消息添加标识，应用include_agent_name策略
                        for msg in new_messages:
                            if isinstance(msg, AIMessage):
                                msg.name = selected_agent_name
                                msg.additional_kwargs = msg.additional_kwargs or {}
                                msg.additional_kwargs["agent_name"] = selected_agent_name
                                
                                # 应用include_agent_name策略
                                if self.supervisor.include_agent_name == "inline" and msg.content:
                                    msg.content = f"""
以下是 {selected_agent_name} 的回答，请参考：

{msg.content}
"""
                        
                        # 根据过滤设置决定要添加的消息
                        if self.supervisor.filter_agent_messages:
                            # 只返回最后一条AI消息（类似LangGraph的last_message模式）
                            filtered_messages = []
                            for msg in reversed(new_messages):
                                if isinstance(msg, AIMessage):
                                    filtered_messages = [msg]
                                    break
                            all_results.extend(filtered_messages)
                            # Supervisor的内部决策历史也应该只包含过滤后的消息
                            messages_for_supervisor = filtered_messages
                        else:
                            # 返回全部执行流程
                            all_results.extend(new_messages)
                            # Supervisor的内部决策历史包含完整消息
                            messages_for_supervisor = new_messages
                        
                        # 添加工具执行成功的消息
                        tool_result_msg = tool_message_adapter.create_supervisor_message(
                            agent_name=selected_agent_name,
                            action="complete",
                            description="执行完成",
                            tool_call_id=tool_call_id
                        )
                        all_results.append(tool_result_msg)
                        
                        # 更新消息历史，供监督者下次决策使用
                        # 根据filter_agent_messages设置添加相应的消息
                        messages.extend(messages_for_supervisor)  # 添加过滤后的agent执行结果
                        messages.append(tool_result_msg)  # 添加工具执行完成信息
                        
                        logger.info(f"Agent {selected_agent_name} 执行完成，添加到Supervisor历史: {len(messages_for_supervisor)}条消息 (filter_messages={self.supervisor.filter_agent_messages})")
                        
                    except Exception as agent_error:
                        logger.error(f"Agent {selected_agent_name} 执行失败: {agent_error}")
                        error_msg = tool_message_adapter.create_tool_error_message(
                            error_message=str(agent_error),
                            tool_name=f"handoff_to_{selected_agent_name}",
                            tool_call_id=tool_call_id
                        )
                        all_results.append(error_msg)
                        messages.append(error_msg)
                
                else:
                    logger.warning(f"监督者选择了不存在的Agent: {selected_agent_name}")
                    error_msg = AIMessage(
                        content=f"监督者选择了不存在的Agent：{selected_agent_name}",
                        name="supervisor",
                        additional_kwargs={"agent_name": "supervisor", "error": "invalid_agent_selection"}
                    )
                    all_results.append(error_msg)
                    messages.append(error_msg)
            
            if iteration >= max_iterations:
                logger.warning(f"监督者达到最大迭代次数限制: {max_iterations}")
                warning_msg = AIMessage(content="监督者达到最大决策次数限制，结束协调")
                all_results.append(warning_msg)
            
            return {"messages": all_results}
            
        except Exception as e:
            logger.error(f"文本模式监督者协调失败: {e}")
            error_msg = AIMessage(content=f"监督者协调失败：{str(e)}")
            return {"messages": [error_msg]}


class TextSwarmWrapper:
    """文本模式的Swarm包装器
    
    模拟LangGraph官方swarm的行为，但使用文本解析而不是function calling
    """
    
    def __init__(self, 
                 agents: List[Any], 
                 default_active_agent: str,
                 filter_agent_messages: bool = False,
                 include_agent_name: str = None):
        """初始化文本模式Swarm
        
        Args:
            agents: Agent列表
            default_active_agent: 默认活跃Agent名称
            filter_agent_messages: 是否过滤Agent消息（True=只返回最后一条，False=返回全部）
            include_agent_name: Agent名称包含方式（None=使用message.name，"inline"=内联到内容中）
        """
        self.agents = agents
        self.default_active_agent = default_active_agent
        self.filter_agent_messages = filter_agent_messages
        self.include_agent_name = include_agent_name
        
        # 创建Agent名称映射
        self.agent_mapping = {}
        for agent in agents:
            agent_name = getattr(agent, 'name', str(agent))
            self.agent_mapping[agent_name] = agent
        
        logger.info(f"文本模式Swarm初始化完成，{len(agents)}个Agent，默认活跃Agent: {default_active_agent}")
    
    def compile(self, checkpointer=None):
        """编译Swarm图"""
        return TextSwarmGraph(
            swarm=self,
            checkpointer=checkpointer
        )


class TextSwarmGraph:
    """文本模式Swarm图"""
    
    def __init__(self, swarm: TextSwarmWrapper, checkpointer=None):
        self.swarm = swarm
        self.checkpointer = checkpointer
    
    async def ainvoke(self, input_data: Dict[str, Any], config: Optional[Dict] = None) -> Dict[str, Any]:
        """异步执行Swarm协调"""
        try:
            messages = input_data.get("messages", [])
            current_active_agent = self.swarm.default_active_agent
            all_results = []
            max_handoffs = 10  # 防止无限循环
            handoff_count = 0
            execution_count = 0
            current_handoff_task = None  # 保存当前转交任务描述
            
            logger.info(f"Swarm开始执行，初始Agent: {current_active_agent}")
            
            while handoff_count < max_handoffs:
                execution_count += 1
                
                try:
                    # 获取当前活跃Agent
                    if current_active_agent not in self.swarm.agent_mapping:
                        logger.error(f"未找到Agent: {current_active_agent}")
                        break
                    
                    agent = self.swarm.agent_mapping[current_active_agent]
                    logger.info(f"Swarm第{execution_count}次执行Agent: {current_active_agent}")
                    
                    # 构建Agent的输入消息
                    if execution_count == 1:
                        # 第一个Agent：使用原始用户消息
                        agent_messages = messages
                    else:
                        # 后续Agent：只携带原始用户问题 + 上一个Agent的非handoff回复内容
                        # 避免完整上下文导致的无限循环
                        
                        # 使用之前保存的转交任务描述
                        handoff_task_description = current_handoff_task or "继续处理用户问题"
                        
                        # 构建精简的消息上下文
                        agent_messages = []
                        
                        # 1. 保留原始用户问题（第一条用户消息）
                        for msg in messages:
                            if isinstance(msg, HumanMessage):
                                agent_messages.append(msg)
                                break
                        
                        # 2. 获取上一个Agent的回复内容和转交信息，合并为一条AI消息
                        last_agent_content = None
                        last_agent_name = None
                        
                        # 找到最后一个AI消息的内容和agent名称
                        for msg in reversed(messages):
                            if isinstance(msg, AIMessage) and hasattr(msg, 'content') and msg.content:
                                # 排除tool call格式和handoff信息，获取纯净的回复内容
                                content = msg.content
                                
                                # 移除tool call格式内容
                                import re
                                # 移除新格式的tool_call内容
                                content = re.sub(r'\[TOOL_CALL\].*?\[/TOOL_CALL\]', '', content, flags=re.DOTALL)
                                # 兼容旧格式
                                content = re.sub(r'<tool_call>.*?</tool_call>', '', content, flags=re.DOTALL)
                                
                                # 移除新格式的handoff内容
                                content = re.sub(r'\[HANDOFF\].*?\[/HANDOFF\]', '', content, flags=re.DOTALL)
                                # 兼容旧格式
                                content = re.sub(r'<handoff>.*?</handoff>', '', content, flags=re.DOTALL)
                                
                                # 清理多余的空白字符
                                content = re.sub(r'\n\s*\n', '\n', content.strip())
                                
                                # 如果还有有效内容，使用它
                                if content.strip():
                                    last_agent_content = content.strip()
                                    last_agent_name = getattr(msg, 'name', 'unknown_agent')
                                    break
                        
                        # 3. 构建包含上一个Agent回复和转交信息的AI消息
                        if last_agent_content and last_agent_name:
                            # 合并上一个Agent的回复和转交信息
                            combined_content = f"""【{last_agent_name}的处理结果】
{last_agent_content}

【转交给当前Agent的任务】
{handoff_task_description}

请基于以上{last_agent_name}的处理结果和转交任务，继续完成用户的需求。"""
                            
                            combined_msg = AIMessage(
                                content=combined_content,
                                name=last_agent_name,
                                additional_kwargs={
                                    "agent_name": last_agent_name,
                                    "message_type": "handoff_context",
                                    "target_agent": current_active_agent
                                }
                            )
                            agent_messages.append(combined_msg)
                        else:
                            # 如果没有找到上一个Agent的内容，只添加转交任务
                            handoff_msg = AIMessage(
                                content=f"【转交任务】{handoff_task_description}",
                                name="system",
                                additional_kwargs={
                                    "message_type": "handoff_only",
                                    "target_agent": current_active_agent
                                }
                            )
                            agent_messages.append(handoff_msg)
                        
                        logger.info(f"Swarm转交时携带精简上下文: {len(agent_messages)}条消息（用户问题+上一Agent回复+转交任务），上一Agent内容: {'有' if last_agent_content else '无'}")
                    
                    # 执行Agent
                    result = await agent.ainvoke(
                        {"messages": agent_messages},
                        config=config
                    )
                    
                    # 收集结果并添加Agent标识
                    new_messages = result.get("messages", [])
                    agent_handoff_executed = result.get("handoff_executed", False)
                    
                    # 为Agent消息添加标识，应用include_agent_name策略
                    for msg in new_messages:
                        if isinstance(msg, AIMessage):
                            msg.name = current_active_agent
                            msg.additional_kwargs = msg.additional_kwargs or {}
                            msg.additional_kwargs["agent_name"] = current_active_agent
                            
                            # 应用include_agent_name策略（注意：Swarm通常不需要inline模式，因为是顺序执行）
                            # 但为了一致性，仍然支持这个参数
                            if self.swarm.include_agent_name == "inline" and msg.content:
                                msg.content = f"""
以下是 {current_active_agent} 的回答，请参考：
                                    
{msg.content}
"""
                    # 根据过滤设置决定要添加的消息
                    if self.swarm.filter_agent_messages:
                        # 只返回最后一条AI消息（类似LangGraph的last_message模式）
                        filtered_messages = []
                        for msg in reversed(new_messages):
                            if isinstance(msg, AIMessage):
                                filtered_messages = [msg]
                                break
                        all_results.extend(filtered_messages)
                    else:
                        # 返回全部执行流程
                        all_results.extend(new_messages)
                    
                    # 检查是否有转交指令
                    handoff_found = False
                    next_agent = None
                    handoff_task = None
                    
                    # 优先检查Agent返回的handoff_executed标志
                    if agent_handoff_executed:
                        logger.info(f"Agent {current_active_agent} 标记已执行handoff，直接从消息中提取转交信息")
                        # 直接从ToolMessage中提取转交信息
                        for message in new_messages:
                            if isinstance(message, ToolMessage) and message.name and message.name.startswith("transfer_to_"):
                                # 检查新格式和旧格式的handoff
                                if "[HANDOFF]" in message.content or "<handoff>" in message.content:
                                    # 先尝试新格式
                                    agent_match = re.search(r'\[HANDOFF\]\s*agent:\s*(\w+)', message.content)
                                    task_match = re.search(r'task:\s*([^\[]*?)\[/HANDOFF\]', message.content)
                                    
                                    # 如果新格式不匹配，尝试旧格式
                                    if not agent_match:
                                        agent_match = re.search(r'agent:\s*(\w+)', message.content)
                                        task_match = re.search(r'task:\s*([^\n]*)', message.content)
                                    
                                    if agent_match:
                                        target_agent = agent_match.group(1)
                                        handoff_task = task_match.group(1) if task_match else ""
                                        
                                        if target_agent in self.swarm.agent_mapping:
                                            next_agent = target_agent
                                            handoff_found = True
                                            handoff_count += 1
                                            logger.info(f"从handoff_executed标志检测到Swarm转交: {current_active_agent} -> {next_agent}，任务: {handoff_task}")
                                            break
                                        else:
                                            logger.warning(f"Swarm转交失败: 目标Agent {target_agent} 不存在")
                                
                                if handoff_found:
                                    break
                    
                    # 如果没有通过handoff_executed检测到，使用传统方法检查AI消息中的工具调用和handoff
                    if not handoff_found:
                        for message in new_messages:
                            if isinstance(message, AIMessage):
                                # 解析文本寻找工具调用和handoff
                                parsed_actions = text_parser.parse_text(message.content)
                                
                                for action in parsed_actions:
                                    if action.action_type == ParsedActionType.TOOL_CALL:
                                        tool_data = action.metadata.get("tool_call")
                                        if tool_data and tool_data.get("name", "").startswith("transfer_to_"):
                                            # 提取目标agent名称
                                            target_agent = tool_data["name"].replace("transfer_to_", "")
                                            handoff_task = tool_data.get("args", {}).get("task_description", "")
                                            
                                            if target_agent in self.swarm.agent_mapping:
                                                next_agent = target_agent
                                                handoff_found = True
                                                handoff_count += 1
                                                logger.info(f"Swarm转交: {current_active_agent} -> {next_agent}，任务: {handoff_task}")
                                                break
                                            else:
                                                logger.warning(f"Swarm转交失败: 目标Agent {target_agent} 不存在")
                                    
                                    elif action.action_type == ParsedActionType.AGENT_HANDOFF:
                                        # 处理直接的handoff格式（只处理第一个handoff）
                                        handoff_data = action.metadata.get("handoff")
                                        if handoff_data:
                                            target_agent = handoff_data["target_agent"]
                                            handoff_task = handoff_data["task_description"]
                                            
                                            if target_agent in self.swarm.agent_mapping:
                                                next_agent = target_agent
                                                handoff_found = True
                                                handoff_count += 1
                                                logger.info(f"Swarm转交: {current_active_agent} -> {next_agent}，任务: {handoff_task}")
                                                logger.info(f"注意：检测到多个handoff，只处理第一个")
                                                break
                                            else:
                                                logger.warning(f"Swarm转交失败: 目标Agent {target_agent} 不存在")
                                
                                if handoff_found:
                                    break
                    
                    # 如果没有在AI消息中找到转交，检查ToolMessage中的转交指令
                    if not handoff_found:
                        for message in new_messages:
                            if isinstance(message, ToolMessage) and message.name and message.name.startswith("transfer_to_"):
                                # 从ToolMessage中解析转交信息
                                if "[HANDOFF]" in message.content or "<handoff>" in message.content:
                                    # 先尝试新格式
                                    agent_match = re.search(r'\[HANDOFF\]\s*agent:\s*(\w+)', message.content)
                                    task_match = re.search(r'task:\s*([^\[]*?)\[/HANDOFF\]', message.content)
                                    
                                    # 如果新格式不匹配，尝试旧格式
                                    if not agent_match:
                                        agent_match = re.search(r'agent:\s*(\w+)', message.content)
                                        task_match = re.search(r'task:\s*([^\n]*)', message.content)
                                    
                                    if agent_match:
                                        target_agent = agent_match.group(1)
                                        handoff_task = task_match.group(1) if task_match else ""
                                        
                                        if target_agent in self.swarm.agent_mapping:
                                            next_agent = target_agent
                                            handoff_found = True
                                            handoff_count += 1
                                            logger.info(f"Swarm转交: {current_active_agent} -> {next_agent}，任务: {handoff_task}")
                                            break
                                        else:
                                            logger.warning(f"Swarm转交失败: 目标Agent {target_agent} 不存在")
                                
                                if handoff_found:
                                    break
                    
                    # 更新消息历史
                    messages.extend(new_messages)
                    
                    # 如果有转交指令，切换到新Agent
                    if handoff_found and next_agent:
                        # 添加handoff工具调用信息
                        handoff_tool_call_id = f"swarm_handoff_{current_active_agent}_to_{next_agent}_{execution_count}"
                        
                        # 为当前agent的最后一条消息添加tool call信息
                        if new_messages:
                            last_agent_msg = None
                            for msg in reversed(new_messages):
                                if isinstance(msg, AIMessage) and msg.name == current_active_agent:
                                    last_agent_msg = msg
                                    break
                            
                            if last_agent_msg:
                                # 添加handoff工具调用到agent消息
                                handoff_tool_call = {
                                    "name": f"handoff_to_{next_agent}",
                                    "args": {
                                        "task_description": handoff_task or "继续处理任务",
                                        "from_agent": current_active_agent
                                    },
                                    "id": handoff_tool_call_id
                                }
                                last_agent_msg.tool_calls = [handoff_tool_call]
                        
                        # 添加handoff执行消息
                        handoff_msg = tool_message_adapter.create_swarm_message(
                            current_agent=current_active_agent,
                            next_agent=next_agent,
                            task=handoff_task,
                            tool_call_id=handoff_tool_call_id
                        )
                        all_results.append(handoff_msg)
                        messages.append(handoff_msg)
                        
                        # 保存转交任务供下次循环使用
                        current_handoff_task = handoff_task
                        
                        # 切换到下一个Agent（不再向messages中添加转交任务，避免消息堆积）
                        current_active_agent = next_agent
                        continue
                    
                    # 如果没有转交，结束执行
                    # 注意：在ReAct模式下，Agent内部已经处理完所有工具调用循环
                    # Swarm层只负责Agent间的转交，不干预单个Agent的内部工具调用逻辑
                    logger.info(f"Swarm执行完成，最终Agent: {current_active_agent}，无转交指令")
                    break
                        
                except Exception as agent_error:
                    logger.error(f"Swarm执行Agent {current_active_agent} 失败: {agent_error}")
                    error_msg = AIMessage(content=f"Swarm Agent {current_active_agent} 执行失败：{str(agent_error)}")
                    all_results.append(error_msg)
                    break
            
            if handoff_count >= max_handoffs:
                logger.warning(f"Swarm达到最大转交次数限制: {max_handoffs}")
                warning_msg = AIMessage(content="Swarm达到最大转交次数限制，停止执行")
                all_results.append(warning_msg)
            
            logger.info(f"Swarm执行结束，共执行{execution_count}次，转交{handoff_count}次")
            return {"messages": all_results}
            
        except Exception as e:
            logger.error(f"文本模式Swarm协调失败: {e}")
            error_msg = AIMessage(content=f"Swarm协调失败：{str(e)}")
            return {"messages": [error_msg]}


def create_text_supervisor(
    agents: List[Any],
    model: ChatOpenAI,
    prompt: str = None,
    output_mode: str = "last_message",
    include_agent_name: str = "inline",
    filter_agent_messages: bool = False
) -> TextSupervisorWrapper:
    """创建文本模式的监督者
    
    Args:
        agents: Agent列表
        model: LLM模型
        prompt: 监督者提示词
        output_mode: 输出模式
        include_agent_name: 包含Agent名称的方式
        filter_agent_messages: 是否过滤Agent消息（True=只返回最后一条，False=返回全部）
        
    Returns:
        TextSupervisorWrapper: 文本模式监督者
    """
    return TextSupervisorWrapper(
        agents=agents,
        model=model,
        prompt=prompt,
        output_mode=output_mode,
        include_agent_name=include_agent_name,
        filter_agent_messages=filter_agent_messages
    )


def create_text_swarm(
    agents: List[Any],
    default_active_agent: str,
    filter_agent_messages: bool = False,
    include_agent_name: str = None
) -> TextSwarmWrapper:
    """创建文本模式的Swarm
    
    Args:
        agents: Agent列表
        default_active_agent: 默认活跃Agent名称
        filter_agent_messages: 是否过滤Agent消息（True=只返回最后一条，False=返回全部）
        include_agent_name: Agent名称包含方式（None=使用message.name，"inline"=内联到内容中）
        
    Returns:
        TextSwarmWrapper: 文本模式Swarm
    """
    return TextSwarmWrapper(
        agents=agents,
        default_active_agent=default_active_agent,
        filter_agent_messages=filter_agent_messages,
        include_agent_name=include_agent_name
    )