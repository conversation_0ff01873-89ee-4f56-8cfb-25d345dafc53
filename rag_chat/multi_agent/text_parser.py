"""
Text-based parser for agent coordination without function calling.
Provides structured text parsing for tool calls and agent handoffs.
"""

import json
import re
import ast
from typing import Dict, List, Optional, Any, Union
from dataclasses import dataclass
from enum import Enum
import logging

logger = logging.getLogger(__name__)


class ParsedActionType(Enum):
    """Types of parsed actions from agent text output."""
    TOOL_CALL = "tool_call"
    AGENT_HANDOFF = "agent_handoff"
    FINAL_RESPONSE = "final_response"
    THINKING = "thinking"


@dataclass
class ParsedAction:
    """Parsed action from agent text output."""
    action_type: ParsedActionType
    content: str
    metadata: Dict[str, Any]
    
    def to_dict(self) -> Dict[str, Any]:
        return {
            "action_type": self.action_type.value,
            "content": self.content,
            "metadata": self.metadata
        }


@dataclass
class ToolCall:
    """Represents a tool call parsed from text."""
    name: str
    args: Dict[str, Any]
    reasoning: Optional[str] = None
    
    def to_dict(self) -> Dict[str, Any]:
        return {
            "name": self.name,
            "args": self.args,
            "reasoning": self.reasoning
        }


@dataclass
class AgentHandoff:
    """Represents an agent handoff parsed from text."""
    target_agent: str
    task_description: str
    context: Optional[str] = None
    
    def to_dict(self) -> Dict[str, Any]:
        return {
            "target_agent": self.target_agent,
            "task_description": self.task_description,
            "context": self.context
        }


class TextParser:
    """
    Text-based parser for agent coordination without function calling.
    Parses structured text output to identify tool calls and agent handoffs.
    """
    
    def __init__(self):
        self.tool_patterns = [
            # 使用方括号标记 - 更稳定不易被过滤，支持嵌套JSON
            r'\[TOOL_CALL\]\s*(.*?)\s*\[/TOOL_CALL\]',
            # 兼容原XML格式 (逐步废弃)
            r'<tool_call>\s*(.*?)\s*</tool_call>',
            # Structured format pattern
            r'TOOL:\s*(\w+)\s*\nARGS:\s*(\{.*?\})',
            # Action format pattern
            r'ACTION:\s*(\w+)\s*\nINPUT:\s*(\{.*?\})',
            # Natural language pattern
            r'使用工具\s*(\w+)\s*参数\s*(\{.*?\})',
            # Simple key-value pattern
            r'调用\s*(\w+)\s*工具.*?参数[:：]\s*(\{.*?\})',
        ]
        
        self.handoff_patterns = [
            # 使用方括号标记 - 更稳定不易被过滤
            r'\[HANDOFF\]\s*agent:\s*(\w+)\s*task:\s*([^\[]*?)\[/HANDOFF\]',
            # 兼容原XML格式 (逐步废弃)
            r'<handoff>\s*agent:\s*(\w+)\s*task:\s*([^<]*)</handoff>',
            # Structured handoff pattern
            r'HANDOFF:\s*(\w+)\s*\nTASK:\s*([^\n]*)',
            # Transfer pattern
            r'TRANSFER_TO:\s*(\w+)\s*\nDESCRIPTION:\s*([^\n]*)',
            # Natural language handoff
            r'转交给\s*(\w+)\s*代理.*?任务[:：]\s*([^\n]*)',
            # Direct assignment
            r'分配给\s*(\w+)\s*[:：]\s*([^\n]*)',
        ]
        
        self.thinking_patterns = [
            # 使用方括号标记 - 更稳定不易被过滤
            r'\[THINKING\](.*?)\[/THINKING\]',
            # 兼容原XML格式 (逐步废弃)
            r'<thinking>(.*?)</thinking>',
            r'思考[:：]\s*([^\n]*)',
            r'分析[:：]\s*([^\n]*)',
            r'THINKING:\s*([^\n]*)',
        ]
    
    def parse_text(self, text: str) -> List[ParsedAction]:
        """
        Parse text output to identify different types of actions.
        Handles implicit thinking blocks and separates them from actions.
        
        Args:
            text: The text output from an agent
            
        Returns:
            List of ParsedAction objects
        """
        actions: List[ParsedAction] = []
        action_part = text
        
        # Handle implicit thinking block ending with </think>
        think_end_tag = '</think>'
        if think_end_tag in text:
            thinking_part, _, action_part = text.rpartition(think_end_tag)
            
            # Clean up potential <think> tag at the start of the thinking part
            if thinking_part.lstrip().startswith('<think>'):
                start_pos = thinking_part.find('<think>')
                thinking_part = thinking_part[start_pos + len('<think>'):]
            
            if thinking_part.strip():
                actions.append(ParsedAction(
                    action_type=ParsedActionType.THINKING,
                    content=thinking_part.strip(),
                    metadata={"pattern": "Implicit thinking before </think>"}
                ))

        # Now parse the action_part for explicit actions
        all_actions_in_part = []
        
        # Check for thinking content first
        thinking_actions = self._parse_thinking(action_part)
        all_actions_in_part.extend(thinking_actions)
        
        # Check for tool calls
        tool_actions = self._parse_tool_calls(action_part)
        all_actions_in_part.extend(tool_actions)
        
        # Check for agent handoffs
        handoff_actions = self._parse_handoffs(action_part)
        all_actions_in_part.extend(handoff_actions)
        
        actions.extend(all_actions_in_part)
        
        # If no specific actions found in the action_part, it might be a final response.
        if not all_actions_in_part and action_part.strip():
            actions.append(ParsedAction(
                action_type=ParsedActionType.FINAL_RESPONSE,
                content=action_part.strip(),
                metadata={}
            ))
        # If no actions were found at all in the original text, treat the whole text as a final response.
        elif not actions and text.strip():
            actions.append(ParsedAction(
                action_type=ParsedActionType.FINAL_RESPONSE,
                content=text.strip(),
                metadata={}
            ))
            
        return actions
    
    def _parse_thinking(self, text: str) -> List[ParsedAction]:
        """Parse thinking content from text."""
        actions = []
        
        for pattern in self.thinking_patterns:
            matches = re.finditer(pattern, text, re.DOTALL | re.IGNORECASE)
            for match in matches:
                thinking_content = match.group(1).strip()
                if thinking_content:
                    actions.append(ParsedAction(
                        action_type=ParsedActionType.THINKING,
                        content=thinking_content,
                        metadata={"pattern": pattern}
                    ))
        
        return actions
    
    def _parse_tool_calls(self, text: str) -> List[ParsedAction]:
        """Parse tool calls from text."""
        actions = []
        
        for pattern in self.tool_patterns:
            matches = re.finditer(pattern, text, re.DOTALL | re.IGNORECASE)
            for match in matches:
                try:
                    if len(match.groups()) == 1:
                        # Single group - 可能包含完整的JSON内容
                        content = match.group(1).strip()
                        
                        # 尝试找到JSON部分
                        json_str = None
                        if content.startswith('{') and content.endswith('}'):
                            # 整个内容就是JSON
                            json_str = content
                        else:
                            # 尝试从内容中提取JSON
                            json_match = re.search(r'\{.*\}', content, re.DOTALL)
                            if json_match:
                                json_str = json_match.group(0)
                        
                        if json_str:
                            # 清理和修复JSON字符串
                            json_str = json_str.replace('{{', '{').replace('}}', '}')
                            # 移除可能的尾随逗号
                            json_str = re.sub(r',\s*}', '}', json_str)
                            json_str = re.sub(r',\s*]', ']', json_str)
                            # 移除注释
                            json_str = re.sub(r'//.*?\n', '\n', json_str)
                            json_str = re.sub(r'/\*.*?\*/', '', json_str, flags=re.DOTALL)
                            
                            tool_data = self._safe_json_parse(json_str)
                            if tool_data:
                                tool_call = ToolCall(
                                    name=tool_data.get("name", "unknown"),
                                    args=tool_data.get("args", {}),
                                    reasoning=tool_data.get("reasoning")
                                )
                            else:
                                continue
                        else:
                            continue
                    else:
                        # Separate tool name and args
                        tool_name = match.group(1)
                        json_str = match.group(2)
                        # 清理和修复JSON字符串
                        json_str = json_str.replace('{{', '{').replace('}}', '}')
                        # 移除可能的尾随逗号
                        json_str = re.sub(r',\s*}', '}', json_str)
                        json_str = re.sub(r',\s*]', ']', json_str)
                        # 移除注释
                        json_str = re.sub(r'//.*?\n', '\n', json_str)
                        json_str = re.sub(r'/\*.*?\*/', '', json_str, flags=re.DOTALL)
                        
                        tool_args = self._safe_json_parse(json_str)
                        if tool_args:
                            tool_call = ToolCall(
                                name=tool_name,
                                args=tool_args
                            )
                        else:
                            continue
                    
                    actions.append(ParsedAction(
                        action_type=ParsedActionType.TOOL_CALL,
                        content=match.group(0),
                        metadata={"tool_call": tool_call.to_dict()}
                    ))
                    
                except (json.JSONDecodeError, KeyError, IndexError) as e:
                    logger.warning(f"Failed to parse tool call: {e}")
                    logger.debug(f"Failed to parse: {match.group(0) if match else 'No match'}")
                    continue
        
        return actions
    
    def _parse_handoffs(self, text: str) -> List[ParsedAction]:
        """Parse agent handoffs from text."""
        actions = []
        
        for pattern in self.handoff_patterns:
            matches = re.finditer(pattern, text, re.DOTALL | re.IGNORECASE)
            for match in matches:
                try:
                    target_agent = match.group(1).strip()
                    task_description = match.group(2).strip()
                    
                    handoff = AgentHandoff(
                        target_agent=target_agent,
                        task_description=task_description
                    )
                    
                    actions.append(ParsedAction(
                        action_type=ParsedActionType.AGENT_HANDOFF,
                        content=match.group(0),
                        metadata={"handoff": handoff.to_dict()}
                    ))
                    
                except (IndexError, AttributeError) as e:
                    logger.warning(f"Failed to parse handoff: {e}")
                    continue
        
        return actions
    
    def extract_tool_calls(self, text: str) -> List[ToolCall]:
        """Extract tool calls from text."""
        actions = self.parse_text(text)
        tool_calls = []
        
        for action in actions:
            if action.action_type == ParsedActionType.TOOL_CALL:
                tool_data = action.metadata.get("tool_call")
                if tool_data:
                    tool_calls.append(ToolCall(
                        name=tool_data["name"],
                        args=tool_data["args"],
                        reasoning=tool_data.get("reasoning")
                    ))
        
        return tool_calls
    
    def extract_handoffs(self, text: str) -> List[AgentHandoff]:
        """Extract agent handoffs from text."""
        actions = self.parse_text(text)
        handoffs = []
        
        for action in actions:
            if action.action_type == ParsedActionType.AGENT_HANDOFF:
                handoff_data = action.metadata.get("handoff")
                if handoff_data:
                    handoffs.append(AgentHandoff(
                        target_agent=handoff_data["target_agent"],
                        task_description=handoff_data["task_description"],
                        context=handoff_data.get("context")
                    ))
        
        return handoffs
    
    def is_thinking_content(self, text: str) -> bool:
        """Check if text contains thinking content."""
        thinking_actions = self._parse_thinking(text)
        return len(thinking_actions) > 0
    
    def is_tool_call(self, text: str) -> bool:
        """Check if text contains tool calls."""
        tool_actions = self._parse_tool_calls(text)
        return len(tool_actions) > 0
    
    def is_handoff(self, text: str) -> bool:
        """Check if text contains agent handoffs."""
        handoff_actions = self._parse_handoffs(text)
        return len(handoff_actions) > 0
    
    def format_tool_call_prompt(self, tool_name: str, description: str, args_schema: Dict[str, Any]) -> str:
        """Format a prompt for tool calling in text mode (LangChain bind_tools style)."""
        # # 构建详细的工具schema描述
        # tool_schema = {
        #     "type": "function",
        #     "function": {
        #         "name": tool_name,
        #         "description": description,
        #         "parameters": {
        #             "type": "object",
        #             "properties": {},
        #             "required": []
        #         }
        #     }
        # }
        
        # # 构建参数详细描述
        # if args_schema:
        #     for arg_name, arg_info in args_schema.items():
        #         if isinstance(arg_info, str):
        #             # 简单字符串描述
        #             tool_schema["function"]["parameters"]["properties"][arg_name] = {
        #                 "type": "string",
        #                 "description": arg_info
        #             }
        #             tool_schema["function"]["parameters"]["required"].append(arg_name)
        #         elif isinstance(arg_info, dict):
        #             # 详细类型描述
        #             tool_schema["function"]["parameters"]["properties"][arg_name] = arg_info
        #             if arg_info.get("required", True):
        #                 tool_schema["function"]["parameters"]["required"].append(arg_name)
        
        # # 转换为JSON并转义模板变量
        # schema_json = json.dumps(tool_schema, ensure_ascii=False, indent=2)
        # schema_json_escaped = schema_json.replace("{", "{{").replace("}", "}}")
        
        # 构建参数示例
        example_args = {}
        for arg_name, arg_info in args_schema.items():
            if isinstance(arg_info, str):
                example_args[arg_name] = f"[{arg_name}]"
            elif isinstance(arg_info, dict) and "example" in arg_info:
                example_args[arg_name] = arg_info["example"]
            else:
                example_args[arg_name] = f"[{arg_name}]"
        
        example_json = json.dumps(example_args, ensure_ascii=False, indent=2)
        
        tool_format = f"""
## 可用工具

### {tool_name}
**描述**: {description}

**使用格式**:
要调用此工具，请按以下格式输出：

[TOOL_CALL]
{{"name": "{tool_name}", "args": {example_json}, "reasoning": "调用此工具的具体原因和预期结果"}}
[/TOOL_CALL]

**参数说明**:
""" + self._format_parameters_description(args_schema)
        
        return tool_format
    
    def format_handoff_prompt(self, available_agents: List[str], agent_descriptions: Optional[Dict[str, str]] = None) -> str:
        """Format a prompt for agent handoff in text mode.
        
        Args:
            available_agents: List of available agent names
            agent_descriptions: Optional dict mapping agent names to descriptions
        """
        if agent_descriptions is None:
            # 默认描述
            agent_descriptions = {
                "knowledge_agent": "只负责三峡集团相关的知识库查询、业务问答等",
                "mcp_agent": "只负责外部工具调用、API集成、复杂计算（如天气查询）", 
                "multimodal_agent": "只负责文件处理、图像分析、文档问答",
                "simple_chat": "负责日常对话、一般性问答、基础咨询"
            }
        
        # 只显示当前可用agent的描述
        available_descriptions = []
        for agent in available_agents:
            desc = agent_descriptions.get(agent, f"处理{agent}相关任务")
            available_descriptions.append(f"- transfer_to_{agent}: {desc}")
        
        available_list = "\n".join(available_descriptions)
        agents_list = ", ".join(available_agents)
        
        return f"""
## Agent转交规则

**重要**: 每次只能转交给一个代理！如果需要处理多个任务，请选择最适合的代理来处理。

当前可用的转交选项：
{available_list}

**转交格式示例**:
[TOOL_CALL]
{{"name": "transfer_to_knowledge_agent", "args": {{"task_description": "查询三峡大坝的发电能力数据"}}, "reasoning": "用户需要查询三峡集团相关信息，需要知识专家处理"}}
[/TOOL_CALL]

可用代理：{agents_list}
"""
    
    def _format_parameters_description(self, args_schema: Dict[str, Any]) -> str:
        """Format parameter descriptions for tool prompt."""
        if not args_schema:
            return "无参数"
        
        descriptions = []
        for arg_name, arg_info in args_schema.items():
            if isinstance(arg_info, str):
                descriptions.append(f"- **{arg_name}** (string): {arg_info}")
            elif isinstance(arg_info, dict):
                arg_type = arg_info.get("type", "string")
                arg_desc = arg_info.get("description", "")
                required = "必需" if arg_info.get("required", True) else "可选"
                descriptions.append(f"- **{arg_name}** ({arg_type}, {required}): {arg_desc}")
        
        return "\n".join(descriptions)
    
    def clean_text_output(self, text: str) -> str:
        """Clean text output by removing parsed actions."""
        # Remove thinking tags
        text = re.sub(r'\[THINKING\].*?\[/THINKING\]', '', text, flags=re.DOTALL)
        text = re.sub(r'<thinking>.*?</thinking>', '', text, flags=re.DOTALL)
        
        # Remove tool call tags
        text = re.sub(r'\[TOOL_CALL\].*?\[/TOOL_CALL\]', '', text, flags=re.DOTALL)
        text = re.sub(r'<tool_call>.*?</tool_call>', '', text, flags=re.DOTALL)
        
        # Remove handoff tags
        text = re.sub(r'\[HANDOFF\].*?\[/HANDOFF\]', '', text, flags=re.DOTALL)
        text = re.sub(r'<handoff>.*?</handoff>', '', text, flags=re.DOTALL)
        
        # Clean up extra whitespace
        text = re.sub(r'\n\s*\n', '\n', text)
        text = text.strip()
        
        return text
    
    def _safe_json_parse(self, json_str: str) -> dict:
        """Safely parse JSON with multiple fallback strategies."""
        try:
            # 首先尝试标准JSON解析
            return json.loads(json_str)
        except json.JSONDecodeError as e:
            logger.debug(f"Standard JSON parsing failed: {e}")
            
            try:
                # 尝试使用ast.literal_eval（更宽松的解析）
                return ast.literal_eval(json_str)
            except (ValueError, SyntaxError) as e:
                logger.debug(f"AST literal_eval failed: {e}")
                
                try:
                    # 修复常见的JSON格式问题
                    fixed_json = json_str.strip()
                    
                    # 修复单引号为双引号
                    fixed_json = re.sub(r"'([^']*)':", r'"\1":', fixed_json)
                    fixed_json = re.sub(r":\s*'([^']*)'", r': "\1"', fixed_json)
                    # 修复True/False为小写
                    fixed_json = fixed_json.replace('True', 'true').replace('False', 'false').replace('None', 'null')
                    
                    return json.loads(fixed_json)
                except json.JSONDecodeError:
                    try:
                        # 尝试修复截断的JSON（缺少右括号）
                        fixed_json = json_str.strip()
                        
                        # 计算括号平衡
                        open_braces = fixed_json.count('{')
                        close_braces = fixed_json.count('}')
                        open_brackets = fixed_json.count('[')
                        close_brackets = fixed_json.count(']')
                        
                        # 补全缺失的括号
                        if open_braces > close_braces:
                            fixed_json += '}' * (open_braces - close_braces)
                        if open_brackets > close_brackets:
                            fixed_json += ']' * (open_brackets - close_brackets)
                        
                        # 处理可能的尾随逗号
                        fixed_json = re.sub(r',\s*}', '}', fixed_json)
                        fixed_json = re.sub(r',\s*]', ']', fixed_json)
                        
                        logger.debug(f"尝试修复截断JSON: {fixed_json[:200]}...")
                        return json.loads(fixed_json)
                        
                    except json.JSONDecodeError:
                        # 最后尝试：通过正则表达式提取关键信息
                        return self._extract_json_info_by_regex(json_str)

    def _extract_json_info_by_regex(self, json_str: str) -> dict:
        """使用正则表达式从损坏的JSON中提取关键信息"""
        result = {"name": "unknown", "args": {}}
        
        try:
            # 提取工具名称
            name_match = re.search(r'"name":\s*"([^"]+)"', json_str)
            if name_match:
                result["name"] = name_match.group(1)
            
            # 提取reasoning
            reasoning_match = re.search(r'"reasoning":\s*"([^"]+)"', json_str)
            if reasoning_match:
                result["reasoning"] = reasoning_match.group(1)
            
            # 提取args对象中的参数
            args_match = re.search(r'"args":\s*\{([^}]*)', json_str, re.DOTALL)
            if args_match:
                args_content = args_match.group(1)
                args = {}
                
                # 提取字符串参数
                for param_match in re.finditer(r'"([^"]+)":\s*"([^"]*)"', args_content):
                    args[param_match.group(1)] = param_match.group(2)
                
                # 提取数组参数（简单处理）
                for array_match in re.finditer(r'"([^"]+)":\s*\[([^\]]*)\]', args_content):
                    array_content = array_match.group(2)
                    # 简单解析字符串数组
                    if '"' in array_content:
                        array_items = re.findall(r'"([^"]*)"', array_content)
                        args[array_match.group(1)] = array_items
                
                # 提取布尔值参数
                for bool_match in re.finditer(r'"([^"]+)":\s*(true|false)', args_content):
                    args[bool_match.group(1)] = bool_match.group(2) == 'true'
                
                # 提取数字参数
                for num_match in re.finditer(r'"([^"]+)":\s*(\d+)', args_content):
                    args[num_match.group(1)] = int(num_match.group(2))
                
                result["args"] = args
            
            logger.info(f"通过正则表达式恢复JSON: name={result['name']}, args={result['args']}")
            return result
            
        except Exception as e:
            logger.warning(f"正则表达式提取失败: {e}")
            return {"name": "unknown", "args": {}}


# Global parser instance
text_parser = TextParser()