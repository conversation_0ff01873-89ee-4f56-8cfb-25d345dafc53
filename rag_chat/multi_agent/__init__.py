"""
多智能体监督者系统模块

这个模块实现了基于LangGraph的多智能体监督者架构，包括：
- 意图识别和分析
- 专门化Agent（MCP、知识库、多模态）
- 统一的LangGraph问答系统
- 监督者协调机制
"""

from .intent_analyzer import IntentAnalyzer, IntentAnalysisResult, create_intent_analyzer
from .unified_graph import UnifiedMultiAgentGraph, UnifiedQAState, create_unified_multi_agent_graph
from .agents import create_simple_chat_agent, create_knowledge_agent, create_mcp_agent, create_multimodal_agent

__version__ = "1.0.0"
__all__ = [
    "IntentAnalyzer",
    "IntentAnalysisResult", 
    "create_intent_analyzer",
    "UnifiedMultiAgentGraph",
    "UnifiedQAState",
    "create_unified_multi_agent_graph",
    "create_simple_chat_agent",
    "create_knowledge_agent",
    "create_mcp_agent", 
    "create_multimodal_agent"
]