"""
ASR语音识别API接口
提供短语音（非流式）和长语音（转写）识别的HTTP POST接口
支持 SILK 格式自动转换
"""

import os
import json
import base64
import asyncio
import tempfile
import shutil
import logging
from datetime import datetime
from uuid import uuid4
from typing import Optional
from contextlib import asynccontextmanager
from fastapi import APIRouter, File, UploadFile, Form, Request, HTTPException, Header
from pydantic import BaseModel

from asr_client import ASRClient
from audio_converter import get_audio_converter_optimized

# 配置日志
logger = logging.getLogger(__name__)

# 创建路由
router = APIRouter()

# 临时目录配置
BASE_DIR = os.getcwd()  # 获取当前工作目录
ASR_TEMP_DIR = os.path.join(BASE_DIR, "temp", "asr_uploads")

# 确保临时目录存在
def ensure_temp_directory():
    """确保临时目录存在"""
    try:
        os.makedirs(ASR_TEMP_DIR, exist_ok=True)
        logger.info(f"临时目录已创建: {ASR_TEMP_DIR}")
    except Exception as e:
        logger.error(f"创建临时目录失败: {e}")
        raise

# 初始化时创建目录
ensure_temp_directory()

# 支持的音频格式（扩展支持）
# SILK 会自动转换为符合要求的格式：单声道、采样率16K、编码pcm_s16le、位深16k
ALLOWED_AUDIO_EXTENSIONS = ['.wav', '.pcm', '.silk', '.mp3', '.aac', '.m4a', '.ogg', '.flac']

# 文件大小限制
SHORT_AUDIO_MAX_SIZE = 5 * 1024 * 1024   # 5MB
LONG_AUDIO_MAX_SIZE = 50 * 1024 * 1024   # 50MB

# ffmpeg 路径配置
FFMPEG_PATH = os.environ.get('FFMPEG_PATH', '/usr/local/bin/ffmpeg')


class ASRRequest(BaseModel):
    """ASR请求模型"""
    audio_base64: str


class ASRResponse(BaseModel):
    """ASR响应模型"""
    success: bool
    message: str
    data: Optional[dict] = None
    error: Optional[str] = None


class TranscribeResponse(BaseModel):
    """音频转写响应模型"""
    success: bool
    message: str
    transcription_text: Optional[str] = None
    speaker_summary: Optional[str] = None
    data: Optional[dict] = None
    error: Optional[str] = None


@asynccontextmanager
async def temporary_file_manager(filename: str, prefix: str = "asr"):
    """临时文件管理器，确保文件在使用后被清理"""
    timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
    unique_id = str(uuid4())[:8]
    temp_filename = f"{prefix}_{timestamp}_{unique_id}_{filename}"
    temp_file_path = os.path.join(ASR_TEMP_DIR, temp_filename)
    
    try:
        logger.info(f"创建临时文件: {temp_file_path}")
        yield temp_file_path
    finally:
        # 确保清理临时文件
        if os.path.exists(temp_file_path):
            try:
                os.remove(temp_file_path)
                logger.info(f"临时文件已清理: {temp_file_path}")
            except Exception as e:
                logger.error(f"清理临时文件失败: {temp_file_path}, 错误: {e}")


def validate_audio_file(filename: str) -> str:
    """验证音频文件格式"""
    file_ext = os.path.splitext(filename)[1].lower()
    if file_ext not in ALLOWED_AUDIO_EXTENSIONS:
        raise HTTPException(
            status_code=400,
            detail=f"不支持的音频格式: {file_ext}，支持格式: {ALLOWED_AUDIO_EXTENSIONS}。SILK、MP3等格式会自动转换为标准WAV格式。"
        )
    return file_ext


def validate_auth_headers(x_api_key: str = None, x_username: str = None):
    """验证认证头部"""
    if not x_username:
        raise HTTPException(status_code=401, detail="缺少用户名")
    if not x_api_key:
        raise HTTPException(status_code=401, detail="缺少API密钥")


async def process_audio_input(
    file: UploadFile = None,
    audio_base64: str = None,
    max_size: int = SHORT_AUDIO_MAX_SIZE,
    prefix: str = "asr"
) -> bytes:
    """处理音频输入，自动转换格式并返回标准WAV音频数据"""
    
    original_audio_data = None
    filename = None
    
    if file and file.filename:
        # 方式1：文件上传
        filename = file.filename
        validate_audio_file(filename)
        
        async with temporary_file_manager(filename, prefix) as temp_file_path:
            # 保存上传的文件
            with open(temp_file_path, "wb") as buffer:
                shutil.copyfileobj(file.file, buffer)
            
            # 读取原始音频数据
            with open(temp_file_path, "rb") as f:
                original_audio_data = f.read()
        
        logger.info(f"{prefix} - 文件上传: {filename}, 原始大小: {len(original_audio_data)} bytes")
    
    elif audio_base64:
        # 方式2：Base64数据
        try:
            original_audio_data = base64.b64decode(audio_base64)
            filename = "audio_data.wav"  # 默认假设为WAV
        except Exception as e:
            raise HTTPException(status_code=400, detail=f"Base64解码失败: {str(e)}")
        
        logger.info(f"{prefix} - Base64数据, 原始大小: {len(original_audio_data)} bytes")
    
    else:
        raise HTTPException(status_code=400, detail="请提供音频文件或Base64编码的音频数据")
    
    # 检查原始数据大小
    if len(original_audio_data) > max_size:
        size_mb = max_size / (1024 * 1024)
        raise HTTPException(
            status_code=400,
            detail=f"音频数据过大: {len(original_audio_data)} bytes，建议小于{size_mb:.0f}MB"
        )
    
    # 音频格式转换
    try:
        file_ext = os.path.splitext(filename)[1].lower()
        
        # 如果是标准格式，直接返回
        if file_ext in ['.wav', '.pcm']:
            logger.info(f"音频格式无需转换: {file_ext}")
            return original_audio_data
        
        # 需要转换的格式
        logger.info(f"开始转换音频格式: {file_ext} -> WAV")
        
        # 获取音频转换器
        converter = get_audio_converter_optimized(FFMPEG_PATH)
        
        # 执行转换
        converted_data, conversion_info = converter.convert_audio(
            original_audio_data,
            filename,
            target_sample_rate=16000
        )
        
        logger.info(f"音频转换成功: {conversion_info}")
        logger.info(f"转换后大小: {len(converted_data)} bytes")
        
        return converted_data
        
    except Exception as e:
        logger.error(f"音频格式转换失败: {e}")
        raise HTTPException(
            status_code=400,
            detail=f"音频格式转换失败: {str(e)}。请确保音频文件格式正确且未损坏。"
        )


@router.post("/jiliang/asr")
async def asr_recognize_short(
    request: Request,
    file: UploadFile = File(None, description="音频文件（支持 WAV、PCM、SILK、MP3 等格式）"),
    audio_base64: str = Form(None, description="Base64编码的音频数据"),
    x_api_key: str = Header(None, alias="X-API-Key"),
    x_username: str = Header(None, alias="X-Username")
):
    """
    短语音非流式识别接口

    支持两种输入方式：
    1. 上传音频文件 (file 参数) - 支持多种格式，自动转换
    2. Base64编码的音频数据 (audio_base64 参数)
    
    支持的音频格式：
    - WAV, PCM (直接支持)
    - SILK (微信语音格式，自动转换)
    - MP3, AAC, M4A, OGG, FLAC (自动转换)
    
    所有格式会自动转换为：单声道、16kHz采样率、16bit位深的WAV格式
    
    适用于较短的音频文件（建议1分钟以内）
    
    注意：productId、userName、password 已设置为固定默认值，无需传入
    """
    
    try:
        # 验证认证信息
        validate_auth_headers(x_api_key, x_username)
        
        # 处理音频数据（包含格式转换）
        audio_data = await process_audio_input(
            file=file,
            audio_base64=audio_base64,
            max_size=SHORT_AUDIO_MAX_SIZE,
            prefix="short_asr"
        )
        
        # 创建ASR客户端并执行短语音识别
        client = ASRClient()
        result = await client.recognize_short_audio(audio_data)
        
        if result["success"]:
            return {
                "success": True,
                "message": "短语音识别成功",
                "data": result["data"]
            }
        else:
            return {
                "success": False,
                "message": f"短语音识别失败: {result.get('message', '未知错误')}",
                "error": result.get("error")
            }
    
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"短语音ASR识别异常: {e}", exc_info=True)
        return {
            "success": False,
            "message": f"短语音识别失败: {str(e)}",
            "error": str(e)
        }


@router.post("/jiliang/long_asr/transcribe")
async def asr_transcribe_audio(
    request: Request,
    file: UploadFile = File(None, description="音频文件（支持 WAV、PCM、SILK、MP3 等格式）"),
    audio_base64: str = Form(None, description="Base64编码的音频数据"),
    identify_speakers: bool = Form(False, description="是否区分发言人"),
    speakers_num: int = Form(2, description="发言人数量"),
    is_correct_text: bool = Form(False, description="是否进行语篇规整/文本纠错"),
    speaker_summary_needed: bool = Form(False, description="是否需要发言人总结"),
    x_api_key: str = Header(None, alias="X-API-Key"),
    x_username: str = Header(None, alias="X-Username")
):
    """
    音频转写服务接口

    支持两种输入方式：
    1. 上传音频文件 (file 参数) - 支持多种格式，自动转换
    2. Base64编码的音频数据 (audio_base64 参数)

    支持的音频格式：
    - WAV, PCM (直接支持)
    - SILK (微信语音格式，自动转换)
    - MP3, AAC, M4A, OGG, FLAC (自动转换)
    
    所有格式会自动转换为：单声道、16kHz采样率、16bit位深的WAV格式

    适用于较长的音频文件，提供转写文本和可选的发言人总结
    
    参数说明：
    - identify_speakers: 是否区分发言人
    - speakers_num: 发言人数量（仅当identify_speakers=true时有效）
    - is_correct_text: 是否进行语篇规整/文本纠错
    - speaker_summary_needed: 是否需要发言人总结（仅当identify_speakers=true时有效）
    """
    
    try:
        # 验证认证信息
        validate_auth_headers(x_api_key, x_username)
        
        # 处理音频数据（包含格式转换）
        audio_data = await process_audio_input(
            file=file,
            audio_base64=audio_base64,
            max_size=LONG_AUDIO_MAX_SIZE,
            prefix="transcribe"
        )
        
        # 创建ASR客户端并执行音频转写
        client = ASRClient()
        result = await client.transcribe_audio(
            audio_data,
            identify_speakers=identify_speakers,
            speakers_num=speakers_num,
            is_correct_text=is_correct_text,
            speaker_summary_needed=speaker_summary_needed
        )
        
        if result["success"]:
            data = result.get("data", {})
            transcription_text = data.get("transcription_text", "")
            speaker_summary = data.get("speaker_summary")
            
            logger.info(f"音频转写成功，文本长度: {len(transcription_text)} 字符")
            
            return {
                "success": True,
                "message": "音频转写成功",
                "transcription_text": transcription_text,
                "speaker_summary": speaker_summary,
                "data": data
            }
        else:
            return {
                "success": False,
                "message": f"音频转写失败: {result.get('message', '未知错误')}",
                "error": result.get("error"),
                "transcription_text": None,
                "speaker_summary": None
            }
    
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"音频转写异常: {e}", exc_info=True)
        return {
            "success": False,
            "message": f"音频转写失败: {str(e)}",
            "error": str(e),
            "transcription_text": None,
            "speaker_summary": None
        }


@router.get("/jiliang/asr/formats")
async def get_supported_formats():
    """
    获取支持的音频格式列表
    """
    try:
        converter = get_audio_converter_optimized(FFMPEG_PATH)
        supported_formats = converter.get_supported_formats()
        
        return {
            "success": True,
            "message": "获取支持格式成功",
            "supported_formats": supported_formats,
            "format_info": {
                "direct_support": [".wav", ".pcm"],
                "auto_convert": [fmt for fmt in supported_formats if fmt not in [".wav", ".pcm"]],
                "output_format": "WAV (单声道, 16kHz, 16bit)",
                "ffmpeg_path": FFMPEG_PATH,
                "ffmpeg_available": converter.ffmpeg_available
            }
        }
    except Exception as e:
        logger.error(f"获取支持格式失败: {e}")
        return {
            "success": False,
            "message": f"获取支持格式失败: {str(e)}",
            "error": str(e)
        }


@router.get("/jiliang/asr/health")
async def health_check():
    """
    健康检查接口
    """
    try:
        # 检查临时目录
        temp_dir_status = os.path.exists(ASR_TEMP_DIR) and os.access(ASR_TEMP_DIR, os.W_OK)
        
        # 检查转换器
        converter_status = False
        supported_formats = []
        converter_info = {}
        
        try:
            converter = get_audio_converter_optimized(FFMPEG_PATH)
            supported_formats = converter.get_supported_formats()
            converter_info = converter.get_status_report()
            converter_status = True
        except Exception as e:
            logger.warning(f"转换器检查失败: {e}")
        
        return {
            "success": True,
            "message": "健康检查完成",
            "status": {
                "temp_directory": {
                    "path": ASR_TEMP_DIR,
                    "available": temp_dir_status
                },
                "audio_converter": {
                    "available": converter_status,
                    "supported_formats": supported_formats,
                    "details": converter_info
                }
            }
        }
    except Exception as e:
        logger.error(f"健康检查失败: {e}")
        return {
            "success": False,
            "message": f"健康检查失败: {str(e)}",
            "error": str(e)
        }


def cleanup_old_temp_files(max_age_hours: int = 24):
    """清理超过指定时间的临时文件"""
    try:
        if not os.path.exists(ASR_TEMP_DIR):
            return
        
        import time
        current_time = time.time()
        max_age_seconds = max_age_hours * 3600
        cleaned_count = 0
        
        for filename in os.listdir(ASR_TEMP_DIR):
            file_path = os.path.join(ASR_TEMP_DIR, filename)
            if os.path.isfile(file_path):
                file_age = current_time - os.path.getctime(file_path)
                if file_age > max_age_seconds:
                    try:
                        os.remove(file_path)
                        cleaned_count += 1
                        logger.debug(f"清理过期临时文件: {filename}")
                    except Exception as e:
                        logger.error(f"清理过期文件失败: {filename}, 错误: {e}")
        
        if cleaned_count > 0:
            logger.info(f"清理了 {cleaned_count} 个过期临时文件")
    
    except Exception as e:
        logger.error(f"清理临时文件异常: {e}")


# 应用启动时的清理任务
async def startup_cleanup():
    """应用启动时的清理任务"""
    logger.info("执行启动时的临时文件清理...")
    cleanup_old_temp_files(max_age_hours=1)  # 清理超过1小时的文件
    
    # 检查音频转换器是否可用
    try:
        converter = get_audio_converter_optimized(FFMPEG_PATH)
        supported_formats = converter.get_supported_formats()
        logger.info(f"音频转换器初始化成功，支持格式: {supported_formats}")
        
        # 获取状态报告
        status_report = converter.get_status_report()
        logger.info("音频转换器状态:")
        for key, value in status_report.items():
            logger.info(f"  {key}: {value}")
            
    except Exception as e:
        logger.warning(f"音频转换器初始化警告: {e}")
        logger.warning("某些音频格式转换功能可能不可用")


if __name__ == "__main__":
    import uvicorn
    from fastapi import FastAPI
    
    app = FastAPI(
        title="ASR语音识别服务",
        version="2.2.0",
        description="支持短语音和长语音识别，自动转换SILK等多种音频格式",
        on_startup=[startup_cleanup]
    )
    app.include_router(router)
    
    uvicorn.run(app, host="0.0.0.0", port=8000)