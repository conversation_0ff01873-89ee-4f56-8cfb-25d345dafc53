"""
Typing compatibility module for Python 3.10 support.
Provides backward compatibility for typing features introduced in Python 3.11+.
Must be imported before any module that uses langmem.
"""

import sys
import typing

def setup_typing_compatibility():
    """Setup typing compatibility for Python 3.10"""
    if sys.version_info < (3, 11):
        try:
            from typing_extensions import NotRequired, Required
            # Monkey patch typing module to include NotRequired and Required
            if not hasattr(typing, 'NotRequired'):
                typing.NotRequired = NotRequired
            if not hasattr(typing, 'Required'):
                typing.Required = Required
        except ImportError:
            # Fallback: create dummy implementations that act as passthroughs
            class _NotRequired:
                def __class_getitem__(cls, item):
                    return item
            
            class _Required:
                def __class_getitem__(cls, item):
                    return item
            
            if not hasattr(typing, 'NotRequired'):
                typing.NotRequired = _NotRequired
            if not hasattr(typing, 'Required'):
                typing.Required = _Required

# Auto-setup when module is imported
setup_typing_compatibility()