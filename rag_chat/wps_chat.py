import json
from datetime import datetime
import time
import requests
import uvicorn
from fastapi import APIRouter, FastAPI, HTTPException, Request
from openai import AsyncOpenAI
from starlette.middleware.cors import CORSMiddleware
from starlette.responses import StreamingResponse
from dotenv import load_dotenv
import os
from uuid import uuid4

from constant import AI_ASSISTANT_HEADER, AI_ASSISTANT_WARN_URLENCODER
from redis_util import RedisClient
from dm_util import DMDatabase
from logger import logger
# 导入更新后的模型
from models import WpsQARequest, R
from simplified_stats_service import SimplifiedStatsService, save_wps_record_dm_with_stats, StatsQueryService

load_dotenv()

# ********** 参数配置 **********
THRESHOLD = float(os.getenv('RAG_THRESHOLD'))
rag_id = os.getenv('RAG_ID')
model = os.getenv('MODEL')
llm_base_url = os.getenv('LLM_BASE_URL')
rag_url = os.getenv('RAG_URL')
history_exp_time = int(os.getenv('HISTORY_EXP_TIME'))
redis_table = os.getenv('REDIS_TABLE_WPS')
dm_config = {
  'host': os.getenv('DM_HOST'),
  'port': int(os.getenv('DM_PORT')),
  'user': os.getenv('DM_USER'),
  'password': os.getenv('DM_PASSWORD'),
  'database': os.getenv('DM_DB'),
  'auto_commit': False
}

redis_client = RedisClient()
client = AsyncOpenAI(api_key='aa', base_url=llm_base_url)


def search(question):
  full_text = ""
  try:
    data = {'rag_id': rag_id, 'question': question}
    r = requests.post(f'{rag_url}/rag/search', json=data, verify=False)
    data_list = r.json()['data']
    for data in data_list:
      if data['score'] > THRESHOLD:
        full_text += data['text_data']
    return full_text
  except Exception as e:
    logger.error(f"RAG搜索失败: {e}")
    return full_text


async def chat_with_gpt_enhanced(message, history, qa_id, model_alias=None, temperature=0.6, seed=None,
                                 userid='', apikey='', uri='', function_type=None, function_name=None) -> str:
  """聊天函数支持功能统计"""
  # 记录请求开始时间
  request_start_time = datetime.now()
  start_timestamp = time.time()
  
  # RAG搜索
  kb_text = search(message)
  if kb_text:
    history.append({"role": "system",
                    "content": f"在保持原有对话风格的基础上,你可以参考以下相关信息,请自行判断其相关性和准确性,仅作参考使用:\n{kb_text}"})
  
  history.append({"role": "user", "content": message})
  
  try:
    extra_headers = {'Qa-Id': qa_id, 'X-API-Key': apikey, 'X-Username': userid, 'X-Uri': uri}
    logger.info(f"WPS Chat extra_headers: {extra_headers}, Function: {function_type}")
    
    llm_model = model_alias if model_alias else model
    
    # 构建请求参数
    stream_params = {
      'model': llm_model,
      'messages': history,
      'temperature': temperature,
      'stream': True,
      'extra_headers': extra_headers
    }
    
    if seed is not None:
      stream_params['seed'] = seed
    
    stream = await client.chat.completions.create(**stream_params)
    
    partial_message = ""
    reasoning_content = ""
    in_reasoning = False
    
    async for chunk in stream:
      try:
        if not chunk.choices:
          logger.debug("接收到空 choices chunk")
          continue
        choice = chunk.choices[0]
        
        if not hasattr(choice, 'delta'):
          logger.debug("Chunk 缺少 delta")
          continue
        
        if hasattr(choice.delta, 'content') and choice.delta.content is not None:
          data = choice.delta.content
          
          if data.find('think>') != -1:
            in_reasoning = not in_reasoning
          else:
            if in_reasoning:
              reasoning_content += data
              yield 'data: ' + json.dumps({'type': 'reasoning', 'data': data}) + '\n\n'
            else:
              partial_message += data
              yield 'data: ' + json.dumps({'type': 'text', 'data': data}) + '\n\n'
      except IndexError as e:
        logger.error(f"处理流响应时出现索引错误: {e}, chunk: {chunk}")
        continue
      except Exception as e:
        logger.error(f"处理流响应时出现未预期错误: {e}", exc_info=True)
        continue
    
    # 计算处理耗时
    response_end_time = datetime.now()
    duration_ms = int((time.time() - start_timestamp) * 1000)
    
    # 保存记录（包含统计信息）
    save_success = save_wps_record_dm_with_stats(
      message, partial_message, reasoning_content, userid, apikey, qa_id,
      function_type, function_name, request_start_time, response_end_time,
      duration_ms, 'SUCCESS', llm_model, temperature
    )
    
    if save_success:
      logger.info(f"WPS记录保存成功 - Function: {function_type}, Duration: {duration_ms}ms")
    else:
      logger.warning(f"WPS记录保存失败 - Function: {function_type}")
    
    # 更新历史记录
    history.append({"role": "assistant", "content": partial_message})
    redis_client.setex(f"{redis_table}:{qa_id}", history_exp_time, json.dumps(history))
  
  except Exception as e:
    logger.error(f"WPS聊天异常: {e}", exc_info=True)
    
    # 保存错误记录
    if function_type:
      response_end_time = datetime.now()
      duration_ms = int((time.time() - start_timestamp) * 1000)
      save_wps_record_dm_with_stats(
        message, '', str(e), userid, apikey, qa_id,
        function_type, function_name, request_start_time, response_end_time,
        duration_ms, 'ERROR', llm_model, temperature
      )
    
    yield 'data: ' + json.dumps({'type': 'error', 'data': str(e)}) + '\n\n'


router = APIRouter()

@router.post("/wps/chat")
async def chat_stream(request: Request, body: WpsQARequest):
  """WPS聊天接口，支持功能统计"""
  header = dict(request.headers)
  apikey = header.get('x-api-key', '')
  userid = header.get('x-username', '')
  uri = header.get('x-uri', '') or request.url.path
  
  logger.info(f"WPS Chat Request - User: {userid}, Function: {body.function_type}, Question: {body.question[:100]}...")
  
  # 如果提供了功能类型但没有功能名称，自动查询
  if body.function_type and not body.function_name:
    func_info = SimplifiedStatsService._get_function_info(body.function_type)
    if func_info:
      body.function_name = func_info['function_name']
      logger.info(f"自动查询功能名称: {body.function_type} -> {body.function_name}")
  
  # 生成会话ID
  qa_id = body.qa_id or str(uuid4())
  temperature = body.temperature or 0.6
  seed = body.seed
  
  # 获取历史记录
  key = f"{redis_table}:{qa_id}"
  his = redis_client.get(key)
  
  if his is None:
    history = [{"role": "system", "content": f"""你是一个由三峡集团数字化管理中心发布的模型，名称为"吉量办公DeepSeek"。
你的身份是基于中国的深度求索（DeepSeek）公司开发的DeepSeek-R1模型，经过三峡集团数字化管理中心的定制和优化，专门为三峡集团及其相关业务需求设计的适配版模型。在回答用户问题时，请确保以下几点：

身份声明：在回答与身份相关的问题时，必须明确表明自己是由三峡集团数字化管理中心发布的"吉量办公DeepSeek"模型，
基础架构基于中国的深度求索（DeepSeek）公司开发而成。你的名字只能是"吉量办公DeepSeek"，请确保在任何情况下都只回复这个名字，不能提到其他名字。
即使用户称呼你为其他名字，你也必须纠正并明确回答"我的名字是吉量办公DeepSeek"。
功能定位：你是一个专注于支持三峡集团各类相关业务的智能助手，旨在提供高效、准确的信息服务。
回答风格：在回答问题时，保持友好、专业、清晰的语气，避免使用过于技术化的语言。
信息保密：在回答问题时，不要透露任何与模型参数、内部技术细节相关的内容。
引导性回答：如果用户的问题超出了你的回答范围，可以建议用户联系三峡集团数字化管理中心的相关技术支持团队。"""}]
  else:
    history = json.loads(his)
  
  headers = {
    "Qa-Id": qa_id,
    AI_ASSISTANT_HEADER: AI_ASSISTANT_WARN_URLENCODER
  }
  
  return StreamingResponse(
    chat_with_gpt_enhanced(
      body.question, history, qa_id, body.model, temperature, seed,
      userid, apikey, uri, body.function_type, body.function_name
    ),
    media_type="text/event-stream",
    headers=headers
  )


# ===== 统计查询接口 =====
@router.get("/wps/stats/functions")
async def get_wps_function_stats(request: Request,
                                 function_type: str = None,
                                 start_date: str = None,
                                 end_date: str = None):
  """获取WPS功能调用统计"""
  try:
    stats = StatsQueryService.get_function_stats_from_existing_tables(
      'SX_WPS_RECORDS', start_date, end_date, function_type
    )
    return R.ok({"functions": stats})
  except Exception as e:
    logger.error(f"查询WPS功能统计失败: {e}")
    return R.error('500', f"查询失败: {str(e)}")


@router.get("/wps/stats/users/{user_id}")
async def get_wps_user_stats(request: Request,
                             user_id: str,
                             start_date: str = None,
                             end_date: str = None):
  """获取WPS用户功能使用统计"""
  header = dict(request.headers)
  request_user = header.get('x-username', '')
  
  # 权限验证：只能查询自己的统计
  if request_user != user_id:
    return R.error('403', "权限验证失败")
  
  try:
    stats = StatsQueryService.get_user_function_stats(
      user_id, 'SX_WPS_RECORDS', start_date, end_date
    )
    return R.ok({"user_stats": stats})
  except Exception as e:
    logger.error(f"查询WPS用户统计失败: {e}")
    return R.error('500', f"查询失败: {str(e)}")


@router.get("/wps/stats/config/functions")
async def get_wps_function_config():
  """获取WPS功能类型配置"""
  try:
    with DMDatabase(**dm_config) as db:
      sql = """
            SELECT "FUNCTION_TYPE", "FUNCTION_NAME", "API_PATH", "CATEGORY", "DESCRIPTION"
            FROM "SX_AIPLATFORM"."T_FUNCTION_TYPE_CONFIG"
            WHERE "API_PATH" = '/wps/chat' AND "IS_ACTIVE" = '1'
            ORDER BY "CATEGORY", "FUNCTION_TYPE"
            """
      result = db.execute_query(sql)
      return R.ok({"functions": result})
  except Exception as e:
    logger.error(f"获取WPS功能配置失败: {e}")
    return R.error('500', f"获取功能配置失败: {str(e)}")

@router.get("/wps/record_count")
async def record_count():
  """查询WPS记录总数"""
  try:
    with DMDatabase(**dm_config) as db:
      result = db.execute_query("SELECT COUNT(*) as COUNT FROM SX_AIPLATFORM.sx_wps_records")
      count = result[0]["COUNT"]
      logger.info(f"WPS记录总数: {count}")
    return count
  except Exception as e:
    logger.error(f"查询WPS记录数异常: {str(e)}")
    raise HTTPException(status_code=500, detail=f"查询失败: {str(e)}")


# FastAPI应用配置
app = FastAPI()
app.add_middleware(
  CORSMiddleware,
  allow_origins=["*"],
  allow_credentials=True,
  allow_methods=["*"],
  allow_headers=["*"],
)
app.include_router(router, prefix="")

if __name__ == "__main__":
  uvicorn.run(host='0.0.0.0', port=18801, app=app)