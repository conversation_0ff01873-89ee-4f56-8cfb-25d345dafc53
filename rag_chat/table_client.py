import json
import base64
import requests
from typing import Dict, Any, Optional, List
import logging
import os
from urllib3.exceptions import InsecureRequestWarning

requests.packages.urllib3.disable_warnings(InsecureRequestWarning)

# 配置日志
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)


class DocumentOCRClient:
  """表格OCR识别客户端"""
  
  def __init__(self, base_url: str = "https://aimp.ctg.com.cn", access_code: str = None):
    """
        初始化表格OCR客户端

        Args:
            base_url: OCR服务基础URL
            access_code: 访问密钥，如果不提供则使用默认值
        """
    self.base_url = base_url.rstrip('/')
    self.access_code = access_code or "6D7AB2BA9B3AEA8B5379757FCC31109C"
    self.ocr_url = f"{self.base_url}/Others/tywzsb"
    self.timeout = 120
    
    logger.info(f"初始化OCR客户端: {self.ocr_url}")
  
  def recognize(self,
                image_data: bytes,
                object_type: str = "general_v5",
                type_param: str = "st_ocrapi",
                languagetype: str = "CHN_ENG",
                line_probability: bool = False,
                img_direction: Optional[str] = None,
                rec_only: Optional[str] = None,
                auto_detect_langtype: bool = False,
                eng_granularity: str = "word",
                disp_line_poly: bool = False,
                disp_paragraph_poly: bool = False) -> Dict[str, Any]:
    """
        表格OCR识别

        Args:
            image_data: 图片二进制数据
            object_type: 识别类型，默认general_v5
            type_param: 返回类型，st_ocrapi 或 st_ocrapi_all
            languagetype: 语言类型，默认CHN_ENG
            line_probability: 是否返回行置信度
            img_direction: 方向判断参数
            rec_only: 跳过检测参数
            auto_detect_langtype: 自动检测语种
            eng_granularity: 英文粒度
            disp_line_poly: 是否返回行的多点坐标
            disp_paragraph_poly: 是否返回段落结果

        Returns:
            dict: 识别结果
        """
    try:
      image_base64 = base64.b64encode(image_data).decode('utf-8')
      logger.info(f"图片编码完成，原始大小: {len(image_data)} bytes")
      
      params = [
        f"object_type={object_type}",
        f"type={type_param}",
        f"languagetype={languagetype}",
        f"image={image_base64}"
      ]
      
      if line_probability:
        params.append("line_probability=true")
      if img_direction:
        params.append(f"imgDirection={img_direction}")
      if rec_only:
        params.append(f"RecOnly={rec_only}")
      if auto_detect_langtype:
        params.append("auto_detect_langtype=true")
      if eng_granularity:
        params.append(f"eng_granularity={eng_granularity}")
      if disp_line_poly:
        params.append("disp_line_poly=true")
      if disp_paragraph_poly:
        params.append("disp_paragraph_poly=true")
      
      request_str = "&".join(params)
      data_base64 = base64.b64encode(request_str.encode('utf-8')).decode('utf-8')
      
      payload = {
        "provider": "default",
        "data": data_base64
      }
      
      headers = {
        "Authorization": f"ACCESSCODE {self.access_code}",
        "Content-Type": "application/json",
        "User-Agent": "DocumentOCR/1.0",
        "Accept": "application/json"
      }
      
      logger.info("发送OCR请求...")
      
      response = requests.post(
        self.ocr_url,
        json=payload,
        headers=headers,
        timeout=self.timeout,
        verify=False
      )
      
      if response.status_code == 200:
        response_data = response.json()
        
        if 'result' not in response_data:
          return self._error_result("响应格式错误：缺少result字段", "INVALID_RESPONSE")

        result_str = base64.b64decode(response_data['result']).decode('utf-8')
        result_json = json.loads(result_str)

        parsed_result = self._parse_result(result_json)

        parsed_result['calc_time_ms'] = response_data.get('calc_time_ms')
        parsed_result['wait_time_ms'] = response_data.get('wait_time_ms')
        
        logger.info(f"OCR识别完成，处理时间: {parsed_result.get('calc_time_ms', 0)}ms")
        return parsed_result
      
      else:
        return self._error_result(f"HTTP错误: {response.status_code}", "HTTP_ERROR")
    
    except requests.exceptions.Timeout:
      return self._error_result("请求超时", "TIMEOUT")
    except requests.exceptions.ConnectionError:
      return self._error_result("连接失败", "CONNECTION_ERROR")
    except (json.JSONDecodeError, base64.binascii.Error) as e:
      return self._error_result(f"数据解析失败: {str(e)}", "PARSE_ERROR")
    except Exception as e:
      logger.error(f"OCR识别异常: {e}", exc_info=True)
      return self._error_result(f"识别异常: {str(e)}", "GENERAL_ERROR")
  
  def recognize_file(self, file_path: str, **kwargs) -> Dict[str, Any]:
    """
        从文件识别

        Args:
            file_path: 图片文件路径
            **kwargs: 其他识别参数

        Returns:
            dict: 识别结果
        """
    try:
      if not os.path.exists(file_path):
        return self._error_result(f"文件不存在: {file_path}", "FILE_NOT_FOUND")
      
      with open(file_path, 'rb') as f:
        image_data = f.read()
      
      logger.info(f"读取文件: {file_path}, 大小: {len(image_data)} bytes")
      return self.recognize(image_data, **kwargs)
    
    except PermissionError:
      return self._error_result(f"文件访问权限不足: {file_path}", "PERMISSION_ERROR")
    except Exception as e:
      return self._error_result(f"文件读取失败: {str(e)}", "FILE_READ_ERROR")
  
  def recognize_base64(self, image_base64: str, **kwargs) -> Dict[str, Any]:
    """
        从Base64字符串识别

        Args:
            image_base64: Base64编码的图片数据
            **kwargs: 其他识别参数

        Returns:
            dict: 识别结果
        """
    try:
      image_data = base64.b64decode(image_base64)
      return self.recognize(image_data, **kwargs)
    except Exception as e:
      return self._error_result(f"Base64解码失败: {str(e)}", "BASE64_DECODE_ERROR")
  
  def _parse_result(self, result_json: Dict[str, Any]) -> Dict[str, Any]:
    """解析OCR识别结果"""
    try:
      err_no = result_json.get('err_no', -1)
      err_msg = result_json.get('err_msg', 'UNKNOWN_ERROR')
      
      if err_no != 0:
        return {
          "success": False,
          "error_code": err_no,
          "error_message": err_msg,
          "text_lines": [],
          "full_text": "",
          "line_count": 0
        }

      ret_data = result_json.get('ret', [])
      
      # 提取文本行信息
      text_lines = []
      for line_data in ret_data:
        line_info = {
          "text": line_data.get('word', ''),
          "rect": line_data.get('rect', {}),
          "confidence": line_data.get('line_probability'),
          "poly_location": line_data.get('poly_location', {}),
          "charset": line_data.get('charset', [])
        }
        text_lines.append(line_info)

      full_text = '\n'.join([line['text'] for line in text_lines if line['text']])
      
      paragraphs = self._analyze_paragraphs(text_lines)
      
      return {
        "success": True,
        "error_code": 0,
        "error_message": "SUCCESS",
        "text_lines": text_lines,
        "full_text": full_text,
        "line_count": len(text_lines),
        "paragraphs": paragraphs,
        "image_info": result_json.get('image_info', {}),
        "querysign": result_json.get('querysign', ''),
        "logid": result_json.get('logid', ''),
        "raw_data": result_json
      }
    
    except Exception as e:
      logger.error(f"解析结果失败: {e}")
      return self._error_result(f"解析结果失败: {str(e)}", "PARSE_RESULT_ERROR")
  
  def _analyze_paragraphs(self, text_lines: List[Dict]) -> List[Dict]:
    """分析段落结构"""
    if not text_lines:
      return []
    
    try:
      paragraphs = []
      current_paragraph = []
      
      for i, line in enumerate(text_lines):
        if not line['text'].strip():
          continue
        
        current_paragraph.append(line)
        
        # 判断是否需要分段
        is_new_paragraph = False
        if i < len(text_lines) - 1:
          current_rect = line.get('rect', {})
          next_rect = text_lines[i + 1].get('rect', {})
          
          if all(key in current_rect for key in ['top', 'height']) and 'top' in next_rect:
            current_bottom = current_rect['top'] + current_rect['height']
            next_top = next_rect['top']
            line_gap = next_top - current_bottom
            
            # 行间距大于行高的一半则分段
            if line_gap > current_rect['height'] * 0.5:
              is_new_paragraph = True
        
        if i == len(text_lines) - 1 or is_new_paragraph:
          if current_paragraph:
            paragraph_text = ' '.join([p['text'] for p in current_paragraph if p['text']])
            paragraphs.append({
              "text": paragraph_text,
              "lines": current_paragraph.copy(),
              "line_count": len(current_paragraph)
            })
            current_paragraph = []
      
      return paragraphs
    
    except Exception as e:
      logger.error(f"段落分析失败: {e}")
      return []
  
  def _error_result(self, message: str, error_type: str) -> Dict[str, Any]:
    """生成错误结果"""
    return {
      "success": False,
      "message": message,
      "error_type": error_type,
      "text_lines": [],
      "full_text": "",
      "line_count": 0
    }


def ocr_file(file_path: str, **kwargs) -> Dict[str, Any]:
  """
    便捷函数：识别文件

    Args:
        file_path: 图片文件路径
        **kwargs: OCR参数

    Returns:
        dict: 识别结果
    """
  client = DocumentOCRClient()
  return client.recognize_file(file_path, **kwargs)


def ocr_base64(image_base64: str, **kwargs) -> Dict[str, Any]:
  """
    便捷函数：识别Base64数据

    Args:
        image_base64: Base64编码的图片
        **kwargs: OCR参数

    Returns:
        dict: 识别结果
    """
  client = DocumentOCRClient()
  return client.recognize_base64(image_base64, **kwargs)


def ocr_bytes(image_data: bytes, **kwargs) -> Dict[str, Any]:
  """
    便捷函数：识别字节数据

    Args:
        image_data: 图片字节数据
        **kwargs: OCR参数

    Returns:
        dict: 识别结果
    """
  client = DocumentOCRClient()
  return client.recognize(image_data, **kwargs)


# 使用示例
def main():
  """使用示例"""

  client = DocumentOCRClient()
  result = client.recognize_file("test.jpg", line_probability=True)

  result = ocr_file("test.jpg", line_probability=True)
  
  if result["success"]:
    print(f"识别到 {result['line_count']} 行文本")
    print(f"内容: {result['full_text'][:100]}...")
    print(f"处理时间: {result.get('calc_time_ms', 0)}ms")
  else:
    print(f"识别失败: {result['message']}")


if __name__ == "__main__":
  main()