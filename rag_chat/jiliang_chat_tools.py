from langchain_core.tools import tool
import requests
from rag_chat.logger import logger
import os
from typing import List, Optional, Dict, Any
import asyncio
import httpx
import json
import base64
from pathlib import Path
import tempfile
import time
import threading
from collections import defaultdict
from dotenv import load_dotenv
from pydantic import BaseModel, Field

load_dotenv(override=True)

rag_id = os.getenv("RAG_ID")
rag_url = os.getenv("RAG_URL")
THRESHOLD = float(os.getenv('RAG_THRESHOLD', '0.8'))

custom_rag_url = os.getenv("RAG_URL")

class SearchKnowledgeBaseSchema(BaseModel):
    """搜索三峡集团知识库的参数模式"""
    question: str = Field(
        ..., 
        description="搜索查询内容。可以是关键词、短语或完整问题。例如：'三峡大坝'、'水电站发电量'、'长江流域管理'等"
    )

@tool(args_schema=SearchKnowledgeBaseSchema)
async def search_knowledge_base(question: str) -> str:
    """
    搜索三峡集团专业知识库，获取权威的企业内部信息。
    返回的内容仍然需要你进行验证和判断。
    """
    full_text = ""
    try:
        data = {
            'rag_id': rag_id,
            'question': question
        }
        r = requests.post(f'{rag_url}/rag/search', json=data, verify=False, timeout=15)
        data_list = r.json()['data']
        for data in data_list:
            if data['score'] > THRESHOLD:
                full_text += data['text_data']
        logger.info(f"Knowledge base search result length: {len(full_text)}")
        return full_text
    except Exception as e:
        logger.error(f"Knowledge base search failed: {e}")
        return "查询知识库接口损坏，请联系管理员"

def create_custom_rag_search_tool(custom_rag_ids: List[str]):
    """创建自定义RAG搜索工具的工厂函数
    
    Args:
        custom_rag_ids: 自定义RAG知识库ID列表
        
    Returns:
        自定义RAG搜索工具
    """
    
    class SearchCustomKnowledgeBaseSchema(BaseModel):
        """搜索自定义知识库的参数模式"""
        question: str = Field(
            ..., 
            description="搜索查询内容。支持多种格式：关键词（如'技术方案'）、短语（如'设备维护流程'）或完整问题（如'如何进行设备安全检查？'）"
        )

    @tool(args_schema=SearchCustomKnowledgeBaseSchema)
    async def search_custom_knowledge_base(question: str) -> str:
        """
        搜索用户的自定义知识库，获取相关信息。
        返回的内容仍然需要你进行验证和判断。
        """
        async def fetch_rag(rag_id):
            try:
                data = {
                    'knowledgeBaseId': rag_id,
                    'question': question
                }
                async with httpx.AsyncClient(verify=False, timeout=15) as client:
                    r = await client.post(f'{custom_rag_url}/ai/aiKnowledgeBase/search', json=data, timeout=15)
                    if r.status_code == 200:
                        response_data = r.json()
                        if 'data' in response_data:
                            results = []
                            for item in response_data['data']:
                                text_content = item.get('textData', '') or item.get('text_data', '')
                                
                                if not text_content:
                                    continue
                                
                                # 构建清晰的结果格式
                                file_name = item.get('fileName', '')
                                start_page = item.get('startPage')
                                end_page = item.get('endPage')
                                file_id = item.get('fileId', '')
                                
                                # 构建详细的来源信息标识
                                if file_name:
                                    # 构建页码信息
                                    page_info = ""
                                    if start_page is not None and end_page is not None:
                                        if start_page == end_page:
                                            page_info = f"第{start_page}页"
                                        else:
                                            page_info = f"第{start_page}-{end_page}页"
                                    
                                    # 使用标准化格式
                                    if page_info:
                                        source_header = f"【文档来源：{file_name} - {page_info}】"
                                    else:
                                        source_header = f"【文档来源：{file_name}】"
                                    
                                    # 格式化最终结果：来源信息在前，内容在后，清晰分隔
                                    result_text = f"{source_header}\n{text_content}\n"
                                else:
                                    # 没有文件信息时的格式
                                    result_text = f"{text_content}\n"
                                
                                results.append(result_text)
                            return results
                    else:
                        logger.warning(f"Custom RAG search failed for ID {rag_id}: HTTP {r.status_code}")
            except Exception as rag_error:
                logger.error(f"Custom RAG search error for ID {rag_id}: {rag_error}")
            return []

        tasks = [fetch_rag(rag_id) for rag_id in custom_rag_ids]
        results = await asyncio.gather(*tasks)
        full_text = "".join([text for sublist in results for text in sublist])

        if full_text.strip():
            logger.info(f"Custom knowledge base search result length: {len(full_text)}")
            return full_text.strip()
        else:
            return "未在指定的自定义知识库中找到相关信息"

    return search_custom_knowledge_base


# ================== 文档处理工具 ==================

# 向量库管理器
class AsyncVectorStoreManager:
    """异步向量库管理器：支持缓存和延时删除"""
    
    def __init__(self, cache_timeout: int = 1800):  # 30分钟默认缓存
        self.cache_timeout = cache_timeout
        self.vector_stores = {}  # 存储向量库实例
        self.access_times = {}   # 记录最后访问时间
        self.cleanup_task = None
        self._shutdown = False  # 添加关闭标志
        self._start_cleanup_task()
        
    def _start_cleanup_task(self):
        """启动清理异步任务"""
        # 不在初始化时启动任务，而是在第一次使用时启动
        pass

    async def _ensure_cleanup_task(self):
        """确保清理任务已启动"""
        try:
            if self.cleanup_task is None or self.cleanup_task.done():
                self.cleanup_task = asyncio.create_task(self._cleanup_expired())
        except Exception as e:
            logger.warning(f"启动清理任务失败: {e}")

    async def _cleanup_expired(self):
        """异步清理过期的向量库"""
        while not self._shutdown:
            try:
                current_time = time.time()
                expired_keys = []
                
                for key, access_time in self.access_times.items():
                    if current_time - access_time > self.cache_timeout:
                        expired_keys.append(key)
                
                for key in expired_keys:
                    await self._remove_vector_store(key)
                    logger.info(f"清理过期向量库: {key}")
                
                # 使用可中断的sleep
                for _ in range(30):  # 每10秒检查一次关闭标志，总共5分钟
                    if self._shutdown:
                        break
                    await asyncio.sleep(10)
                    
            except asyncio.CancelledError:
                logger.info("向量库清理任务被取消")
                break
            except Exception as e:
                logger.error(f"向量库清理任务出错: {e}")
                await asyncio.sleep(60)
        
        logger.info("向量库清理任务优雅退出")
    
    async def shutdown(self):
        """优雅关闭向量库管理器"""
        logger.info("开始关闭AsyncVectorStoreManager...")
        self._shutdown = True
        
        # 取消清理任务
        if self.cleanup_task and not self.cleanup_task.done():
            self.cleanup_task.cancel()
            try:
                await self.cleanup_task
            except asyncio.CancelledError:
                logger.info("清理任务已取消")
        
        # 清理所有向量库
        for key in list(self.vector_stores.keys()):
            await self._remove_vector_store(key)
        
        logger.info("AsyncVectorStoreManager关闭完成")
    
    async def _remove_vector_store(self, key: str):
        """异步移除向量库并清理文件"""
        if key in self.vector_stores:
            try:
                # 异步清理临时文件
                if hasattr(self.vector_stores[key], '_temp_file'):
                    temp_file = getattr(self.vector_stores[key], '_temp_file')
                    import aiofiles.os
                    if await aiofiles.os.path.exists(temp_file):
                        await aiofiles.os.remove(temp_file)
                        logger.debug(f"清理临时文件: {temp_file}")
            except Exception as e:
                logger.warning(f"清理临时文件失败: {e}")
            
            del self.vector_stores[key]
            del self.access_times[key]
    
    async def get_or_create_vector_store(self, file_path: str, content: str):
        """异步获取或创建向量库"""
        # 确保清理任务已启动
        await self._ensure_cleanup_task()
        
        # 生成唯一key - 基于文件名、大小和内容采样hash，忽略临时路径
        import hashlib
        
        # 使用文件名（而非完整路径）
        filename = os.path.basename(file_path)
        
        # 快速内容指纹：使用文件大小 + 开头/中间/结尾内容片段
        content_len = len(content)
        if content_len <= 3000:
            # 小文件：使用完整内容
            content_sample = content
        else:
            # 大文件：使用开头1000 + 中间1000 + 结尾1000字符
            mid_pos = content_len // 2
            content_sample = (
                content[:1000] + 
                content[mid_pos-500:mid_pos+500] + 
                content[-1000:]
            )
        
        # 组合指纹：文件大小 + 内容采样
        fingerprint = f"{content_len}_{content_sample}"
        content_hash = hashlib.md5(fingerprint.encode('utf-8')).hexdigest()
        key = f"{abs(hash(filename))}_{content_hash}"
        
        # 更新访问时间
        self.access_times[key] = time.time()
        
        # 如果已存在，直接返回
        if key in self.vector_stores:
            logger.info(f"使用缓存的向量库: {key}")
            return self.vector_stores[key]
        
        # 异步创建新的向量库
        try:
            from langchain_text_splitters import RecursiveCharacterTextSplitter
            from langchain_milvus import Milvus
            from langchain_openai import OpenAIEmbeddings
            
            # 文档分块 - 带ID标记
            text_splitter = RecursiveCharacterTextSplitter(
                chunk_size=500,
                chunk_overlap=50,
                length_function=len,
            )
            raw_chunks = text_splitter.split_text(content)

            if not raw_chunks:
                return None
            
            # 为每个分块添加元数据和ID
            chunks_with_metadata = []
            for i, chunk in enumerate(raw_chunks):
                # 生成唯一的段落ID
                chunk_id = f"chunk_{i+1:03d}"  # 格式：chunk_001, chunk_002 等
                # 创建带元数据的文档对象
                chunk_metadata = {
                    "chunk_id": chunk_id,
                    "chunk_index": i,
                    "total_chunks": len(raw_chunks),
                    "source_file": os.path.basename(file_path)
                }
                chunks_with_metadata.append((chunk, chunk_metadata))
            
            # 提取纯文本用于向量化
            chunks = [chunk for chunk, _ in chunks_with_metadata]
            
            # 初始化embeddings - 根据环境选择不同配置
            base_url = os.getenv("EMBEDDING_BASE_URL", "")
            is_local = "127.0.0.1" in base_url or "localhost" in base_url
            
            if is_local:
                # 本地环境：使用标准OpenAI embeddings配置
                embeddings = OpenAIEmbeddings(
                    model=os.getenv("EMBEDDING_MODEL"),
                    api_key=os.getenv("OPENAI_API_KEY"),
                    base_url=base_url,
                    tiktoken_enabled=False,
                    check_embedding_ctx_length=False
                )
            else:
                # 生产环境：自定义embedding实现，支持 /encode 接口
                from typing import List
                
                class CustomEmbeddings:
                    def __init__(self, base_url: str):
                        self.base_url = base_url
                    
                    async def aembed_documents(self, texts: List[str]) -> List[List[float]]:
                        """异步编码文档列表"""
                        import httpx
                        
                        try:
                            async with httpx.AsyncClient(verify=False, timeout=30) as client:
                                # 使用生产环境的 /encode 接口
                                data = {
                                    "documents": texts,  # 文档列表
                                }
                                
                                response = await client.post(
                                    self.base_url,  # 直接使用完整的encode URL
                                    json=data
                                )
                                
                                if response.status_code == 200:
                                    result = response.json()
                                    # {"embeddings": [[...], [...]]}
                                    return result.get("data", [])
                                else:
                                    logger.error(f"Embedding API failed: {response.status_code}")
                                    return [[0.0] * 1536 for _ in texts]  # 返回默认维度的零向量
                        except Exception as e:
                            logger.error(f"Custom embedding error: {e}")
                            return [[0.0] * 1536 for _ in texts]  # 返回默认维度的零向量
                    
                    async def aembed_query(self, text: str) -> List[float]:
                        """异步编码单个查询"""
                        results = await self.aembed_documents([text])
                        return results[0] if results else [0.0] * 1536
                
                embeddings = CustomEmbeddings(
                    base_url=base_url
                )
            
            # 创建临时数据库文件
            with tempfile.NamedTemporaryFile(suffix='.db', delete=False) as tmp_file:
                db_path = tmp_file.name
            
            # 准备文档和元数据
            documents_with_metadata = []
            from langchain_core.documents import Document
            
            for i, (chunk, metadata) in enumerate(chunks_with_metadata):
                doc = Document(
                    page_content=chunk,
                    metadata=metadata
                )
                documents_with_metadata.append(doc)
            
            # 使用异步方法创建向量库（使用文档对象而不是纯文本）
            vector_store = await Milvus.afrom_documents(
                documents=documents_with_metadata,
                embedding=embeddings,
                connection_args={"uri": db_path},
                collection_name=f"doc_{key}",
                drop_old=True
            )
            
            # 保存临时文件路径用于后续清理
            setattr(vector_store, '_temp_file', db_path)
            
            # 缓存向量库
            self.vector_stores[key] = vector_store
            logger.info(f"创建新向量库: {key}")
            
            return vector_store
            
        except Exception as e:
            logger.error(f"创建向量库失败: {e}")
            return None

# 全局异步向量库管理器
_vector_manager = AsyncVectorStoreManager()

# 添加清理函数
async def cleanup_vector_manager():
    """清理全局向量库管理器"""
    await _vector_manager.shutdown()

# 注册程序退出时的清理函数
import atexit

def sync_cleanup():
    """同步清理函数，用于程序退出时调用"""
    try:
        loop = asyncio.get_event_loop()
        if loop.is_running():
            # 如果事件循环正在运行，创建任务
            asyncio.ensure_future(cleanup_vector_manager())
        else:
            # 如果事件循环未运行，直接运行
            asyncio.run(cleanup_vector_manager())
    except RuntimeError:
        # 如果无法获取事件循环，创建新的
        try:
            asyncio.run(cleanup_vector_manager())
        except Exception as e:
            logger.warning(f"程序退出时清理向量库管理器失败: {e}")

atexit.register(sync_cleanup)

class SearchDocumentWithRagSchema(BaseModel):
    """使用RAG技术搜索文档的参数模式"""
    file_paths: List[str] = Field(
        ..., 
        description="要搜索的文档文件路径列表。必须使用完整的绝对路径，从上传文件信息中获取。示例：['/Volumes/Extreme SSD/sx_ai/sx-ai-rag-chat/rag_chat/temp/document.pdf']",
        json_schema_extra={
            "examples": [["/Volumes/Extreme SSD/sx_ai/sx-ai-rag-chat/rag_chat/temp/document.pdf"]]
        }
    )
    query: str = Field(
        ..., 
        description="搜索查询内容。可以是：关键词（如'销售数据'）、短语（如'市场分析报告'）、完整问题（如'去年的销售增长率是多少？'）或复合查询（如'产品价格和销售策略'）"
    )
    describe_image: bool = Field(
        False, 
        description="是否启用图像内容描述功能。设为True时会使用多模态模型分析文档中的图片、图表等视觉内容，但会显著增加处理时间。"
    )
    k: int = Field(
        5, 
        description="返回的相关文档片段数量。建议值：简单查询用3-5个，复杂查询用8-10个，详细分析用15-20个。数量越多结果越全面但响应时间越长"
    )

@tool(args_schema=SearchDocumentWithRagSchema)
async def search_document_with_rag(file_paths: List[str], query: str, describe_image: bool = False, k: int = 5) -> str:
    """
    在上传的文档中搜索相关信息。
    返回的内容仍需要你进行验证和判断。
    如果你觉得信息不足，可以再次调用这个工具，扩大搜索范围或使用不同的查询内容。
    """
    try:
        from markitdown import MarkItDown
        from pathlib import Path
        from openai import OpenAI
        
        # 确保file_paths是列表
        if isinstance(file_paths, str):
            file_paths = [file_paths]
        
        # 检查文件是否存在
        existing_files = []
        for file_path in file_paths:
            if Path(file_path).exists():
                existing_files.append(file_path)
            else:
                logger.warning(f"文件不存在，跳过: {file_path}")
        
        if not existing_files:
            return "所有指定的文件都不存在"
        
        # 检查是否启用多模态描述
        if describe_image:
            client = OpenAI(
                api_key=os.getenv("OPENAI_API_KEY"),
                base_url=os.getenv("OPENAI_BASE_URL")
            )
            vlm_model = os.getenv("MULTIMODAL_MODEL", "qwen2-5-vl-72b")
            md = MarkItDown(llm_client=client, llm_model=vlm_model)
        else:
            md = MarkItDown()
        
        # 并发处理所有文件
        async def process_single_file(file_path):
            """异步处理单个文件"""
            try:
                # 使用MarkItDown提取文档内容
                result = md.convert(file_path)
                
                if not result or not result.text_content:
                    logger.warning(f"无法解析文档内容: {file_path}")
                    return None
                
                # 使用异步向量库管理器获取或创建向量库
                vector_store = await _vector_manager.get_or_create_vector_store(file_path, result.text_content)
                
                if not vector_store:
                    logger.warning(f"创建向量库失败: {file_path}")
                    return None
                
                # 异步执行相似性搜索，带分数
                relevant_docs_with_scores = await vector_store.asimilarity_search_with_score(query, k=k)
                
                # 为每个文档补充完整的文件来源信息
                filename = Path(file_path).name
                enhanced_docs = []
                
                for doc, score in relevant_docs_with_scores:
                    # 确保元数据包含所有必要信息
                    if 'source_file' not in doc.metadata:
                        doc.metadata['source_file'] = filename
                    if 'file_path' not in doc.metadata:
                        doc.metadata['file_path'] = file_path
                    
                    # 添加相似度分数
                    doc.metadata['similarity_score'] = round(score, 4)
                    
                    enhanced_docs.append(doc)
                
                return {
                    'docs': enhanced_docs,
                    'filename': filename
                }
                
            except Exception as e:
                logger.error(f"处理文件失败 {file_path}: {e}")
                return None
        
        # 并发处理所有文件
        tasks = [process_single_file(file_path) for file_path in existing_files]
        results = await asyncio.gather(*tasks, return_exceptions=True)
        
        # 整理结果
        all_relevant_docs = []
        processed_files = []
        
        for result in results:
            if result and not isinstance(result, Exception):
                all_relevant_docs.extend(result['docs'])
                processed_files.append(result['filename'])
        
        if not all_relevant_docs:
            return f"在文件 {', '.join(processed_files)} 中未找到相关内容"
        
        # 按相似度分数排序（从高到低）
        try:
            all_relevant_docs.sort(key=lambda x: x.metadata.get('similarity_score', 0), reverse=True)
        except:
            pass  # 如果排序失败，保持原顺序

        # 整理搜索结果，包含详细的来源信息
        result_content = []
        for i, doc in enumerate(all_relevant_docs, 1):
            # 提取详细的元数据信息
            source_file = doc.metadata.get('source_file', '未知文件')
            chunk_id = doc.metadata.get('chunk_id', f'段落{i}')
            chunk_index = doc.metadata.get('chunk_index', i-1)
            total_chunks = doc.metadata.get('total_chunks', '未知')
            similarity_score = doc.metadata.get('similarity_score', 0)
            
            # 构造详细的来源信息
            source_info = f"📄 文档: {source_file} | 🔖 段落ID: {chunk_id} | 📊 相似度: {similarity_score:.3f} | 📍 位置: {chunk_index+1}/{total_chunks}"
            
            # 格式化内容，使来源信息更加突出
            content = f"**片段 {i}**\n{source_info}\n\n{doc.page_content}"
            result_content.append(content)
        
        search_result = "\n\n" + "="*80 + "\n\n".join([""] + result_content)
        
        # 添加摘要信息
        total_files = len(processed_files)
        total_chunks = len(all_relevant_docs)
        avg_score = sum(doc.metadata.get('similarity_score', 0) for doc in all_relevant_docs) / total_chunks if total_chunks > 0 else 0
        
        summary = f"""
📋 **搜索摘要**
• 处理文件: {total_files} 个 ({', '.join(processed_files)})
• 找到相关段落: {total_chunks} 个
• 平均相似度: {avg_score:.3f}
• 查询内容: "{query}"
"""
        
        logger.info(f"在 {total_files} 个文件中找到 {total_chunks} 个相关片段，平均相似度: {avg_score:.3f}")
        return f"{summary}\n\n📖 **详细结果**{search_result}"
        
    except ImportError as e:
        return f"缺少必要的依赖: {str(e)}"
    except Exception as e:
        logger.error(f"RAG搜索失败: {e}")
        return f"RAG搜索失败: {str(e)}"


# ================== 直接文件读取工具 ==================

def calculate_file_size(file_path: str) -> int:
    """计算文件大小（字节）"""
    try:
        return Path(file_path).stat().st_size
    except:
        return 0

def get_intelligent_threshold(query: str, file_paths: List[str]) -> float:
    """基于查询类型和文件特征智能确定阈值（MB）"""
    
    # 位置导向查询：强烈倾向直接读取
    position_keywords = ['第几页', '第几行', '开头', '结尾', '前面', '后面', '起始', '顶部', '底部', '开始', '最后']
    if any(keyword in query for keyword in position_keywords):
        logger.info(f"位置导向查询，使用0.5MB阈值: {query}")
        return 0.5
    
    # 快速查阅类查询：倾向直接读取
    quick_keywords = ['显示', '查看', '读取', '获取', '打开']
    if any(keyword in query for keyword in quick_keywords) and len(query) < 20:
        logger.info(f"快速查阅查询，使用1.0MB阈值: {query}")
        return 1.0
    
    # 多文档查询：强烈倾向RAG搜索
    if len(file_paths) > 3:
        logger.info(f"多文档查询({len(file_paths)}个文件)，使用5.0MB阈值")
        return 5.0
    
    # 复杂分析型查询：倾向RAG搜索
    complex_analysis_keywords = ['深入分析', '详细总结', '全面对比', '综合评估', '深度理解']
    if any(keyword in query for keyword in complex_analysis_keywords):
        logger.info(f"复杂分析查询，使用4.0MB阈值: {query}")
        return 4.0
    
    # 一般分析型查询：倾向RAG搜索
    analysis_keywords = ['总结', '分析', '对比', '关系', '找出', '搜索', '查找', '相关']
    if any(keyword in query for keyword in analysis_keywords):
        logger.info(f"分析型查询，使用3.0MB阈值: {query}")
        return 3.0
    
    # 默认平衡阈值
    logger.info(f"使用默认2.0MB阈值")
    return 2.0

def choose_document_strategy(query: str, file_paths: List[str], size_threshold_mb: Optional[float] = None) -> str:
    """
    智能选择文档处理策略
    
    Args:
        query: 用户查询内容
        file_paths: 文件路径列表
        size_threshold_mb: 可选的手动阈值（MB），如不提供则使用智能默认值
        
    Returns:
        'direct_read' 或 'rag_search'
    """
    # 获取智能阈值或使用手动指定的阈值
    if size_threshold_mb is None:
        threshold_mb = get_intelligent_threshold(query, file_paths)
    else:
        # 限制手动阈值的合理范围
        threshold_mb = max(0.1, min(50.0, size_threshold_mb))
        logger.info(f"使用手动指定阈值: {threshold_mb}MB")
    
    size_threshold = int(threshold_mb * 1024 * 1024)  # 转换为字节
    
    # 检测位置导向的查询（高优先级，直接返回）
    position_keywords = ['第几页', '第几行', '开头', '结尾', '前面', '后面', '起始', '顶部', '底部', '开始', '最后']
    if any(keyword in query for keyword in position_keywords):
        logger.info(f"检测到位置导向查询，强制选择直接读取策略: {query}")
        return "direct_read"
    
    # 检测快速查阅类查询（高优先级）
    quick_keywords = ['显示', '查看', '读取', '获取', '打开']
    if any(keyword in query for keyword in quick_keywords) and len(query) < 20:
        logger.info(f"检测到快速查阅查询，优先选择直接读取策略: {query}")
        return "direct_read"
    
    # 检测分析型查询（优先使用RAG，不受文件大小限制）
    analysis_keywords = ['总结', '分析', '对比', '关系', '找出', '搜索', '查找', '相关']
    if any(keyword in query for keyword in analysis_keywords):
        logger.info(f"检测到分析型查询，强制选择RAG搜索策略: {query}")
        return "rag_search"
    
    # 检查文件大小（只对非分析型查询生效）
    total_size = sum(calculate_file_size(fp) for fp in file_paths if Path(fp).exists())
    if total_size < size_threshold:
        logger.info(f"文件大小 {total_size/1024:.1f}KB < 阈值 {threshold_mb}MB，选择直接读取策略")
        return "direct_read"
    
    # 默认策略：大文件用RAG，小文件用直接读取
    strategy = "rag_search" if total_size >= size_threshold else "direct_read"
    logger.info(f"使用默认策略: {strategy} (文件大小: {total_size/1024:.1f}KB, 阈值: {threshold_mb}MB)")
    return strategy

class ReadDocumentDirectlySchema(BaseModel):
    """直接读取文档内容的参数模式"""
    file_paths: List[str] = Field(
        ..., 
        description="要读取的文档文件路径列表。必须使用完整的绝对路径。",
        json_schema_extra={
            "examples": [["/Volumes/Extreme SSD/sx_ai/sx-ai-rag-chat/rag_chat/temp/document.pdf"]]
        }
    )
    start_line: int = Field(
        1, 
        description="起始行号（从1开始）。指定从文档的第几行开始读取内容"
    )
    line_count: int = Field(
        50, 
        description="读取的行数。建议值：快速预览用20-50行，详细阅读用100-200行"
    )
    search_keywords: Optional[List[str]] = Field(
        None, 
        description="可选的关键词过滤列表。如果提供，将优先显示包含这些关键词的行及其上下文"
    )
    describe_image: bool = Field(
        False, 
        description="是否启用图像内容描述功能（适用于包含图片的文档）"
    )

@tool(args_schema=ReadDocumentDirectlySchema)
async def read_document_directly(
    file_paths: List[str], 
    start_line: int = 1, 
    line_count: int = 50, 
    search_keywords: Optional[List[str]] = None,
    describe_image: bool = False
) -> str:
    """
    直接按行读取文档内容，适用于小文档或需要精确定位的场景。
    相比RAG搜索，此方法响应更快但不支持语义搜索。
    如果你觉得信息不足，可以再次调用这个工具，扩大读取范围或读取不同的范围。
    返回的内容仍需要你进行验证和判断。
    """
    try:
        from markitdown import MarkItDown
        from pathlib import Path
        from openai import OpenAI
        
        # 确保file_paths是列表
        if isinstance(file_paths, str):
            file_paths = [file_paths]
        
        # 检查文件是否存在
        existing_files = []
        for file_path in file_paths:
            if Path(file_path).exists():
                existing_files.append(file_path)
            else:
                logger.warning(f"文件不存在，跳过: {file_path}")
        
        if not existing_files:
            return "所有指定的文件都不存在"
        
        # 检查是否启用多模态描述
        if describe_image:
            client = OpenAI(
                api_key=os.getenv("OPENAI_API_KEY"),
                base_url=os.getenv("OPENAI_BASE_URL")
            )
            vlm_model = os.getenv("MULTIMODAL_MODEL", "qwen2-5-vl-72b")
            md = MarkItDown(llm_client=client, llm_model=vlm_model)
        else:
            md = MarkItDown()
        
        # 处理所有文件
        all_results = []
        
        for file_path in existing_files:
            try:
                # 使用MarkItDown转换文档
                result = md.convert(file_path)
                
                if not result or not result.text_content:
                    logger.warning(f"无法解析文档内容: {file_path}")
                    continue
                
                # 按行分割内容
                lines = result.text_content.split('\n')
                total_lines = len(lines)
                filename = Path(file_path).name
                
                logger.info(f"文件 {filename} 总行数: {total_lines}")
                
                # 如果提供了关键词，进行关键词搜索
                if search_keywords:
                    keyword_results = []
                    for i, line in enumerate(lines):
                        if any(keyword.lower() in line.lower() for keyword in search_keywords):
                            # 包含关键词的行及其前后各2行的上下文
                            context_start = max(0, i - 2)
                            context_end = min(total_lines, i + 3)
                            
                            context_lines = []
                            for j in range(context_start, context_end):
                                marker = ">>> " if j == i else "    "
                                context_lines.append(f"{marker}Line {j+1}: {lines[j]}")
                            
                            keyword_results.append("\n".join(context_lines))
                    
                    if keyword_results:
                        content = f"""
📄 **文件**: {filename} (总行数: {total_lines})
🔍 **关键词搜索结果** (关键词: {', '.join(search_keywords)})

{chr(10).join(keyword_results[:line_count//5])}  # 限制结果数量
"""
                    else:
                        content = f"📄 **文件**: {filename}\n❌ 未找到包含关键词 {search_keywords} 的内容"
                
                else:
                    # 直接按行号读取
                    if start_line > total_lines:
                        content = f"📄 **文件**: {filename}\n❌ 起始行号 {start_line} 超出文档总行数 {total_lines}"
                    else:
                        end_line = min(start_line + line_count - 1, total_lines)
                        selected_lines = lines[start_line-1:end_line]
                        
                        # 格式化输出
                        formatted_lines = []
                        for i, line in enumerate(selected_lines):
                            line_num = start_line + i
                            formatted_lines.append(f"Line {line_num}: {line}")
                        
                        content = f"""
📄 **文件**: {filename} (总行数: {total_lines})
📍 **读取范围**: 第 {start_line} - {end_line} 行

{chr(10).join(formatted_lines)}
"""
                
                all_results.append(content)
                
            except Exception as e:
                logger.error(f"处理文件失败 {file_path}: {e}")
                all_results.append(f"📄 **文件**: {Path(file_path).name}\n❌ 处理失败: {str(e)}")
        
        if not all_results:
            return "所有文件处理失败"
        
        # 合并所有结果
        final_result = "\n\n" + "="*80 + "\n\n".join([""] + all_results)
        
        # 添加处理摘要
        summary = f"""
📋 **直接读取摘要**
• 处理文件: {len(all_results)} 个
• 读取模式: {'关键词搜索' if search_keywords else '按行读取'}
• 图像描述: {'启用' if describe_image else '禁用'}
"""
        
        return f"{summary}\n\n📖 **文档内容**{final_result}"
        
    except ImportError as e:
        return f"缺少必要的依赖: {str(e)}"
    except Exception as e:
        logger.error(f"直接读取失败: {e}")
        return f"直接读取失败: {str(e)}"

class SmartDocumentSearchSchema(BaseModel):
    """智能文档搜索的参数模式（自动选择最佳策略）"""
    file_paths: List[str] = Field(
        ..., 
        description="要搜索的文档文件路径列表。必须使用完整的绝对路径。",
        json_schema_extra={
            "examples": [["/Volumes/Extreme SSD/sx_ai/sx-ai-rag-chat/rag_chat/temp/document.pdf"]]
        }
    )
    query: str = Field(
        ..., 
        description="搜索查询内容。支持位置查询（如'查看开头部分'）、关键词搜索（如'销售数据'）、分析型查询（如'总结主要内容'）等。系统会根据查询类型自动选择最优处理策略。"
    )
    describe_image: bool = Field(
        False, 
        description="是否启用图像内容描述功能"
    )
    size_threshold_mb: Optional[float] = Field(
        None,
        description="""文件大小阈值（MB），用于策略选择。如不提供将使用智能默认值：
        
📍 位置定位查询（如'查看开头'、'第几页'）→ 0.5MB（强烈倾向直接读取）
⚡ 快速查阅查询（如'显示'、'读取'且<20字符）→ 1.0MB（倾向直接读取）  
🧠 一般分析查询（如'总结'、'分析'）→ 3.0MB（倾向RAG搜索）
🔬 复杂分析查询（如'深入分析'、'详细总结'）→ 4.0MB（倾向RAG搜索）
📚 多文档查询（>3个文件）→ 5.0MB（强烈倾向RAG搜索）
⚖️ 默认平衡模式 → 2.0MB

特殊情况可手动指定（范围：0.1-50.0MB）：
• 超大文档深度分析：建议10.0MB以上
• 超小文档语义搜索：建议0.1MB以下"""
    )
    max_results: int = Field(
        5, 
        description="RAG搜索时返回的最大结果数量，直接读取时的最大行数，对于直接读取，建议值：快速预览用20-50行，详细阅读用100-200行。对于RAG搜索，建议值：简单查询用3-5个，复杂查询用8-10个，详细分析用15-20个。"
    )

@tool(args_schema=SmartDocumentSearchSchema)
async def smart_document_search(
    file_paths: List[str], 
    query: str, 
    describe_image: bool = False,
    size_threshold_mb: Optional[float] = None,
    max_results: int = 5
) -> str:
    """
    智能文档搜索工具：根据查询类型和文件特征自动选择最适合的搜索策略。
    
    🎯 智能策略选择：
    - 📍 位置查询/快速查阅 → 直接读取（秒级响应）
    - 🧠 语义分析/复杂查询 → RAG搜索（智能理解）
    - 📚 多文档处理 → 自动优化策略
    
    💡 智能阈值：系统根据查询类型自动优化，也可手动指定特殊阈值

    如果你觉得信息不足，可以再次调用这个工具，扩大搜索范围或使用不同的查询内容。
    返回的内容仍需要你进行验证和判断。
    """
    try:
        # 确保file_paths是列表
        if isinstance(file_paths, str):
            file_paths = [file_paths]
        
        # 智能选择策略（传递可选的阈值参数）
        strategy = choose_document_strategy(query, file_paths, size_threshold_mb)
        
        if strategy == "direct_read":
            # 使用直接读取策略
            logger.info(f"使用直接读取策略处理查询: {query}")
            
            # 解析查询中的位置信息
            start_line = 1
            line_count = max_results * 150  # 直接读取时显示更多行
            search_keywords = None
            
            # 简单的关键词提取
            if any(keyword in query for keyword in ['搜索', '查找', '找']):
                # 提取可能的关键词
                import re
                # 提取引号内的内容作为关键词
                quoted_keywords = re.findall(r'[""\'](.*?)[""\'"]', query)
                if quoted_keywords:
                    search_keywords = quoted_keywords
                else:
                    # 提取常见的搜索词
                    words = query.split()
                    search_keywords = [word for word in words if len(word) > 1 and word not in ['搜索', '查找', '找', '内容', '文档']][:3]
            
            return await read_document_directly.ainvoke(input={
                "file_paths": file_paths,
                "start_line": start_line,
                "line_count": line_count,
                "search_keywords": search_keywords,
                "describe_image": describe_image
            })
        
        else:
            # 使用RAG搜索策略
            logger.info(f"使用RAG搜索策略处理查询: {query}")
            return await search_document_with_rag.ainvoke(input={
                "file_paths": file_paths,
                "query": query,
                "describe_image": describe_image,
                "k": max_results
            })
    
    except Exception as e:
        logger.error(f"智能文档搜索失败: {e}")
        return f"智能文档搜索失败: {str(e)}"


# ================== 长期记忆管理工具 ==================

class SearchLongTermMemorySchema(BaseModel):
    """搜索长期记忆的参数模式"""
    query: str = Field(..., description="搜索查询内容，用于检索相关的历史对话记忆")
    limit: Optional[int] = Field(default=5, description="返回记忆的最大数量，默认5条")
    threshold: Optional[float] = Field(default=0.8, description="相关性阈值，越高越严格，默认0.8")
    chat_type: Optional[int] = Field(default=None, description="对话类型过滤：0普通问答，1文件问答，2知识库问答")

@tool(args_schema=SearchLongTermMemorySchema)
async def search_long_term_memory(query: str, limit: int = 5, threshold: float = 0.8, chat_type: Optional[int] = None, api_key: str = "", user_name: str = "") -> str:
    """
    搜索用户的长期记忆，获取相关的历史对话信息。
    这个工具可以帮助我们了解用户的偏好、之前讨论过的话题和重要信息。
    """
    try:
        from .long_term_memory import get_memory_manager
        from .context_utils import get_current_user_context
        
        # 获取当前用户上下文（如果可用）
        user_context = get_current_user_context()
        if not user_context or not user_context.get("userid"):
            return "无法获取用户信息，无法搜索长期记忆"
        
        userid = user_context["userid"]
        qa_id = user_context.get("qa_id", "")
        
        memory_manager = get_memory_manager()
        if not memory_manager.config.enabled:
            return "长期记忆功能未启用"
        
        # 搜索相关记忆
        memories = await memory_manager.search_memories(
            query=query,
            userid=userid,
            qa_id=qa_id,
            agent_id="",  # 添加agent_id参数
            limit=limit,
            threshold=threshold,
            chat_type=chat_type,
            api_key=api_key,
            username=user_name
        )
        
        if not memories:
            return "没有找到相关的历史记忆"
        
        # 格式化记忆信息
        formatted_memories = []
        for i, memory in enumerate(memories, 1):
            chat_type_name = memory_manager.config.chat_types.get(
                memory.metadata.get("chat_type", 0), 
                "未知类型"
            )
            
            formatted_memories.append(
                f"记忆 {i}:\n"
                f"内容: {memory.memory}\n"
                f"类型: {chat_type_name}\n"
                f"相关性: {memory.score:.3f}\n"
                f"时间: {memory.created_at[:19] if memory.created_at else '未知'}\n"
            )
        
        result = f"找到 {len(memories)} 条相关记忆:\n\n" + "\n".join(formatted_memories)
        logger.info(f"搜索长期记忆成功，返回 {len(memories)} 条记忆")
        return result
        
    except Exception as e:
        logger.error(f"搜索长期记忆失败: {e}")
        return f"搜索长期记忆失败: {str(e)}"


class StoreLongTermMemorySchema(BaseModel):
    """存储长期记忆的参数模式"""
    info: str = Field(..., description="要存储的重要信息或知识")
    chat_type: Optional[int] = Field(default=0, description="对话类型：0普通问答，1文件问答，2知识库问答")

@tool(args_schema=StoreLongTermMemorySchema)
async def store_long_term_memory(info: str, chat_type: int = 0, api_key: str = "", user_name: str = "") -> str:
    """
    存储重要信息到长期记忆中。
    当发现用户提供了重要的个人信息、偏好或需要记住的内容时，使用此工具存储。
    """
    try:
        from .long_term_memory import get_memory_manager
        from .context_utils import get_current_user_context
        
        # 获取当前用户上下文
        user_context = get_current_user_context()
        if not user_context or not user_context.get("userid"):
            return "无法获取用户信息，无法存储长期记忆"
        
        userid = user_context["userid"]
        qa_id = user_context.get("qa_id", "")
        
        memory_manager = get_memory_manager()
        if not memory_manager.config.enabled:
            return "长期记忆功能未启用，无法存储"
        
        # 构建要存储的消息
        messages = [
            {"role": "user", "content": f"重要信息: {info}"},
            {"role": "assistant", "content": "我已经记录了这个重要信息，会在将来的对话中考虑它。"}
        ]
        
        # 存储记忆
        stored_memories = await memory_manager.store_conversation(
            userid=userid,
            qa_id=qa_id,
            messages=messages,
            api_key=api_key,
            username=user_name,
            agent_id="",  # 添加agent_id参数
            chat_type=chat_type
        )
        
        if stored_memories:
            logger.info(f"成功存储长期记忆: {info[:50]}...")
            return f"已成功存储重要信息到长期记忆中。存储了 {len(stored_memories)} 条记忆。"
        else:
            return "存储长期记忆失败，请稍后重试"
        
    except Exception as e:
        logger.error(f"存储长期记忆失败: {e}")
        return f"存储长期记忆失败: {str(e)}"


class GetUserMemoriesSchema(BaseModel):
    """获取用户记忆的参数模式"""
    chat_type: Optional[int] = Field(default=None, description="对话类型过滤：0普通问答，1文件问答，2知识库问答")

@tool(args_schema=GetUserMemoriesSchema)
async def get_user_memories(chat_type: Optional[int] = None, api_key: str = "", user_name: str = "") -> str:
    """
    获取当前用户的所有记忆信息。
    用于了解用户的完整对话历史和重要信息。
    """
    try:
        from .long_term_memory import get_memory_manager
        from .context_utils import get_current_user_context
        
        # 获取当前用户上下文
        user_context = get_current_user_context()
        if not user_context or not user_context.get("userid"):
            return "无法获取用户信息"
        
        userid = user_context["userid"]
        qa_id = user_context.get("qa_id", "")
        
        memory_manager = get_memory_manager()
        if not memory_manager.config.enabled:
            return "长期记忆功能未启用"
        
        # 获取用户所有记忆
        memories = await memory_manager.get_session_memories(
            userid=userid,
            qa_id=qa_id,
            agent_id="",  # 添加agent_id参数
            chat_type=chat_type,
            api_key=api_key,
            username=user_name
        )
        
        if not memories:
            return "用户暂无长期记忆记录"
        
        # 按类型统计记忆
        memory_stats = defaultdict(int)
        recent_memories = []
        
        for memory in memories:
            chat_type_val = memory.metadata.get("chat_type", 0)
            memory_stats[chat_type_val] += 1
            
            # 收集最近的记忆（最多5条）
            if len(recent_memories) < 5:
                recent_memories.append(memory)
        
        # 格式化统计信息
        stats_text = "记忆统计:\n"
        for ct, count in memory_stats.items():
            type_name = memory_manager.config.chat_types.get(ct, "未知类型")
            stats_text += f"- {type_name}: {count}条\n"
        
        # 格式化最近记忆
        recent_text = "\n最近记忆:\n"
        for i, memory in enumerate(recent_memories, 1):
            recent_text += f"{i}. {memory.memory[:100]}{'...' if len(memory.memory) > 100 else ''}\n"
        
        result = f"用户共有 {len(memories)} 条长期记忆\n\n{stats_text}{recent_text}"
        logger.info(f"获取用户记忆成功，共 {len(memories)} 条")
        return result
        
    except Exception as e:
        logger.error(f"获取用户记忆失败: {e}")
        return f"获取用户记忆失败: {str(e)}"

# ================== 测试代码 ==================

if __name__ == "__main__":
    # 测试不同策略的效果
    pdf_path = "/Volumes/Extreme SSD/sx_ai/sx-ai-rag-chat/rag_chat/temp/AI服务接口文档.pdf"
    
    # 测试直接读取
    print("=== 测试直接读取 ===")
    result1 = asyncio.run(read_document_directly.ainvoke(input={
        "file_paths": [pdf_path],
        "start_line": 1,
        "line_count": 20
    }))
    print(result1[:500] + "...")
    
    # 测试智能搜索
    print("\n=== 测试智能搜索 ===")
    result2 = asyncio.run(smart_document_search.ainvoke(input={
        "file_paths": [pdf_path],
        "query": "API接口文档的主要内容",
        "size_threshold_mb": 1.0  # 降低阈值测试RAG策略
    }))
    print(result2[:500] + "...")
